using UnityEngine;
using Cinemachine;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Camera controller that smoothly follows the player during tutorial gameplay
    /// Uses Cinemachine for professional camera behavior
    /// </summary>
    public class CameraFollowController : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private bool enableCameraFollow = true;
        [SerializeField] private Transform playerTarget;
        [SerializeField] private CinemachineVirtualCamera virtualCamera;
        
        [Header("Isometric Camera Settings")]
        [SerializeField] private Vector3 cameraOffset = new Vector3(0, 15, -15);
        [SerializeField] private float cameraAngle = 30f;
        [SerializeField] private float orthographicSize = 10f;
        [SerializeField] private bool useOrthographic = true;
        
        [Header("Follow Settings")]
        [SerializeField] private float followSmoothness = 2f;
        [SerializeField] private float lookAheadDistance = 3f;
        [SerializeField] private bool enableLookAhead = false; // Disable for better centering

        [Head<PERSON>("Camera Rotation Controls")]
        [SerializeField] private bool enableCameraRotation = true;
        [SerializeField] private float rotationSpeed = 100f;
        [SerializeField] private KeyCode rotateLeftKey = KeyCode.Q;
        [SerializeField] private KeyCode rotateRightKey = KeyCode.E;
        [SerializeField] private bool useMouseRotation = true;
        [SerializeField] private float mouseRotationSpeed = 2f;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        private Camera mainCamera;
        private Vector3 lastPlayerPosition;
        private Vector3 targetPosition;
        private float currentRotationY = 0f;
        private bool isRotatingWithMouse = false;
        
        private void Awake()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
        }
        
        private void Start()
        {
            InitializeCameraSystem();
        }
        
        private void InitializeCameraSystem()
        {
            // Find player if not assigned
            if (playerTarget == null)
            {
                var playerController = FindObjectOfType<AshesOfTheGrove.Player.PlayerController>();
                if (playerController != null)
                {
                    playerTarget = playerController.transform;
                }
            }

            if (playerTarget == null)
            {
                Debug.LogWarning("[CameraFollowController] No player target found. Camera follow will not work.");
                enableCameraFollow = false;
                return;
            }

            // Setup main camera for direct following (no Cinemachine complexity)
            SetupMainCamera();

            // Initialize positions and rotation
            if (playerTarget != null)
            {
                lastPlayerPosition = playerTarget.position;
                targetPosition = CalculateTargetPosition();
                currentRotationY = 0f; // Start facing north
            }

            Debug.Log("[CameraFollowController] Direct camera follow system initialized");
            Debug.Log($"[CameraFollowController] Camera rotation controls: {rotateLeftKey}/{rotateRightKey} keys, Mouse: {useMouseRotation}");
        }
        
        // Removed complex Cinemachine setup - using direct camera control instead
        
        private void SetupMainCamera()
        {
            if (mainCamera == null) return;
            
            // Configure main camera for isometric view
            if (useOrthographic)
            {
                mainCamera.orthographic = true;
                mainCamera.orthographicSize = orthographicSize;
            }
            else
            {
                mainCamera.orthographic = false;
                mainCamera.fieldOfView = 60f;
            }
            
            // Set initial position if no virtual camera
            if (virtualCamera == null && playerTarget != null)
            {
                mainCamera.transform.position = playerTarget.position + cameraOffset;
                mainCamera.transform.rotation = Quaternion.Euler(cameraAngle, 0, 0);
            }
        }
        
        private void LateUpdate()
        {
            if (!enableCameraFollow || playerTarget == null || mainCamera == null) return;

            // Handle camera rotation input (separate from player movement)
            HandleCameraRotationInput();

            // Update camera position to follow player (always center on player)
            UpdateCameraFollow();
        }
        
        private void HandleCameraRotationInput()
        {
            if (!enableCameraRotation) return;

            float rotationInput = 0f;

            // Keyboard rotation input (Q/E keys)
            if (Input.GetKey(rotateLeftKey))
            {
                rotationInput -= rotationSpeed * Time.deltaTime;
            }
            if (Input.GetKey(rotateRightKey))
            {
                rotationInput += rotationSpeed * Time.deltaTime;
            }

            // Mouse rotation input (hold right mouse button and drag)
            if (useMouseRotation)
            {
                if (Input.GetMouseButtonDown(1)) // Right mouse button pressed
                {
                    isRotatingWithMouse = true;
                }
                else if (Input.GetMouseButtonUp(1)) // Right mouse button released
                {
                    isRotatingWithMouse = false;
                }

                if (isRotatingWithMouse)
                {
                    float mouseX = Input.GetAxis("Mouse X");
                    rotationInput += mouseX * mouseRotationSpeed;
                }
            }

            // Apply rotation
            if (Mathf.Abs(rotationInput) > 0.01f)
            {
                currentRotationY += rotationInput;
                // Keep rotation in 0-360 range
                currentRotationY = currentRotationY % 360f;
                if (currentRotationY < 0) currentRotationY += 360f;

                Debug.Log($"[CameraFollowController] Camera rotation: {currentRotationY:F1}°");
            }
        }

        private void UpdateCameraFollow()
        {
            if (mainCamera == null || playerTarget == null) return;

            // Always center camera on player
            Vector3 playerPosition = playerTarget.position;

            // Calculate camera offset based on current rotation
            Vector3 rotatedOffset = Quaternion.Euler(0, currentRotationY, 0) * cameraOffset;
            Vector3 targetCameraPosition = playerPosition + rotatedOffset;

            // Smooth camera movement
            Vector3 currentPosition = mainCamera.transform.position;
            Vector3 newPosition = Vector3.Lerp(currentPosition, targetCameraPosition, followSmoothness * Time.deltaTime);
            mainCamera.transform.position = newPosition;

            // Camera always looks at player (centered)
            Vector3 lookDirection = (playerPosition - newPosition).normalized;
            if (lookDirection != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
                mainCamera.transform.rotation = Quaternion.Lerp(mainCamera.transform.rotation, targetRotation, followSmoothness * Time.deltaTime);
            }

            // Update last position for next frame
            lastPlayerPosition = playerPosition;
        }
        
        private Vector3 CalculateTargetPosition()
        {
            Vector3 basePosition = playerTarget.position + cameraOffset;
            
            if (enableLookAhead && playerTarget != null)
            {
                // Add look-ahead based on player movement
                Vector3 playerVelocity = (playerTarget.position - lastPlayerPosition) / Time.deltaTime;
                Vector3 lookAhead = playerVelocity.normalized * lookAheadDistance;
                basePosition += new Vector3(lookAhead.x, 0, lookAhead.z);
            }
            
            return basePosition;
        }
        
        // Public methods for tutorial system integration
        public void SetFollowTarget(Transform target)
        {
            playerTarget = target;
            if (virtualCamera != null)
            {
                virtualCamera.Follow = target;
                virtualCamera.LookAt = target;
            }
        }
        
        public void EnableCameraFollow(bool enable)
        {
            enableCameraFollow = enable;
            if (virtualCamera != null)
            {
                virtualCamera.gameObject.SetActive(enable);
            }

            Debug.Log($"[CameraFollowController] Camera follow {(enable ? "enabled" : "disabled")}");
        }

        public void DisableCameraSystem()
        {
            enableCameraFollow = false;
            if (virtualCamera != null)
            {
                virtualCamera.gameObject.SetActive(false);
            }

            // Reset main camera to manual control
            if (mainCamera != null)
            {
                var brain = mainCamera.GetComponent<CinemachineBrain>();
                if (brain != null)
                {
                    brain.enabled = false;
                }
            }

            Debug.Log("[CameraFollowController] Camera system completely disabled");
        }
        
        public void SetCameraDistance(float distance)
        {
            cameraOffset = cameraOffset.normalized * distance;
            if (virtualCamera != null)
            {
                var transposer = virtualCamera.GetCinemachineComponent<CinemachineTransposer>();
                if (transposer != null)
                {
                    transposer.m_FollowOffset = cameraOffset;
                }
            }
        }
        
        public void SetOrthographicSize(float size)
        {
            orthographicSize = size;
            if (mainCamera != null && mainCamera.orthographic)
            {
                mainCamera.orthographicSize = size;
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showDebugInfo || playerTarget == null) return;
            
            // Draw camera target position
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(targetPosition, 1f);
            
            // Draw connection line
            Gizmos.color = Color.green;
            Gizmos.DrawLine(playerTarget.position, targetPosition);
            
            // Draw camera offset
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(playerTarget.position + cameraOffset, Vector3.one);
        }
    }
}

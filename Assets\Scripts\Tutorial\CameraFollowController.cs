using UnityEngine;
using Cinemachine;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Camera controller that smoothly follows the player during tutorial gameplay
    /// Uses Cinemachine for professional camera behavior
    /// </summary>
    public class CameraFollowController : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private bool enableCameraFollow = true;
        [SerializeField] private Transform playerTarget;
        [SerializeField] private CinemachineVirtualCamera virtualCamera;
        
        [<PERSON><PERSON>("Isometric Camera Settings")]
        [SerializeField] private Vector3 cameraOffset = new Vector3(0, 15, -15);
        [SerializeField] private float cameraAngle = 30f;
        [SerializeField] private float orthographicSize = 10f;
        [SerializeField] private bool useOrthographic = true;
        
        [Header("Follow Settings")]
        [SerializeField] private float followSmoothness = 2f;
        [SerializeField] private float lookAheadDistance = 3f;
        [SerializeField] private bool enableLookAhead = true;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        private Camera mainCamera;
        private Vector3 lastPlayerPosition;
        private Vector3 targetPosition;
        
        private void Awake()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
        }
        
        private void Start()
        {
            InitializeCameraSystem();
        }
        
        private void InitializeCameraSystem()
        {
            // Find player if not assigned
            if (playerTarget == null)
            {
                var playerController = FindObjectOfType<AshesOfTheGrove.Player.PlayerController>();
                if (playerController != null)
                {
                    playerTarget = playerController.transform;
                }
            }
            
            if (playerTarget == null)
            {
                Debug.LogWarning("[CameraFollowController] No player target found. Camera follow will not work.");
                enableCameraFollow = false;
                return;
            }
            
            // Setup Cinemachine Virtual Camera if available
            SetupVirtualCamera();
            
            // Setup main camera for isometric view
            SetupMainCamera();
            
            // Initialize positions
            if (playerTarget != null)
            {
                lastPlayerPosition = playerTarget.position;
                targetPosition = CalculateTargetPosition();
            }
            
            Debug.Log("[CameraFollowController] Camera follow system initialized");
        }
        
        private void SetupVirtualCamera()
        {
            // Try to find existing virtual camera
            if (virtualCamera == null)
            {
                virtualCamera = FindObjectOfType<CinemachineVirtualCamera>();
            }
            
            // Create virtual camera if none exists
            if (virtualCamera == null)
            {
                GameObject vcamObject = new GameObject("Tutorial_VirtualCamera");
                virtualCamera = vcamObject.AddComponent<CinemachineVirtualCamera>();
                
                // Set high priority to ensure it's active
                virtualCamera.Priority = 10;
            }
            
            if (virtualCamera != null && playerTarget != null)
            {
                // Configure virtual camera to follow player
                virtualCamera.Follow = playerTarget;
                virtualCamera.LookAt = playerTarget;
                
                // Add Cinemachine Brain to main camera if not present
                if (mainCamera != null && mainCamera.GetComponent<CinemachineBrain>() == null)
                {
                    var brain = mainCamera.gameObject.AddComponent<CinemachineBrain>();
                    brain.m_DefaultBlend.m_Time = 1f; // Smooth transitions
                }
                
                // Configure for isometric view
                ConfigureVirtualCameraForIsometric();
                
                Debug.Log("[CameraFollowController] Cinemachine Virtual Camera configured");
            }
        }
        
        private void ConfigureVirtualCameraForIsometric()
        {
            if (virtualCamera == null) return;
            
            // Position the virtual camera for isometric view
            virtualCamera.transform.position = playerTarget.position + cameraOffset;
            virtualCamera.transform.rotation = Quaternion.Euler(cameraAngle, 0, 0);
            
            // Add and configure Transposer for smooth following
            var transposer = virtualCamera.AddCinemachineComponent<CinemachineTransposer>();
            transposer.m_FollowOffset = cameraOffset;
            transposer.m_XDamping = followSmoothness;
            transposer.m_YDamping = followSmoothness;
            transposer.m_ZDamping = followSmoothness;
            
            // Add composer for look-at behavior
            var composer = virtualCamera.AddCinemachineComponent<CinemachineComposer>();
            composer.m_TrackedObjectOffset = Vector3.zero;
            composer.m_LookaheadTime = enableLookAhead ? 0.5f : 0f;
            composer.m_LookaheadSmoothing = 10f;
        }
        
        private void SetupMainCamera()
        {
            if (mainCamera == null) return;
            
            // Configure main camera for isometric view
            if (useOrthographic)
            {
                mainCamera.orthographic = true;
                mainCamera.orthographicSize = orthographicSize;
            }
            else
            {
                mainCamera.orthographic = false;
                mainCamera.fieldOfView = 60f;
            }
            
            // Set initial position if no virtual camera
            if (virtualCamera == null && playerTarget != null)
            {
                mainCamera.transform.position = playerTarget.position + cameraOffset;
                mainCamera.transform.rotation = Quaternion.Euler(cameraAngle, 0, 0);
            }
        }
        
        private void LateUpdate()
        {
            if (!enableCameraFollow || playerTarget == null) return;
            
            // If using Cinemachine, let it handle the following
            if (virtualCamera != null && virtualCamera.gameObject.activeInHierarchy)
            {
                return;
            }
            
            // Manual camera following as fallback
            UpdateManualCameraFollow();
        }
        
        private void UpdateManualCameraFollow()
        {
            if (mainCamera == null || playerTarget == null) return;
            
            // Calculate target position
            Vector3 newTargetPosition = CalculateTargetPosition();
            
            // Smooth camera movement
            targetPosition = Vector3.Lerp(targetPosition, newTargetPosition, followSmoothness * Time.deltaTime);
            mainCamera.transform.position = targetPosition;
            
            // Update last position
            lastPlayerPosition = playerTarget.position;
        }
        
        private Vector3 CalculateTargetPosition()
        {
            Vector3 basePosition = playerTarget.position + cameraOffset;
            
            if (enableLookAhead && playerTarget != null)
            {
                // Add look-ahead based on player movement
                Vector3 playerVelocity = (playerTarget.position - lastPlayerPosition) / Time.deltaTime;
                Vector3 lookAhead = playerVelocity.normalized * lookAheadDistance;
                basePosition += new Vector3(lookAhead.x, 0, lookAhead.z);
            }
            
            return basePosition;
        }
        
        // Public methods for tutorial system integration
        public void SetFollowTarget(Transform target)
        {
            playerTarget = target;
            if (virtualCamera != null)
            {
                virtualCamera.Follow = target;
                virtualCamera.LookAt = target;
            }
        }
        
        public void EnableCameraFollow(bool enable)
        {
            enableCameraFollow = enable;
            if (virtualCamera != null)
            {
                virtualCamera.gameObject.SetActive(enable);
            }
        }
        
        public void SetCameraDistance(float distance)
        {
            cameraOffset = cameraOffset.normalized * distance;
            if (virtualCamera != null)
            {
                var transposer = virtualCamera.GetCinemachineComponent<CinemachineTransposer>();
                if (transposer != null)
                {
                    transposer.m_FollowOffset = cameraOffset;
                }
            }
        }
        
        public void SetOrthographicSize(float size)
        {
            orthographicSize = size;
            if (mainCamera != null && mainCamera.orthographic)
            {
                mainCamera.orthographicSize = size;
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showDebugInfo || playerTarget == null) return;
            
            // Draw camera target position
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(targetPosition, 1f);
            
            // Draw connection line
            Gizmos.color = Color.green;
            Gizmos.DrawLine(playerTarget.position, targetPosition);
            
            // Draw camera offset
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(playerTarget.position + cameraOffset, Vector3.one);
        }
    }
}

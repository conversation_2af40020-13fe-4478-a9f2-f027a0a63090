using UnityEngine;
using System.Collections.Generic;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Defines the layout and structure of the tutorial scene
    /// Contains all the areas and their specific learning objectives
    /// </summary>
    [System.Serializable]
    public class TutorialArea
    {
        public string areaName;
        public string objective;
        public Vector3 playerStartPosition;
        public Vector3 cameraPosition;
        public List<string> instructions;
        public bool isCompleted;
    }

    public class TutorialSceneLayout : MonoBehaviour
    {
        [Header("Tutorial Areas")]
        [SerializeField] private List<TutorialArea> tutorialAreas = new List<TutorialArea>();
        
        [Header("Scene References")]
        [SerializeField] private Transform playerSpawn;
        [SerializeField] private Transform[] enemySpawns;
        [SerializeField] private Transform[] patrolPoints;
        [SerializeField] private Transform[] hidingSpots;
        
        private void Awake()
        {
            InitializeTutorialAreas();
        }
        
        private void InitializeTutorialAreas()
        {
            tutorialAreas.Clear();
            
            // Area 1: Basic Movement
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Basic Movement",
                objective = "Learn to move and control your character",
                playerStartPosition = new Vector3(0, 1, -10),
                cameraPosition = new Vector3(0, 8, -15),
                instructions = new List<string>
                {
                    "Welcome to Ashes of the Grove!",
                    "Use WASD or Arrow Keys to move",
                    "Your character will face the direction you're moving",
                    "Move to the green marker to continue"
                }
            });
            
            // Area 2: Stealth Basics
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Stealth Mechanics",
                objective = "Learn crouching and movement speed control",
                playerStartPosition = new Vector3(0, 1, -5),
                cameraPosition = new Vector3(0, 8, -10),
                instructions = new List<string>
                {
                    "Stealth is key to survival",
                    "Press Left Ctrl to toggle crouch",
                    "Crouching makes you quieter but slower",
                    "Hold Left Shift to run (but you'll be louder)",
                    "Practice different movement speeds"
                }
            });
            
            // Area 3: Enemy Detection
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Enemy Detection",
                objective = "Understand how enemies detect you",
                playerStartPosition = new Vector3(-8, 1, 0),
                cameraPosition = new Vector3(-8, 10, -5),
                instructions = new List<string>
                {
                    "Enemies have vision cones (shown in red)",
                    "Stay out of their sight to avoid detection",
                    "Moving closer increases detection risk",
                    "Crouching reduces your detection profile",
                    "Observe the guard's patrol pattern"
                }
            });
            
            // Area 4: Using Cover
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Using Cover",
                objective = "Learn to use environmental cover",
                playerStartPosition = new Vector3(-8, 1, 5),
                cameraPosition = new Vector3(-8, 10, 0),
                instructions = new List<string>
                {
                    "Use walls and objects to break line of sight",
                    "Hide behind cover when enemies approach",
                    "Wait for the right moment to move",
                    "Reach the hiding spot without being seen"
                }
            });
            
            // Area 5: Patrol Patterns
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Patrol Patterns",
                objective = "Learn to read and exploit enemy patterns",
                playerStartPosition = new Vector3(0, 1, 5),
                cameraPosition = new Vector3(0, 12, 0),
                instructions = new List<string>
                {
                    "Enemies follow predictable patrol routes",
                    "Watch their movement patterns carefully",
                    "Time your movement when they turn away",
                    "Navigate past both guards to the exit"
                }
            });
            
            // Area 6: Alert States
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Alert System",
                objective = "Understand enemy alert states",
                playerStartPosition = new Vector3(8, 1, 0),
                cameraPosition = new Vector3(8, 10, -5),
                instructions = new List<string>
                {
                    "Enemies have different alert levels:",
                    "GREEN: Normal patrol",
                    "YELLOW: Suspicious, investigating",
                    "RED: Alert, actively searching",
                    "Avoid triggering high alert states"
                }
            });
            
            // Area 7: Final Challenge
            tutorialAreas.Add(new TutorialArea
            {
                areaName = "Final Challenge",
                objective = "Combine all skills to reach the objective",
                playerStartPosition = new Vector3(8, 1, -8),
                cameraPosition = new Vector3(8, 15, -15),
                instructions = new List<string>
                {
                    "Final Test: Infiltrate the compound",
                    "Use all the skills you've learned",
                    "Avoid multiple guards with overlapping patrols",
                    "Reach the objective marker to complete the tutorial"
                }
            });
        }
        
        public List<TutorialArea> GetTutorialAreas()
        {
            return tutorialAreas;
        }
        
        public TutorialArea GetArea(int index)
        {
            if (index >= 0 && index < tutorialAreas.Count)
                return tutorialAreas[index];
            return null;
        }
        
        public int GetAreaCount()
        {
            return tutorialAreas.Count;
        }
        
        public void CompleteArea(int index)
        {
            if (index >= 0 && index < tutorialAreas.Count)
            {
                tutorialAreas[index].isCompleted = true;
            }
        }
        
        // Gizmos for scene visualization
        private void OnDrawGizmos()
        {
            if (tutorialAreas == null) return;
            
            for (int i = 0; i < tutorialAreas.Count; i++)
            {
                var area = tutorialAreas[i];
                
                // Draw player start position
                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(area.playerStartPosition, 0.5f);
                
                // Draw camera position
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(area.cameraPosition, Vector3.one);
                
                // Draw connection line
                Gizmos.color = Color.gray;
                Gizmos.DrawLine(area.playerStartPosition, area.cameraPosition);
            }
            
            // Draw patrol points
            if (patrolPoints != null)
            {
                Gizmos.color = Color.red;
                for (int i = 0; i < patrolPoints.Length; i++)
                {
                    if (patrolPoints[i] != null)
                    {
                        Gizmos.DrawWireSphere(patrolPoints[i].position, 0.3f);
                        
                        // Draw patrol route
                        if (i < patrolPoints.Length - 1 && patrolPoints[i + 1] != null)
                        {
                            Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[i + 1].position);
                        }
                    }
                }
            }
            
            // Draw hiding spots
            if (hidingSpots != null)
            {
                Gizmos.color = Color.green;
                foreach (var spot in hidingSpots)
                {
                    if (spot != null)
                    {
                        Gizmos.DrawWireCube(spot.position, new Vector3(1, 2, 1));
                    }
                }
            }
        }
    }
}

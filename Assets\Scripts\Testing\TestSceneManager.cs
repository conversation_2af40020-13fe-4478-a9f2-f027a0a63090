using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing
{
    /// <summary>
    /// Manages the test scene and provides easy setup for testing PlayerControllerModular
    /// </summary>
    public class TestSceneManager : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool enableDebugLogging = true;
        
        [Header("References")]
        [SerializeField] private PlayerControllerModular playerController;
        [SerializeField] private InputTester inputTester;
        [SerializeField] private TestInstructions testInstructions;
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupTestScene();
            }
        }
        
        [ContextMenu("Setup Test Scene")]
        public void SetupTestScene()
        {
            Debug.Log("[TestSceneManager] Setting up test scene...");
            
            // Create or find player
            SetupPlayer();
            
            // Setup test components
            SetupTestComponents();
            
            // Setup camera
            SetupCamera();
            
            // Create test environment
            CreateTestEnvironment();
            
            // Final setup
            FinalizeSetup();
            
            Debug.Log("[TestSceneManager] Test scene setup complete!");
            LogTestInstructions();
        }
        
        private void SetupPlayer()
        {
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            
            if (playerObj == null)
            {
                // Create new player
                playerObj = new GameObject("Player");
                playerObj.tag = "Player";
                playerObj.layer = 8; // Player layer
                
                // Add required components
                Rigidbody rb = playerObj.AddComponent<Rigidbody>();
                rb.mass = 1f;
                rb.drag = 5f;
                rb.freezeRotation = true;
                
                CapsuleCollider col = playerObj.AddComponent<CapsuleCollider>();
                col.height = 2f;
                col.radius = 0.5f;
                col.center = new Vector3(0, 1f, 0);
                
                playerController = playerObj.AddComponent<PlayerControllerModular>();
                
                // Create visual representation
                CreatePlayerVisual(playerObj);
                
                Debug.Log("[TestSceneManager] Player created and configured.");
            }
            else
            {
                playerController = playerObj.GetComponent<PlayerControllerModular>();
                if (playerController == null)
                {
                    playerController = playerObj.AddComponent<PlayerControllerModular>();
                }
                Debug.Log("[TestSceneManager] Existing player found and configured.");
            }
        }
        
        private void CreatePlayerVisual(GameObject player)
        {
            GameObject visual = new GameObject("Visual");
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = Vector3.zero;
            
            // Body
            GameObject body = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            body.name = "Body";
            body.transform.SetParent(visual.transform);
            body.transform.localPosition = new Vector3(0, 1f, 0);
            Destroy(body.GetComponent<CapsuleCollider>());
            
            // Direction indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cube);
            indicator.name = "Direction";
            indicator.transform.SetParent(visual.transform);
            indicator.transform.localPosition = new Vector3(0, 1f, 0.6f);
            indicator.transform.localScale = new Vector3(0.2f, 0.2f, 0.2f);
            Destroy(indicator.GetComponent<BoxCollider>());
            
            // Materials
            Material bodyMat = new Material(Shader.Find("Standard"));
            bodyMat.color = Color.blue;
            body.GetComponent<Renderer>().material = bodyMat;
            
            Material indicatorMat = new Material(Shader.Find("Standard"));
            indicatorMat.color = Color.red;
            indicator.GetComponent<Renderer>().material = indicatorMat;
        }
        
        private void SetupTestComponents()
        {
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            
            // Add InputTester
            inputTester = playerObj.GetComponent<InputTester>();
            if (inputTester == null)
            {
                inputTester = playerObj.AddComponent<InputTester>();
            }
            
            // Add TestInstructions to scene
            GameObject instructionsObj = GameObject.Find("TestInstructions");
            if (instructionsObj == null)
            {
                instructionsObj = new GameObject("TestInstructions");
                testInstructions = instructionsObj.AddComponent<TestInstructions>();
            }
            
            Debug.Log("[TestSceneManager] Test components added.");
        }
        
        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera != null)
            {
                // Isometric camera setup
                mainCamera.transform.position = new Vector3(0, 10, -10);
                mainCamera.transform.rotation = Quaternion.Euler(30, 0, 0);
                mainCamera.orthographic = true;
                mainCamera.orthographicSize = 8;
                
                Debug.Log("[TestSceneManager] Camera configured for isometric view.");
            }
        }
        
        private void CreateTestEnvironment()
        {
            // Create ground
            GameObject ground = GameObject.Find("TestGround");
            if (ground == null)
            {
                ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
                ground.name = "TestGround";
                ground.transform.position = Vector3.zero;
                ground.transform.localScale = new Vector3(5, 1, 5);
                
                Material groundMat = new Material(Shader.Find("Standard"));
                groundMat.color = new Color(0.3f, 0.5f, 0.3f); // Dark green
                ground.GetComponent<Renderer>().material = groundMat;
            }
            
            // Create some test obstacles
            CreateTestObstacles();
            
            Debug.Log("[TestSceneManager] Test environment created.");
        }
        
        private void CreateTestObstacles()
        {
            // Create a few cubes as obstacles
            Vector3[] obstaclePositions = {
                new Vector3(5, 0.5f, 5),
                new Vector3(-5, 0.5f, 5),
                new Vector3(5, 0.5f, -5),
                new Vector3(-5, 0.5f, -5),
                new Vector3(0, 0.5f, 8)
            };
            
            for (int i = 0; i < obstaclePositions.Length; i++)
            {
                GameObject obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
                obstacle.name = $"TestObstacle_{i + 1}";
                obstacle.transform.position = obstaclePositions[i];
                obstacle.layer = 10; // Environment layer
                
                Material obstacleMat = new Material(Shader.Find("Standard"));
                obstacleMat.color = Color.gray;
                obstacle.GetComponent<Renderer>().material = obstacleMat;
            }
        }
        
        private void FinalizeSetup()
        {
            // Position player at origin
            if (playerController != null)
            {
                playerController.transform.position = new Vector3(0, 1, 0);
            }
            
            // Enable debug logging if requested
            if (enableDebugLogging)
            {
                Debug.Log("[TestSceneManager] Debug logging enabled.");
            }
        }
        
        private void LogTestInstructions()
        {
            Debug.Log("=== PLAYER INPUT TEST READY ===");
            Debug.Log("CONTROLS:");
            Debug.Log("• WASD or Arrow Keys: Move");
            Debug.Log("• Left Ctrl: Toggle Crouch");
            Debug.Log("• Left Shift: Hold to Run");
            Debug.Log("• F1: Toggle Debug Panel");
            Debug.Log("• F2: Toggle Instructions Panel");
            Debug.Log("");
            Debug.Log("TEST SCENARIOS:");
            Debug.Log("1. Basic movement in all directions");
            Debug.Log("2. Crouch toggle and movement");
            Debug.Log("3. Run (hold shift) and movement");
            Debug.Log("4. Combined actions (crouch+move, run+move)");
            Debug.Log("5. Rapid input changes");
            Debug.Log("");
            Debug.Log("Watch the debug panel (F1) for real-time input feedback!");
        }
        
        // Public methods for manual testing
        public void RunMovementTest()
        {
            if (inputTester != null)
            {
                inputTester.TestMovementInput();
            }
        }
        
        public void RunStealthTest()
        {
            if (inputTester != null)
            {
                inputTester.TestStealthInput();
            }
        }
        
        public void RunAllTests()
        {
            if (inputTester != null)
            {
                inputTester.TestAllInputs();
            }
        }
    }
}

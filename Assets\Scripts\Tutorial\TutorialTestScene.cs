using UnityEngine;
using AshesOfTheGrove.Tutorial;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Simple test scene setup for the new tutorial system
    /// Use this to quickly test the zone-based tutorial architecture
    /// </summary>
    public class TutorialTestScene : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool autoStartTutorial = true;
        
        [Header("Test Controls")]
        [SerializeField] private KeyCode restartKey = KeyCode.R;
        [SerializeField] private KeyCode nextZoneKey = KeyCode.N;
        [SerializeField] private KeyCode prevZoneKey = KeyCode.P;
        [SerializeField] private KeyCode toggleDebugKey = KeyCode.F1;
        
        private ModularTutorialSceneSetup sceneSetup;
        private TutorialZoneManager zoneManager;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupTestScene();
            }
        }
        
        private void Update()
        {
            HandleTestInput();
        }
        
        [ContextMenu("Setup Test Scene")]
        public void SetupTestScene()
        {
            Debug.Log("[TutorialTestScene] Setting up test scene...");
            
            // Get or create the scene setup component
            sceneSetup = GetComponent<ModularTutorialSceneSetup>();
            if (sceneSetup == null)
            {
                sceneSetup = gameObject.AddComponent<ModularTutorialSceneSetup>();
            }
            
            // Configure the scene setup
            ConfigureSceneSetup();
            
            // Setup the tutorial scene
            sceneSetup.SetupTutorialScene();
            
            // Get zone manager reference
            zoneManager = GetComponent<TutorialZoneManager>();
            
            Debug.Log("[TutorialTestScene] Test scene setup complete!");
            Debug.Log("[TutorialTestScene] Controls:");
            Debug.Log($"  {restartKey} - Restart Tutorial");
            Debug.Log($"  {nextZoneKey} - Next Zone");
            Debug.Log($"  {prevZoneKey} - Previous Zone");
            Debug.Log($"  {toggleDebugKey} - Toggle Debug Mode");
        }
        
        private void ConfigureSceneSetup()
        {
            // Use reflection to set private fields or modify the setup component
            // For now, we'll rely on the default configuration
            
            if (enableDebugMode)
            {
                sceneSetup.ToggleDebugMode();
            }
        }
        
        private void HandleTestInput()
        {
            if (Input.GetKeyDown(restartKey))
            {
                RestartTutorial();
            }
            
            if (Input.GetKeyDown(nextZoneKey))
            {
                NextZone();
            }
            
            if (Input.GetKeyDown(prevZoneKey))
            {
                PreviousZone();
            }
            
            if (Input.GetKeyDown(toggleDebugKey))
            {
                ToggleDebugMode();
            }
        }
        
        public void RestartTutorial()
        {
            if (sceneSetup != null)
            {
                sceneSetup.RestartTutorial();
                Debug.Log("[TutorialTestScene] Tutorial restarted");
            }
        }
        
        public void NextZone()
        {
            if (zoneManager != null)
            {
                int nextZone = zoneManager.CurrentZoneIndex + 1;
                if (nextZone < zoneManager.TotalZones)
                {
                    sceneSetup.SkipToZone(nextZone);
                    Debug.Log($"[TutorialTestScene] Skipped to zone {nextZone + 1}");
                }
                else
                {
                    Debug.Log("[TutorialTestScene] Already at last zone");
                }
            }
        }
        
        public void PreviousZone()
        {
            if (zoneManager != null)
            {
                int prevZone = zoneManager.CurrentZoneIndex - 1;
                if (prevZone >= 0)
                {
                    sceneSetup.SkipToZone(prevZone);
                    Debug.Log($"[TutorialTestScene] Skipped to zone {prevZone + 1}");
                }
                else
                {
                    Debug.Log("[TutorialTestScene] Already at first zone");
                }
            }
        }
        
        public void ToggleDebugMode()
        {
            enableDebugMode = !enableDebugMode;
            if (sceneSetup != null)
            {
                sceneSetup.ToggleDebugMode();
            }
            Debug.Log($"[TutorialTestScene] Debug mode: {enableDebugMode}");
        }
        
        // GUI for test controls
        private void OnGUI()
        {
            if (!enableDebugMode) return;
            
            GUILayout.BeginArea(new Rect(Screen.width - 320, 10, 300, 300));
            GUILayout.Label("Tutorial Test Controls", GUI.skin.box);
            
            if (GUILayout.Button($"Restart Tutorial ({restartKey})"))
            {
                RestartTutorial();
            }
            
            if (GUILayout.Button($"Previous Zone ({prevZoneKey})"))
            {
                PreviousZone();
            }
            
            if (GUILayout.Button($"Next Zone ({nextZoneKey})"))
            {
                NextZone();
            }
            
            if (GUILayout.Button($"Toggle Debug ({toggleDebugKey})"))
            {
                ToggleDebugMode();
            }
            
            GUILayout.Space(10);
            
            if (zoneManager != null)
            {
                GUILayout.Label($"Current Zone: {zoneManager.CurrentZoneIndex + 1}/{zoneManager.TotalZones}");
                GUILayout.Label($"Progress: {zoneManager.GetTutorialProgress() * 100:F0}%");
                
                // Zone selection buttons
                GUILayout.Label("Quick Zone Selection:");
                for (int i = 0; i < zoneManager.TotalZones; i++)
                {
                    if (GUILayout.Button($"Zone {i + 1}"))
                    {
                        sceneSetup.SkipToZone(i);
                    }
                }
            }
            
            GUILayout.EndArea();
        }
        
        // Validation methods
        [ContextMenu("Validate Tutorial System")]
        public void ValidateTutorialSystem()
        {
            Debug.Log("[TutorialTestScene] Validating tutorial system...");
            
            // Check for required components
            bool isValid = true;
            
            if (sceneSetup == null)
            {
                Debug.LogError("[TutorialTestScene] ModularTutorialSceneSetup component missing!");
                isValid = false;
            }
            
            if (zoneManager == null)
            {
                Debug.LogError("[TutorialTestScene] TutorialZoneManager component missing!");
                isValid = false;
            }
            
            // Check for player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                Debug.LogError("[TutorialTestScene] Player object not found!");
                isValid = false;
            }
            
            // Check for tutorial zones
            TutorialZone[] zones = FindObjectsOfType<TutorialZone>();
            if (zones.Length == 0)
            {
                Debug.LogError("[TutorialTestScene] No tutorial zones found!");
                isValid = false;
            }
            else
            {
                Debug.Log($"[TutorialTestScene] Found {zones.Length} tutorial zones");
            }
            
            if (isValid)
            {
                Debug.Log("[TutorialTestScene] Tutorial system validation passed!");
            }
            else
            {
                Debug.LogError("[TutorialTestScene] Tutorial system validation failed!");
            }
        }
        
        [ContextMenu("Clear Test Scene")]
        public void ClearTestScene()
        {
            if (sceneSetup != null)
            {
                sceneSetup.ClearTutorialScene();
                Debug.Log("[TutorialTestScene] Test scene cleared");
            }
        }
        
        // Helper method to show tutorial system info
        public void ShowSystemInfo()
        {
            Debug.Log("=== Tutorial System Info ===");
            Debug.Log($"Scene Setup: {(sceneSetup != null ? "✓" : "✗")}");
            Debug.Log($"Zone Manager: {(zoneManager != null ? "✓" : "✗")}");
            Debug.Log($"Player: {(GameObject.FindGameObjectWithTag("Player") != null ? "✓" : "✗")}");
            Debug.Log($"Tutorial Zones: {FindObjectsOfType<TutorialZone>().Length}");
            Debug.Log($"Debug Mode: {enableDebugMode}");
            Debug.Log("============================");
        }
    }
}

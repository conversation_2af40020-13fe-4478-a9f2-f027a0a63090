using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Interactive hiding spot that provides cover for the player
    /// Teaches stealth mechanics and line of sight breaking
    /// </summary>
    public class HidingSpot : MonoBehaviour
    {
        [Header("Hiding Spot Settings")]
        [SerializeField] private float hideRadius = 1.5f;
        [SerializeField] private bool requireCrouching = false;
        [SerializeField] private float hidingEffectiveness = 0.8f;
        
        [Header("Visual Feedback")]
        [SerializeField] private bool showHideRadius = true;
        [SerializeField] private Color hideRadiusColor = Color.green;
        [SerializeField] private GameObject hideIndicator;
        
        [Header("Tutorial")]
        [SerializeField] private string hideInstruction = "Press Ctrl to crouch and hide";
        [SerializeField] private bool showInstructions = true;
        
        private PlayerControllerModular player;
        private bool playerInRange = false;
        private bool playerHiding = false;
        private Material originalMaterial;
        private Renderer spotRenderer;
        
        private void Start()
        {
            player = FindObjectOfType<PlayerControllerModular>();
            spotRenderer = GetComponent<Renderer>();
            
            if (spotRenderer != null)
            {
                originalMaterial = spotRenderer.material;
            }
            
            // Create hide indicator if not assigned
            if (hideIndicator == null)
            {
                CreateHideIndicator();
            }
            
            UpdateVisualState();
        }
        
        private void Update()
        {
            CheckPlayerProximity();
            UpdateHidingState();
            UpdateVisualState();
        }
        
        private void CheckPlayerProximity()
        {
            if (player == null) return;
            
            float distance = Vector3.Distance(transform.position, player.transform.position);
            bool wasInRange = playerInRange;
            playerInRange = distance <= hideRadius;
            
            // Show instruction when player enters range
            if (playerInRange && !wasInRange && showInstructions)
            {
                ShowHideInstruction();
            }
        }
        
        private void UpdateHidingState()
        {
            if (!playerInRange)
            {
                playerHiding = false;
                return;
            }
            
            // Check if player meets hiding requirements
            bool canHide = true;
            
            if (requireCrouching && !player.IsCrouching())
            {
                canHide = false;
            }
            
            playerHiding = canHide;
            
            // Update player hiding state
            if (playerHiding)
            {
                // This would need to be implemented in PlayerController
                // player.SetHiding(true, hidingEffectiveness);
            }
        }
        
        private void UpdateVisualState()
        {
            if (spotRenderer == null || originalMaterial == null) return;
            
            Color targetColor = originalMaterial.color;
            
            if (playerHiding)
            {
                targetColor = Color.green;
            }
            else if (playerInRange)
            {
                targetColor = Color.yellow;
            }
            
            // Create new material with target color
            Material newMaterial = new Material(originalMaterial);
            newMaterial.color = targetColor;
            spotRenderer.material = newMaterial;
            
            // Update hide indicator
            if (hideIndicator != null)
            {
                hideIndicator.SetActive(playerInRange);
            }
        }
        
        private void CreateHideIndicator()
        {
            hideIndicator = new GameObject("HideIndicator");
            hideIndicator.transform.SetParent(transform);
            hideIndicator.transform.localPosition = Vector3.up * 2f;
            
            // Create a simple arrow or icon
            GameObject arrow = GameObject.CreatePrimitive(PrimitiveType.Cube);
            arrow.transform.SetParent(hideIndicator.transform);
            arrow.transform.localPosition = Vector3.zero;
            arrow.transform.localScale = new Vector3(0.2f, 0.5f, 0.2f);
            
            // Make it green and slightly transparent
            Material arrowMat = new Material(Shader.Find("Standard"));
            arrowMat.color = new Color(0, 1, 0, 0.7f);
            arrowMat.SetFloat("_Mode", 3); // Transparent mode
            arrowMat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            arrowMat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            arrowMat.SetInt("_ZWrite", 0);
            arrowMat.DisableKeyword("_ALPHATEST_ON");
            arrowMat.EnableKeyword("_ALPHABLEND_ON");
            arrowMat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            arrowMat.renderQueue = 3000;
            
            arrow.GetComponent<Renderer>().material = arrowMat;
            
            // Remove collider
            Destroy(arrow.GetComponent<Collider>());
            
            hideIndicator.SetActive(false);
        }
        
        private void ShowHideInstruction()
        {
            TutorialUI tutorialUI = FindObjectOfType<TutorialUI>();
            if (tutorialUI != null)
            {
                tutorialUI.ShowInstruction(hideInstruction);
            }
            else
            {
                Debug.Log($"[HidingSpot] {hideInstruction}");
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showHideRadius) return;
            
            // Draw hide radius
            Gizmos.color = hideRadiusColor;
            Gizmos.DrawWireSphere(transform.position, hideRadius);
            
            // Draw effectiveness indicator
            if (playerHiding)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(transform.position + Vector3.up * 2.5f, Vector3.one * 0.5f);
            }
            else if (playerInRange)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(transform.position + Vector3.up * 2.5f, Vector3.one * 0.3f);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw detailed information when selected
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, hideRadius);
            
            // Draw hiding effectiveness visualization
            if (Application.isPlaying && playerHiding)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawSphere(transform.position + Vector3.up * 3f, 0.2f);
            }
        }
        
        // Public methods for tutorial system
        public bool IsPlayerHiding()
        {
            return playerHiding;
        }
        
        public bool IsPlayerInRange()
        {
            return playerInRange;
        }
        
        public float GetHidingEffectiveness()
        {
            return playerHiding ? hidingEffectiveness : 0f;
        }
        
        public void SetTutorialMode(bool enabled)
        {
            showHideRadius = enabled;
            showInstructions = enabled;
            
            if (hideIndicator != null)
            {
                hideIndicator.SetActive(enabled && playerInRange);
            }
        }
        
        public void SetHideInstruction(string instruction)
        {
            hideInstruction = instruction;
        }
        
        // Trigger events for tutorial system
        private void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                Debug.Log("[HidingSpot] Player entered hiding spot");
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                Debug.Log("[HidingSpot] Player left hiding spot");
                playerHiding = false;
            }
        }
    }
}

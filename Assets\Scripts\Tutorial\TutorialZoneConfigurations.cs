using UnityEngine;
using System.Collections.Generic;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Predefined configurations for all tutorial zones
    /// Contains the setup data for each zone including positions, objectives, and completion conditions
    /// </summary>
    [CreateAssetMenu(fileName = "TutorialZoneConfigurations", menuName = "Tutorial/Zone Configurations")]
    public class TutorialZoneConfigurations : ScriptableObject
    {
        [Header("Zone Configurations")]
        public List<TutorialZoneConfig> zoneConfigs = new List<TutorialZoneConfig>();
        
        private void OnEnable()
        {
            if (zoneConfigs.Count == 0)
            {
                InitializeDefaultConfigurations();
            }
        }
        
        private void InitializeDefaultConfigurations()
        {
            zoneConfigs.Clear();
            
            // Zone 1: Basic Movement
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Basic Movement",
                zoneDescription = "Learn to move your character using WASD keys",
                zoneIndex = 0,
                zoneCenter = new Vector3(0, 0, 0),
                zoneSize = new Vector3(15, 5, 15),
                playerStartPosition = new Vector3(-5, 0, -5),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string> 
                { 
                    "Use WASD keys to move around",
                    "Walk to the green objective marker"
                },
                instructions = new List<string>
                {
                    "Welcome to Ashes of the Grove!",
                    "Use WASD keys to move your character",
                    "Walk to the green marker to complete this zone"
                },
                completionType = ZoneCompletionType.ReachObjective,
                completionPosition = new Vector3(5, 0, 5),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>(),
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(0, 0, 0), // Central obstacle
                    new Vector3(-2, 0, 2),
                    new Vector3(2, 0, -2)
                },
                hidingSpotPositions = new List<Vector3>()
            });
            
            // Zone 2: Crouching
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Stealth Basics - Crouching",
                zoneDescription = "Learn to crouch for stealth movement",
                zoneIndex = 1,
                zoneCenter = new Vector3(25, 0, 0),
                zoneSize = new Vector3(15, 5, 15),
                playerStartPosition = new Vector3(-5, 0, -5),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string>
                {
                    "Press Left Ctrl to crouch",
                    "Move while crouching to the objective"
                },
                instructions = new List<string>
                {
                    "Crouching makes you harder to detect",
                    "Hold Left Ctrl to crouch",
                    "Crouch and move to the objective"
                },
                completionType = ZoneCompletionType.ReachObjective,
                completionPosition = new Vector3(5, 0, 5),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>(),
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(0, 0, 0),
                    new Vector3(-3, 0, 3),
                    new Vector3(3, 0, -3)
                },
                hidingSpotPositions = new List<Vector3>
                {
                    new Vector3(-2, 0, 0),
                    new Vector3(2, 0, 0)
                }
            });
            
            // Zone 3: Running
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Movement Speed - Running",
                zoneDescription = "Learn to run for faster movement",
                zoneIndex = 2,
                zoneCenter = new Vector3(50, 0, 0),
                zoneSize = new Vector3(20, 5, 15),
                playerStartPosition = new Vector3(-8, 0, 0),
                playerStartRotation = new Vector3(0, 90, 0),
                objectives = new List<string>
                {
                    "Hold Left Shift to run",
                    "Run to the objective quickly"
                },
                instructions = new List<string>
                {
                    "Running allows faster movement",
                    "Hold Left Shift while moving to run",
                    "But be careful - running makes more noise!"
                },
                completionType = ZoneCompletionType.ReachObjective,
                completionPosition = new Vector3(8, 0, 0),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>(),
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(-2, 0, -3),
                    new Vector3(2, 0, 3),
                    new Vector3(0, 0, -5)
                },
                hidingSpotPositions = new List<Vector3>()
            });
            
            // Zone 4: Enemy Detection
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Enemy Awareness",
                zoneDescription = "Learn about enemy field of view and detection",
                zoneIndex = 3,
                zoneCenter = new Vector3(0, 0, 25),
                zoneSize = new Vector3(20, 5, 20),
                playerStartPosition = new Vector3(-8, 0, -8),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string>
                {
                    "Observe the enemy's field of view cone",
                    "Avoid the red detection area",
                    "Reach the objective without being seen"
                },
                instructions = new List<string>
                {
                    "Enemies have a field of view shown as a cone",
                    "Stay out of the red area to avoid detection",
                    "Sneak around to reach the objective"
                },
                completionType = ZoneCompletionType.StealthPractice,
                completionPosition = new Vector3(8, 0, 8),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>
                {
                    new Vector3(0, 0, 0) // Central enemy
                },
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(-4, 0, 2),
                    new Vector3(4, 0, -2),
                    new Vector3(-2, 0, -4),
                    new Vector3(2, 0, 4)
                },
                hidingSpotPositions = new List<Vector3>
                {
                    new Vector3(-6, 0, -2),
                    new Vector3(6, 0, 2),
                    new Vector3(-2, 0, -6),
                    new Vector3(2, 0, 6)
                }
            });
            
            // Zone 5: Using Cover
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Using Cover and Hiding",
                zoneDescription = "Learn to use hiding spots and cover",
                zoneIndex = 4,
                zoneCenter = new Vector3(25, 0, 25),
                zoneSize = new Vector3(20, 5, 20),
                playerStartPosition = new Vector3(-8, 0, -8),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string>
                {
                    "Use hiding spots to avoid detection",
                    "Move from cover to cover",
                    "Reach the objective safely"
                },
                instructions = new List<string>
                {
                    "Green areas are hiding spots",
                    "Stay in hiding spots when enemies are near",
                    "Move between hiding spots to stay hidden"
                },
                completionType = ZoneCompletionType.StealthPractice,
                completionPosition = new Vector3(8, 0, 8),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>
                {
                    new Vector3(-2, 0, 2),
                    new Vector3(2, 0, -2)
                },
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(0, 0, 0),
                    new Vector3(-5, 0, 0),
                    new Vector3(5, 0, 0),
                    new Vector3(0, 0, -5),
                    new Vector3(0, 0, 5)
                },
                hidingSpotPositions = new List<Vector3>
                {
                    new Vector3(-6, 0, -6),
                    new Vector3(-3, 0, 0),
                    new Vector3(0, 0, -3),
                    new Vector3(3, 0, 3),
                    new Vector3(6, 0, 6)
                }
            });
            
            // Zone 6: Patrol Patterns
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Enemy Patrol Patterns",
                zoneDescription = "Learn to predict and avoid enemy patrols",
                zoneIndex = 5,
                zoneCenter = new Vector3(50, 0, 25),
                zoneSize = new Vector3(25, 5, 20),
                playerStartPosition = new Vector3(-10, 0, -8),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string>
                {
                    "Observe enemy patrol routes",
                    "Time your movement between patrols",
                    "Reach the objective undetected"
                },
                instructions = new List<string>
                {
                    "Enemies follow patrol routes",
                    "Watch their patterns and timing",
                    "Move when they're looking away"
                },
                completionType = ZoneCompletionType.StealthPractice,
                completionPosition = new Vector3(10, 0, 8),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>
                {
                    new Vector3(-5, 0, 0),
                    new Vector3(5, 0, 0),
                    new Vector3(0, 0, -5)
                },
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(-8, 0, -3),
                    new Vector3(8, 0, 3),
                    new Vector3(-3, 0, 8),
                    new Vector3(3, 0, -8),
                    new Vector3(0, 0, 0)
                },
                hidingSpotPositions = new List<Vector3>
                {
                    new Vector3(-7, 0, -7),
                    new Vector3(-7, 0, 7),
                    new Vector3(7, 0, -7),
                    new Vector3(7, 0, 7),
                    new Vector3(0, 0, -8),
                    new Vector3(0, 0, 8)
                }
            });
            
            // Zone 7: Advanced Stealth
            zoneConfigs.Add(new TutorialZoneConfig
            {
                zoneName = "Advanced Stealth Challenge",
                zoneDescription = "Put all your skills together in a final challenge",
                zoneIndex = 6,
                zoneCenter = new Vector3(0, 0, 50),
                zoneSize = new Vector3(30, 5, 25),
                playerStartPosition = new Vector3(-12, 0, -10),
                playerStartRotation = new Vector3(0, 45, 0),
                objectives = new List<string>
                {
                    "Use all stealth techniques learned",
                    "Navigate through multiple enemies",
                    "Reach the final objective"
                },
                instructions = new List<string>
                {
                    "This is your final test!",
                    "Use crouching, hiding, and timing",
                    "Avoid all enemies to complete the tutorial"
                },
                completionType = ZoneCompletionType.StealthPractice,
                completionPosition = new Vector3(12, 0, 10),
                completionRadius = 2f,
                enemyPositions = new List<Vector3>
                {
                    new Vector3(-8, 0, -5),
                    new Vector3(-4, 0, 0),
                    new Vector3(0, 0, -8),
                    new Vector3(4, 0, 0),
                    new Vector3(8, 0, 5)
                },
                obstaclePositions = new List<Vector3>
                {
                    new Vector3(-10, 0, -8),
                    new Vector3(-6, 0, -4),
                    new Vector3(-2, 0, 0),
                    new Vector3(2, 0, 0),
                    new Vector3(6, 0, 4),
                    new Vector3(10, 0, 8),
                    new Vector3(0, 0, -10),
                    new Vector3(0, 0, 10)
                },
                hidingSpotPositions = new List<Vector3>
                {
                    new Vector3(-12, 0, -8),
                    new Vector3(-8, 0, -12),
                    new Vector3(-4, 0, -8),
                    new Vector3(0, 0, -4),
                    new Vector3(4, 0, 8),
                    new Vector3(8, 0, 12),
                    new Vector3(12, 0, 8),
                    new Vector3(-6, 0, 6),
                    new Vector3(6, 0, -6)
                }
            });
        }
        
        public TutorialZoneConfig GetZoneConfig(int index)
        {
            if (index >= 0 && index < zoneConfigs.Count)
            {
                return zoneConfigs[index];
            }
            return null;
        }
        
        public List<TutorialZoneConfig> GetAllConfigs()
        {
            return new List<TutorialZoneConfig>(zoneConfigs);
        }
    }
    
    [System.Serializable]
    public class TutorialZoneConfig
    {
        [Header("Zone Identity")]
        public string zoneName = "Tutorial Zone";
        public string zoneDescription = "Learn basic mechanics";
        public int zoneIndex = 0;
        
        [Header("Zone Boundaries")]
        public Vector3 zoneCenter = Vector3.zero;
        public Vector3 zoneSize = new Vector3(20f, 5f, 20f);
        
        [Header("Player Setup")]
        public Vector3 playerStartPosition = Vector3.zero;
        public Vector3 playerStartRotation = Vector3.zero;
        
        [Header("Content")]
        public List<string> objectives = new List<string>();
        public List<string> instructions = new List<string>();
        
        [Header("Completion")]
        public ZoneCompletionType completionType = ZoneCompletionType.ReachObjective;
        public Vector3 completionPosition = Vector3.zero;
        public float completionRadius = 2f;
        public float completionTime = 0f;
        
        [Header("Zone Elements")]
        public List<Vector3> enemyPositions = new List<Vector3>();
        public List<Vector3> obstaclePositions = new List<Vector3>();
        public List<Vector3> hidingSpotPositions = new List<Vector3>();
    }
}

using UnityEngine;
using AshesOfTheGrove.Player;
using AshesOfTheGrove.AI;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Automatically builds the tutorial scene with all necessary components
    /// Creates player, enemies, environment, and interactive elements
    /// </summary>
    public class TutorialSceneBuilder : MonoBehaviour
    {
        [Header("Scene Generation")]
        [SerializeField] private bool buildOnStart = true;
        [SerializeField] private bool clearExistingObjects = true;
        
        [Header("Player Setup")]
        [SerializeField] private Vector3 playerStartPosition = new Vector3(0, 1, -10);
        [SerializeField] private bool createPlayerVisual = true;
        
        [Header("Environment")]
        [SerializeField] private Vector3 groundSize = new Vector3(50, 1, 50);
        [SerializeField] private bool createWalls = true;
        [SerializeField] private bool createCover = true;
        
        [Header("Enemies")]
        [SerializeField] private int enemyCount = 3;
        [SerializeField] private bool setupPatrols = true;
        
        [Header("Tutorial Elements")]
        [SerializeField] private bool createObjectives = true;
        [SerializeField] private bool createHidingSpots = true;
        
        private void Start()
        {
            if (buildOnStart)
            {
                BuildTutorialScene();
            }
        }
        
        [ContextMenu("Build Tutorial Scene")]
        public void BuildTutorialScene()
        {
            Debug.Log("[TutorialSceneBuilder] Building tutorial scene...");
            
            if (clearExistingObjects)
            {
                ClearExistingObjects();
            }
            
            CreateEnvironment();
            CreatePlayer();
            CreateEnemies();
            CreateTutorialElements();
            SetupCamera();
            
            Debug.Log("[TutorialSceneBuilder] Tutorial scene built successfully!");
        }
        
        private void ClearExistingObjects()
        {
            // Clear existing tutorial objects
            GameObject[] players = GameObject.FindGameObjectsWithTag("Player");
            GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
            
            foreach (var player in players)
            {
                if (Application.isPlaying)
                    Destroy(player);
                else
                    DestroyImmediate(player);
            }
            
            foreach (var enemy in enemies)
            {
                if (Application.isPlaying)
                    Destroy(enemy);
                else
                    DestroyImmediate(enemy);
            }
            
            // Clear tutorial-specific objects
            ObjectiveMarker[] objectives = FindObjectsOfType<ObjectiveMarker>();
            HidingSpot[] hidingSpots = FindObjectsOfType<HidingSpot>();
            
            foreach (var obj in objectives)
            {
                if (Application.isPlaying)
                    Destroy(obj.gameObject);
                else
                    DestroyImmediate(obj.gameObject);
            }
            
            foreach (var spot in hidingSpots)
            {
                if (Application.isPlaying)
                    Destroy(spot.gameObject);
                else
                    DestroyImmediate(spot.gameObject);
            }
        }
        
        private void CreateEnvironment()
        {
            // Create ground
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ground.name = "Ground";
            ground.transform.position = new Vector3(0, -0.5f, 0);
            ground.transform.localScale = groundSize;
            
            // Ground material
            Material groundMat = new Material(Shader.Find("Standard"));
            groundMat.color = new Color(0.3f, 0.3f, 0.3f);
            ground.GetComponent<Renderer>().material = groundMat;
            
            if (createWalls)
            {
                CreateWalls();
            }
            
            if (createCover)
            {
                CreateCoverObjects();
            }
        }
        
        private void CreateWalls()
        {
            // Create perimeter walls
            float halfGroundX = groundSize.x * 0.5f;
            float halfGroundZ = groundSize.z * 0.5f;
            float wallHeight = 3f;
            
            // North wall
            CreateWall(new Vector3(0, wallHeight * 0.5f, halfGroundZ), new Vector3(groundSize.x, wallHeight, 0.5f));
            // South wall
            CreateWall(new Vector3(0, wallHeight * 0.5f, -halfGroundZ), new Vector3(groundSize.x, wallHeight, 0.5f));
            // East wall
            CreateWall(new Vector3(halfGroundX, wallHeight * 0.5f, 0), new Vector3(0.5f, wallHeight, groundSize.z));
            // West wall
            CreateWall(new Vector3(-halfGroundX, wallHeight * 0.5f, 0), new Vector3(0.5f, wallHeight, groundSize.z));
        }
        
        private void CreateWall(Vector3 position, Vector3 scale)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = "Wall";
            wall.transform.position = position;
            wall.transform.localScale = scale;
            
            Material wallMat = new Material(Shader.Find("Standard"));
            wallMat.color = new Color(0.6f, 0.6f, 0.6f);
            wall.GetComponent<Renderer>().material = wallMat;
        }
        
        private void CreateCoverObjects()
        {
            // Create various cover objects throughout the scene
            CreateCoverBox(new Vector3(-5, 1, -2), new Vector3(2, 2, 1));
            CreateCoverBox(new Vector3(3, 1, 2), new Vector3(1, 2, 3));
            CreateCoverBox(new Vector3(-8, 1, 5), new Vector3(1.5f, 2, 1.5f));
            CreateCoverBox(new Vector3(6, 1, -5), new Vector3(2, 2, 2));
            CreateCoverBox(new Vector3(0, 1, 8), new Vector3(3, 2, 1));
        }
        
        private void CreateCoverBox(Vector3 position, Vector3 scale)
        {
            GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cover.name = "Cover";
            cover.transform.position = position;
            cover.transform.localScale = scale;
            
            Material coverMat = new Material(Shader.Find("Standard"));
            coverMat.color = new Color(0.4f, 0.3f, 0.2f);
            cover.GetComponent<Renderer>().material = coverMat;
        }
        
        private void CreatePlayer()
        {
            GameObject player = new GameObject("Player");
            player.tag = "Player";
            player.transform.position = playerStartPosition;
            
            // Add components
            Rigidbody rb = player.AddComponent<Rigidbody>();
            rb.mass = 1f;
            rb.drag = 5f;
            rb.freezeRotation = true;
            
            CapsuleCollider col = player.AddComponent<CapsuleCollider>();
            col.height = 2f;
            col.radius = 0.5f;
            col.center = new Vector3(0, 1f, 0);
            
            PlayerController pc = player.AddComponent<PlayerController>();
            
            if (createPlayerVisual)
            {
                CreatePlayerVisual(player);
            }
            
            Debug.Log("[TutorialSceneBuilder] Player created");
        }
        
        private void CreatePlayerVisual(GameObject player)
        {
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.name = "PlayerVisual";
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = new Vector3(0, 1f, 0);
            
            // Remove collider from visual
            Destroy(visual.GetComponent<CapsuleCollider>());
            
            // Player material
            Material playerMat = new Material(Shader.Find("Standard"));
            playerMat.color = Color.blue;
            visual.GetComponent<Renderer>().material = playerMat;
        }
        
        private void CreateEnemies()
        {
            Vector3[] enemyPositions = {
                new Vector3(-5, 1, 0),
                new Vector3(5, 1, 3),
                new Vector3(0, 1, 6)
            };
            
            for (int i = 0; i < Mathf.Min(enemyCount, enemyPositions.Length); i++)
            {
                CreateEnemy(enemyPositions[i], i);
            }
        }
        
        private void CreateEnemy(Vector3 position, int index)
        {
            GameObject enemy = new GameObject($"Enemy_{index}");
            enemy.tag = "Enemy";
            enemy.transform.position = position;
            
            // Add visual
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.name = "EnemyVisual";
            visual.transform.SetParent(enemy.transform);
            visual.transform.localPosition = new Vector3(0, 1f, 0);
            
            // Enemy material
            Material enemyMat = new Material(Shader.Find("Standard"));
            enemyMat.color = Color.red;
            visual.GetComponent<Renderer>().material = enemyMat;
            
            // Add components
            CapsuleCollider col = enemy.AddComponent<CapsuleCollider>();
            col.height = 2f;
            col.radius = 0.5f;
            col.center = new Vector3(0, 1f, 0);
            
            // Add AI components
            FieldOfView fov = enemy.AddComponent<FieldOfView>();
            EnemyAIController ai = enemy.AddComponent<EnemyAIController>();
            
            // Setup for tutorial
            fov.SetTutorialVisualization(true);
            ai.SetTutorialMode(true);
            
            if (setupPatrols)
            {
                SetupEnemyPatrol(ai, index);
            }
            
            Debug.Log($"[TutorialSceneBuilder] Enemy {index} created");
        }
        
        private void SetupEnemyPatrol(EnemyAIController ai, int index)
        {
            Vector3[][] patrolRoutes = {
                new Vector3[] { new Vector3(-5, 0, 0), new Vector3(-2, 0, 0) },
                new Vector3[] { new Vector3(5, 0, 3), new Vector3(8, 0, 3), new Vector3(8, 0, 6) },
                new Vector3[] { new Vector3(0, 0, 6), new Vector3(3, 0, 6), new Vector3(3, 0, 9), new Vector3(0, 0, 9) }
            };

            if (index < patrolRoutes.Length)
            {
                ai.SetPatrolPoints(patrolRoutes[index]);
            }
        }
        
        private void CreateTutorialElements()
        {
            if (createObjectives)
            {
                CreateObjectiveMarkers();
            }
            
            if (createHidingSpots)
            {
                CreateHidingSpots();
            }
        }
        
        private void CreateObjectiveMarkers()
        {
            Vector3[] objectivePositions = {
                new Vector3(0, 0.1f, -5),
                new Vector3(5, 0.1f, -5),
                new Vector3(-5, 0.1f, 3),
                new Vector3(8, 0.1f, 8)
            };
            
            string[] objectiveNames = {
                "Basic Movement",
                "Stealth Practice",
                "Avoid Detection",
                "Final Objective"
            };
            
            for (int i = 0; i < objectivePositions.Length; i++)
            {
                CreateObjectiveMarker(objectivePositions[i], objectiveNames[i]);
            }
        }
        
        private void CreateObjectiveMarker(Vector3 position, string objectiveName)
        {
            GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            marker.name = $"Objective_{objectiveName}";
            marker.transform.position = position;
            marker.transform.localScale = new Vector3(1, 0.1f, 1);
            
            ObjectiveMarker objMarker = marker.AddComponent<ObjectiveMarker>();
            objMarker.SetObjectiveInfo(objectiveName, $"Reach the {objectiveName} marker");
        }
        
        private void CreateHidingSpots()
        {
            Vector3[] hidingPositions = {
                new Vector3(-8, 1, 5),
                new Vector3(3, 1, 2),
                new Vector3(6, 1, -5)
            };
            
            for (int i = 0; i < hidingPositions.Length; i++)
            {
                CreateHidingSpot(hidingPositions[i]);
            }
        }
        
        private void CreateHidingSpot(Vector3 position)
        {
            GameObject spot = GameObject.CreatePrimitive(PrimitiveType.Cube);
            spot.name = "HidingSpot";
            spot.transform.position = position;
            spot.transform.localScale = new Vector3(1.5f, 2f, 1.5f);
            
            Material hideMat = new Material(Shader.Find("Standard"));
            hideMat.color = new Color(0.2f, 0.6f, 0.2f);
            spot.GetComponent<Renderer>().material = hideMat;
            
            HidingSpot hideSpot = spot.AddComponent<HidingSpot>();
            hideSpot.SetTutorialMode(true);
        }
        
        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera != null)
            {
                mainCamera.transform.position = new Vector3(0, 15, -15);
                mainCamera.transform.rotation = Quaternion.Euler(30, 0, 0);
                mainCamera.orthographic = true;
                mainCamera.orthographicSize = 15;
            }
        }
    }
}

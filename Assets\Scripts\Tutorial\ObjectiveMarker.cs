using UnityEngine;
using System.Collections;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Interactive objective marker for tutorial progression
    /// Provides visual feedback and completion detection
    /// </summary>
    public class ObjectiveMarker : MonoBehaviour
    {
        [Header("Objective Settings")]
        [SerializeField] private string objectiveName = "Reach this point";
        [SerializeField] private string objectiveDescription = "Move to this location to continue";
        [SerializeField] private float completionRadius = 2f;
        [SerializeField] private bool autoComplete = true;
        
        [Header("Visual Settings")]
        [SerializeField] private bool pulseEffect = true;
        [SerializeField] private float pulseSpeed = 2f;
        [SerializeField] private Color objectiveColor = Color.green;
        [SerializeField] private Color completedColor = Color.blue;
        
        [Header("Audio")]
        [SerializeField] private AudioClip completionSound;
        [SerializeField] private bool playCompletionSound = true;
        
        // State
        private bool isCompleted = false;
        private bool playerInRange = false;
        private float pulseTimer = 0f;
        private Vector3 originalScale;
        private Material markerMaterial;
        private Renderer markerRenderer;
        private AudioSource audioSource;
        
        // Events
        public System.Action<ObjectiveMarker> OnObjectiveCompleted;
        public System.Action<ObjectiveMarker> OnPlayerEntered;
        public System.Action<ObjectiveMarker> OnPlayerExited;
        
        private void Start()
        {
            Initialize();
        }
        
        private void Initialize()
        {
            originalScale = transform.localScale;
            markerRenderer = GetComponent<Renderer>();
            
            // Create material
            if (markerRenderer != null)
            {
                markerMaterial = new Material(Shader.Find("Standard"));
                markerMaterial.color = objectiveColor;
                markerMaterial.EnableKeyword("_EMISSION");
                markerMaterial.SetColor("_EmissionColor", objectiveColor * 0.5f);
                markerRenderer.material = markerMaterial;
            }
            
            // Setup audio
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && playCompletionSound)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.volume = 0.7f;
            }
            
            // Add collider if missing
            if (GetComponent<Collider>() == null)
            {
                SphereCollider col = gameObject.AddComponent<SphereCollider>();
                col.isTrigger = true;
                col.radius = completionRadius;
            }
            
            Debug.Log($"[ObjectiveMarker] Initialized: {objectiveName}");
        }
        
        private void Update()
        {
            if (!isCompleted)
            {
                UpdatePulseEffect();
                CheckPlayerProximity();
            }
        }
        
        private void UpdatePulseEffect()
        {
            if (!pulseEffect) return;
            
            pulseTimer += Time.deltaTime * pulseSpeed;
            float pulseScale = 1f + Mathf.Sin(pulseTimer) * 0.2f;
            transform.localScale = originalScale * pulseScale;
            
            // Update emission intensity
            if (markerMaterial != null)
            {
                float emissionIntensity = 0.3f + Mathf.Sin(pulseTimer) * 0.2f;
                markerMaterial.SetColor("_EmissionColor", objectiveColor * emissionIntensity);
            }
        }
        
        private void CheckPlayerProximity()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null) return;
            
            float distance = Vector3.Distance(transform.position, player.transform.position);
            bool wasInRange = playerInRange;
            playerInRange = distance <= completionRadius;
            
            // Handle range events
            if (playerInRange && !wasInRange)
            {
                OnPlayerEnteredRange();
            }
            else if (!playerInRange && wasInRange)
            {
                OnPlayerExitedRange();
            }
            
            // Auto complete if enabled
            if (autoComplete && playerInRange && !isCompleted)
            {
                CompleteObjective();
            }
        }
        
        private void OnPlayerEnteredRange()
        {
            Debug.Log($"[ObjectiveMarker] Player entered range: {objectiveName}");
            OnPlayerEntered?.Invoke(this);
            
            // Show objective description
            TutorialUI tutorialUI = FindObjectOfType<TutorialUI>();
            if (tutorialUI != null)
            {
                tutorialUI.ShowInstruction(objectiveDescription);
            }
        }
        
        private void OnPlayerExitedRange()
        {
            Debug.Log($"[ObjectiveMarker] Player exited range: {objectiveName}");
            OnPlayerExited?.Invoke(this);
        }
        
        public void CompleteObjective()
        {
            if (isCompleted) return;
            
            isCompleted = true;
            Debug.Log($"[ObjectiveMarker] Objective completed: {objectiveName}");
            
            // Update visual state
            UpdateCompletedVisuals();
            
            // Play completion sound
            if (playCompletionSound && audioSource != null && completionSound != null)
            {
                audioSource.PlayOneShot(completionSound);
            }
            
            // Notify listeners
            OnObjectiveCompleted?.Invoke(this);
            
            // Start completion sequence
            StartCoroutine(CompletionSequence());
        }
        
        private void UpdateCompletedVisuals()
        {
            if (markerMaterial != null)
            {
                markerMaterial.color = completedColor;
                markerMaterial.SetColor("_EmissionColor", completedColor * 0.3f);
            }

            // Stop pulsing
            pulseEffect = false;
            transform.localScale = originalScale;
        }
        
        private IEnumerator CompletionSequence()
        {
            // Completion animation
            float animationTime = 1f;
            Vector3 startScale = transform.localScale;
            Vector3 endScale = startScale * 1.5f;
            
            // Scale up
            for (float t = 0; t < animationTime; t += Time.deltaTime)
            {
                float progress = t / animationTime;
                transform.localScale = Vector3.Lerp(startScale, endScale, progress);
                yield return null;
            }
            
            // Scale down and fade
            for (float t = 0; t < animationTime; t += Time.deltaTime)
            {
                float progress = t / animationTime;
                transform.localScale = Vector3.Lerp(endScale, Vector3.zero, progress);
                
                if (markerMaterial != null)
                {
                    Color color = markerMaterial.color;
                    color.a = 1f - progress;
                    markerMaterial.color = color;
                }
                
                yield return null;
            }
            
            // Deactivate
            gameObject.SetActive(false);
        }
        
        private void OnDrawGizmos()
        {
            // Draw completion radius
            Gizmos.color = isCompleted ? completedColor : objectiveColor;
            Gizmos.DrawWireSphere(transform.position, completionRadius);
            
            // Draw objective indicator
            if (!isCompleted)
            {
                Gizmos.color = objectiveColor;
                Gizmos.DrawWireCube(transform.position + Vector3.up * 2f, Vector3.one * 0.5f);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw detailed information when selected
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, completionRadius);
            
            // Draw objective name
            if (Application.isPlaying)
            {
                Gizmos.color = playerInRange ? Color.green : Color.red;
                Gizmos.DrawSphere(transform.position + Vector3.up * 3f, 0.2f);
            }
        }
        
        // Public methods for tutorial system
        public bool IsCompleted()
        {
            return isCompleted;
        }
        
        public bool IsPlayerInRange()
        {
            return playerInRange;
        }
        
        public string GetObjectiveName()
        {
            return objectiveName;
        }
        
        public string GetObjectiveDescription()
        {
            return objectiveDescription;
        }
        
        public void SetObjectiveInfo(string name, string description)
        {
            objectiveName = name;
            objectiveDescription = description;
        }
        
        public void SetCompletionRadius(float radius)
        {
            completionRadius = radius;
            
            // Update collider if it exists
            SphereCollider col = GetComponent<SphereCollider>();
            if (col != null)
            {
                col.radius = radius;
            }
        }
        
        public void SetAutoComplete(bool enabled)
        {
            autoComplete = enabled;
        }
        
        public void ResetObjective()
        {
            isCompleted = false;
            playerInRange = false;
            pulseEffect = true;
            transform.localScale = originalScale;
            gameObject.SetActive(true);
            
            if (markerMaterial != null)
            {
                markerMaterial.color = objectiveColor;
                markerMaterial.SetColor("_EmissionColor", objectiveColor * 0.5f);
            }
            
            Debug.Log($"[ObjectiveMarker] Reset objective: {objectiveName}");
        }
        
        // Trigger events
        private void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Player") && !isCompleted)
            {
                OnPlayerEnteredRange();
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Player"))
            {
                OnPlayerExitedRange();
            }
        }
    }
}

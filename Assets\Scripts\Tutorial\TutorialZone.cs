using UnityEngine;
using System.Collections.Generic;
using System.Collections;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Individual tutorial zone that manages its own enemies, objectives, and progression
    /// Each zone is a self-contained learning experience with clear boundaries
    /// </summary>
    public class TutorialZone : MonoBehaviour
    {
        [Header("Zone Identity")]
        [SerializeField] private string zoneName = "Tutorial Zone";
        [SerializeField] private string zoneDescription = "Learn basic mechanics";
        [SerializeField] private int zoneIndex = 0;
        
        [Header("Zone Boundaries")]
        [SerializeField] private Vector3 zoneCenter = Vector3.zero;
        [SerializeField] private Vector3 zoneSize = new Vector3(20f, 5f, 20f);
        [SerializeField] private bool showZoneBounds = true;
        
        [Header("Player Setup")]
        [SerializeField] private Vector3 playerStartPosition = Vector3.zero;
        [SerializeField] private Vector3 playerStartRotation = Vector3.zero;
        
        [Header("Objectives")]
        [SerializeField] private List<string> objectives = new List<string>();
        [SerializeField] private List<string> instructions = new List<string>();
        [SerializeField] private float instructionDisplayTime = 3f;
        
        [Header("Zone Elements")]
        [SerializeField] private List<GameObject> zoneEnemies = new List<GameObject>();
        [SerializeField] private List<GameObject> zoneObstacles = new List<GameObject>();
        [SerializeField] private List<GameObject> zoneObjectives = new List<GameObject>();
        [SerializeField] private List<GameObject> zoneHidingSpots = new List<GameObject>();
        
        [Header("Completion Conditions")]
        [SerializeField] private ZoneCompletionType completionType = ZoneCompletionType.ReachObjective;
        [SerializeField] private Vector3 completionPosition = Vector3.zero;
        [SerializeField] private float completionRadius = 2f;
        [SerializeField] private float completionTime = 0f; // 0 = no time limit
        
        // Zone State
        private bool isActive = false;
        private bool isCompleted = false;
        private bool playerInZone = false;

        // Configuration support
        private TutorialZoneConfig zoneConfig;
        private float zoneStartTime = 0f;
        private int currentObjectiveIndex = 0;
        
        // References
        private GameObject player;
        private TutorialZoneManager zoneManager;
        
        // Events
        public System.Action<TutorialZone> OnZoneEntered;
        public System.Action<TutorialZone> OnZoneExited;
        public System.Action<TutorialZone> OnZoneCompleted;
        public System.Action<TutorialZone, int> OnObjectiveCompleted;
        
        // Properties
        public string ZoneName => zoneName;
        public string ZoneDescription => zoneDescription;
        public int ZoneIndex => zoneIndex;
        public bool IsActive => isActive;
        public bool IsCompleted => isCompleted;
        public bool PlayerInZone => playerInZone;
        public Vector3 PlayerStartPosition => transform.position + playerStartPosition;
        public List<string> Instructions => instructions;
        public List<string> Objectives => objectives;
        
        private void Awake()
        {
            // Find zone manager
            zoneManager = FindObjectOfType<TutorialZoneManager>();
            
            // Set zone center relative to this transform
            if (zoneCenter == Vector3.zero)
            {
                zoneCenter = transform.position;
            }
            
            // Initialize zone elements
            InitializeZoneElements();
        }
        
        private void Start()
        {
            // Find player
            FindPlayer();

            // Initialize zone elements
            InitializeZoneElements();

            // Deactivate zone by default
            SetZoneActive(false);
        }

        private void FindPlayer()
        {
            // Find player GameObject first
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            if (playerObj != null)
            {
                player = playerObj.GetComponent<PlayerControllerModular>();
                if (player == null)
                {
                    Debug.LogWarning($"[TutorialZone] Player GameObject found but no PlayerControllerModular component!");
                }
            }
            else
            {
                Debug.LogWarning($"[TutorialZone] No GameObject with 'Player' tag found!");
            }
        }
        
        private void Update()
        {
            if (!isActive) return;

            // Re-find player if it's null (might have been destroyed and recreated)
            if (player == null)
            {
                FindPlayer();
            }

            CheckPlayerInZone();
            CheckCompletionConditions();
        }
        
        private void InitializeZoneElements()
        {
            Debug.Log($"[TutorialZone] Initializing zone elements for: {zoneName}");

            // Find all zone elements if not manually assigned
            if (zoneEnemies.Count == 0)
            {
                FindZoneElements("Enemy", zoneEnemies);
                Debug.Log($"[TutorialZone] Found {zoneEnemies.Count} enemies in zone");
            }

            if (zoneObstacles.Count == 0)
            {
                FindZoneElements("Obstacle", zoneObstacles);
                Debug.Log($"[TutorialZone] Found {zoneObstacles.Count} obstacles in zone");
            }

            if (zoneObjectives.Count == 0)
            {
                FindZoneElements("Objective", zoneObjectives);
                Debug.Log($"[TutorialZone] Found {zoneObjectives.Count} objectives in zone");
            }

            if (zoneHidingSpots.Count == 0)
            {
                FindZoneElements("HidingSpot", zoneHidingSpots);
                Debug.Log($"[TutorialZone] Found {zoneHidingSpots.Count} hiding spots in zone");
            }
        }
        
        private void FindZoneElements(string tag, List<GameObject> list)
        {
            GameObject[] elements = GameObject.FindGameObjectsWithTag(tag);
            foreach (var element in elements)
            {
                if (IsInZoneBounds(element.transform.position))
                {
                    list.Add(element);
                }
            }
        }
        
        private bool IsInZoneBounds(Vector3 position)
        {
            // Calculate zone center in world space
            Vector3 worldZoneCenter = transform.position + zoneCenter;
            Vector3 localPos = position - worldZoneCenter;
            return Mathf.Abs(localPos.x) <= zoneSize.x / 2f &&
                   Mathf.Abs(localPos.y) <= zoneSize.y / 2f &&
                   Mathf.Abs(localPos.z) <= zoneSize.z / 2f;
        }
        
        private void CheckPlayerInZone()
        {
            if (player == null) return;

            bool wasInZone = playerInZone;
            playerInZone = IsInZoneBounds(player.transform.position);

            if (playerInZone && !wasInZone)
            {
                OnPlayerEnteredZone();
            }
            else if (!playerInZone && wasInZone)
            {
                OnPlayerExitedZone();
            }
        }
        
        private void CheckCompletionConditions()
        {
            if (isCompleted || !playerInZone) return;
            
            bool completed = false;
            
            switch (completionType)
            {
                case ZoneCompletionType.ReachObjective:
                    completed = CheckReachObjective();
                    break;
                case ZoneCompletionType.Timer:
                    completed = CheckTimer();
                    break;
                case ZoneCompletionType.EliminateEnemies:
                    completed = CheckEnemiesEliminated();
                    break;
                case ZoneCompletionType.CollectItems:
                    completed = CheckItemsCollected();
                    break;
                case ZoneCompletionType.StealthPractice:
                    completed = CheckStealthPractice();
                    break;
            }
            
            if (completed)
            {
                CompleteZone();
            }
        }
        
        private bool CheckReachObjective()
        {
            if (player == null) return false;
            
            Vector3 targetPos = transform.position + completionPosition;
            float distance = Vector3.Distance(player.transform.position, targetPos);
            return distance <= completionRadius;
        }
        
        private bool CheckTimer()
        {
            return completionTime > 0 && (Time.time - zoneStartTime) >= completionTime;
        }
        
        private bool CheckEnemiesEliminated()
        {
            // Check if all enemies in zone are inactive/eliminated
            foreach (var enemy in zoneEnemies)
            {
                if (enemy != null && enemy.activeInHierarchy)
                {
                    return false;
                }
            }
            return true;
        }
        
        private bool CheckItemsCollected()
        {
            // Check if all objectives/items are collected
            foreach (var objective in zoneObjectives)
            {
                if (objective != null && objective.activeInHierarchy)
                {
                    return false;
                }
            }
            return true;
        }
        
        private bool CheckStealthPractice()
        {
            // Custom stealth practice logic
            // For now, just check if player reached objective without being detected
            return CheckReachObjective();
        }
        
        private void OnPlayerEnteredZone()
        {
            Debug.Log($"[TutorialZone] Player entered zone: {zoneName}");
            OnZoneEntered?.Invoke(this);
        }
        
        private void OnPlayerExitedZone()
        {
            Debug.Log($"[TutorialZone] Player exited zone: {zoneName}");
            OnZoneExited?.Invoke(this);
        }
        
        // Public control methods
        public void SetZoneActive(bool active)
        {
            isActive = active;
            
            // Activate/deactivate zone elements
            SetZoneElementsActive(active);
            
            if (active)
            {
                zoneStartTime = Time.time;
                Debug.Log($"[TutorialZone] Zone activated: {zoneName}");
            }
            else
            {
                Debug.Log($"[TutorialZone] Zone deactivated: {zoneName}");
            }
        }
        
        private void SetZoneElementsActive(bool active)
        {
            SetListActive(zoneEnemies, active);
            SetListActive(zoneObstacles, active);
            SetListActive(zoneObjectives, active);
            SetListActive(zoneHidingSpots, active);
        }
        
        private void SetListActive(List<GameObject> list, bool active)
        {
            foreach (var obj in list)
            {
                if (obj != null)
                {
                    obj.SetActive(active);
                    Debug.Log($"[TutorialZone] Set {obj.name} active: {active}");
                }
                else
                {
                    Debug.LogWarning($"[TutorialZone] Null object found in zone element list");
                }
            }
        }
        
        public void CompleteZone()
        {
            if (isCompleted) return;
            
            isCompleted = true;
            Debug.Log($"[TutorialZone] Zone completed: {zoneName}");
            
            OnZoneCompleted?.Invoke(this);
            
            // Notify zone manager
            if (zoneManager != null)
            {
                zoneManager.OnZoneCompleted(this);
            }
        }
        
        public void ResetZone()
        {
            isCompleted = false;
            isActive = false;
            playerInZone = false;
            currentObjectiveIndex = 0;
            zoneStartTime = 0f;
            
            // Reset zone elements
            SetZoneElementsActive(false);
            
            Debug.Log($"[TutorialZone] Zone reset: {zoneName}");
        }
        
        public void MovePlayerToStart()
        {
            if (player != null)
            {
                player.transform.position = PlayerStartPosition;
                player.transform.rotation = Quaternion.Euler(playerStartRotation);
                Debug.Log($"[TutorialZone] Player moved to zone start position: {PlayerStartPosition}");
            }
            else
            {
                Debug.LogWarning($"[TutorialZone] Cannot move player to start - player is null!");
            }
        }
        
        // Debug visualization
        private void OnDrawGizmos()
        {
            if (!showZoneBounds) return;

            // Draw zone bounds
            Gizmos.color = isActive ? Color.green : Color.yellow;
            if (isCompleted) Gizmos.color = Color.blue;

            // Draw zone bounds in world space
            Vector3 worldZoneCenter = transform.position + zoneCenter;
            Gizmos.DrawWireCube(worldZoneCenter, zoneSize);
            
            // Draw player start position
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position + playerStartPosition, 0.5f);
            
            // Draw completion position
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position + completionPosition, completionRadius);
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw zone name
            Vector3 labelPos = zoneCenter + Vector3.up * (zoneSize.y / 2f + 1f);

#if UNITY_EDITOR
            UnityEditor.Handles.Label(labelPos, $"{zoneName} (Zone {zoneIndex})");
#endif
        }

        // Configuration methods
        public void ConfigureFromConfig(TutorialZoneConfig config)
        {
            if (config == null) return;

            zoneConfig = config;

            // Apply configuration
            zoneName = config.zoneName;
            zoneDescription = config.zoneDescription;
            zoneIndex = config.zoneIndex;
            zoneCenter = config.zoneCenter;
            zoneSize = config.zoneSize;
            playerStartPosition = config.playerStartPosition;
            playerStartRotation = config.playerStartRotation;
            objectives = new List<string>(config.objectives);
            instructions = new List<string>(config.instructions);
            completionType = config.completionType;
            completionPosition = config.completionPosition;
            completionRadius = config.completionRadius;
            completionTime = config.completionTime;

            Debug.Log($"[TutorialZone] Configured zone: {zoneName}");
        }
    }
    
    public enum ZoneCompletionType
    {
        ReachObjective,
        Timer,
        EliminateEnemies,
        CollectItems,
        StealthPractice
    }
}

using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing
{
    /// <summary>
    /// Simple input test script that can be attached to any GameObject
    /// Provides basic input testing without complex scene setup
    /// </summary>
    public class SimpleInputTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool showDebugInfo = true;
        [SerializeField] private bool autoFindPlayer = true;
        
        [Header("References")]
        [SerializeField] private PlayerControllerModular playerController;
        
        private void Start()
        {
            if (autoFindPlayer && playerController == null)
            {
                playerController = FindObjectOfType<PlayerControllerModular>();
                if (playerController == null)
                {
                    Debug.LogWarning("[SimpleInputTest] No PlayerControllerModular found. Please assign one or create a player in the scene.");
                }
                else
                {
                    Debug.Log("[SimpleInputTest] PlayerControllerModular found and connected.");
                }
            }
            
            LogInstructions();
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F3))
            {
                showDebugInfo = !showDebugInfo;
            }
            
            if (showDebugInfo && playerController != null)
            {
                LogInputState();
            }
        }
        
        private void LogInputState()
        {
            // Log input changes (throttled to avoid spam)
            if (Time.frameCount % 30 == 0) // Every 30 frames
            {
                float h = Input.GetAxis("Horizontal");
                float v = Input.GetAxis("Vertical");
                bool crouch = Input.GetKey(KeyCode.LeftControl);
                bool run = Input.GetKey(KeyCode.LeftShift);
                
                if (Mathf.Abs(h) > 0.1f || Mathf.Abs(v) > 0.1f || crouch || run)
                {
                    Debug.Log($"[Input] H:{h:F1} V:{v:F1} Crouch:{crouch} Run:{run} | Player - Crouching:{playerController.IsCrouching} Noise:{playerController.GetNoiseLevel():F1}");
                }
            }
        }
        
        private void LogInstructions()
        {
            Debug.Log("=== SIMPLE INPUT TEST ACTIVE ===");
            Debug.Log("Controls:");
            Debug.Log("• WASD/Arrows: Move");
            Debug.Log("• Left Ctrl: Toggle Crouch");
            Debug.Log("• Left Shift: Hold to Run");
            Debug.Log("• F3: Toggle debug logging");
            Debug.Log("Watch the console for input feedback!");
        }
        
        private void OnGUI()
        {
            if (!showDebugInfo) return;
            
            // Simple on-screen display
            GUI.Box(new Rect(10, 10, 250, 120), "");
            
            GUI.Label(new Rect(20, 20, 230, 20), "=== INPUT TEST ===");
            GUI.Label(new Rect(20, 40, 230, 20), $"F3: Toggle Debug ({showDebugInfo})");
            
            if (playerController != null)
            {
                GUI.Label(new Rect(20, 60, 230, 20), $"Crouching: {playerController.IsCrouching}");
                GUI.Label(new Rect(20, 80, 230, 20), $"Hiding: {playerController.IsHiding}");
                GUI.Label(new Rect(20, 100, 230, 20), $"Noise Level: {playerController.GetNoiseLevel():F1}");
            }
            else
            {
                GUI.Label(new Rect(20, 60, 230, 20), "PlayerController: Not Found");
                GUI.Label(new Rect(20, 80, 230, 20), "Create a player or assign reference");
            }
        }
        
        // Public methods for manual testing
        [ContextMenu("Test Movement")]
        public void TestMovement()
        {
            Debug.Log("[SimpleInputTest] Testing movement - use WASD or arrow keys");
        }
        
        [ContextMenu("Test Stealth")]
        public void TestStealth()
        {
            Debug.Log("[SimpleInputTest] Testing stealth - use Left Ctrl (crouch) and Left Shift (run)");
        }
        
        [ContextMenu("Create Simple Player")]
        public void CreateSimplePlayer()
        {
            if (GameObject.FindGameObjectWithTag("Player") != null)
            {
                Debug.Log("[SimpleInputTest] Player already exists in scene");
                return;
            }
            
            // Create basic player
            GameObject player = new GameObject("Player");
            player.tag = "Player";
            
            // Add components
            Rigidbody rb = player.AddComponent<Rigidbody>();
            rb.mass = 1f;
            rb.drag = 5f;
            rb.freezeRotation = true;
            
            CapsuleCollider col = player.AddComponent<CapsuleCollider>();
            col.height = 2f;
            col.radius = 0.5f;
            col.center = new Vector3(0, 1f, 0);
            
            PlayerControllerModular pc = player.AddComponent<PlayerControllerModular>();
            
            // Create simple visual
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.name = "Visual";
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = new Vector3(0, 1f, 0);
            Destroy(visual.GetComponent<CapsuleCollider>());
            
            // Set reference
            playerController = pc;
            
            Debug.Log("[SimpleInputTest] Simple player created successfully!");
        }
    }
}

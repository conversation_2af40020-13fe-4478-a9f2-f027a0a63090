using UnityEngine;
using UnityEngine.InputSystem;
using AshesOfTheGrove.Tutorial;

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Simple demonstration script showing how to use the tutorial system
    /// This script can be added to an empty GameObject to create a complete tutorial scene
    /// </summary>
    public class TutorialDemo : MonoBehaviour
    {
        [Header("Demo Configuration")]
        [SerializeField] private bool autoStartDemo = true;
        [SerializeField] private bool showInstructions = true;
        
        private ModularTutorialSceneSetup sceneSetup;
        
        private void Start()
        {
            if (autoStartDemo)
            {
                StartDemo();
            }
        }
        
        private void Update()
        {
            // Demo controls using new Input System
            if (Keyboard.current != null)
            {
                if (Keyboard.current.rKey.wasPressedThisFrame)
                {
                    RestartDemo();
                }

                if (Keyboard.current.hKey.wasPressedThisFrame)
                {
                    ToggleInstructions();
                }

                if (Keyboard.current.escapeKey.wasPressedThisFrame)
                {
                    QuitDemo();
                }
            }
        }
        
        [ContextMenu("Start Demo")]
        public void StartDemo()
        {
            Debug.Log("[TutorialDemo] Starting tutorial demo...");
            
            // Create the scene setup if it doesn't exist
            if (sceneSetup == null)
            {
                GameObject setupObj = new GameObject("ModularTutorialSceneSetup");
                sceneSetup = setupObj.AddComponent<ModularTutorialSceneSetup>();
            }
            
            // Setup the complete tutorial scene
            sceneSetup.SetupTutorialScene();
            
            if (showInstructions)
            {
                ShowDemoInstructions();
            }
            
            Debug.Log("[TutorialDemo] Demo started successfully!");
        }
        
        [ContextMenu("Restart Demo")]
        public void RestartDemo()
        {
            Debug.Log("[TutorialDemo] Restarting demo...");
            
            if (sceneSetup != null)
            {
                sceneSetup.RestartTutorial();
            }
            else
            {
                StartDemo();
            }
        }
        
        private void ShowDemoInstructions()
        {
            string instructions = @"
=== TUTORIAL DEMO CONTROLS ===

Movement:
- WASD: Move player
- Ctrl: Crouch/Stealth
- Mouse: Look around

Demo Controls:
- R: Restart tutorial
- H: Toggle this help
- F4: Toggle debug info
- Esc: Quit demo

Tutorial Features:
- Follow the green objective markers
- Use hiding spots (green cubes) for cover
- Avoid red enemy detection cones
- Watch AI state colors:
  * Green = Patrolling
  * Yellow = Alert
  * Orange = Searching
  * Red = Chasing

Enjoy the tutorial!
";
            
            Debug.Log(instructions);
            
            // Also show in UI if available
            TutorialUI tutorialUI = FindObjectOfType<TutorialUI>();
            if (tutorialUI != null)
            {
                tutorialUI.ShowInstruction("Press H for help, R to restart, F4 for debug info");
            }
        }
        
        private void ToggleInstructions()
        {
            showInstructions = !showInstructions;
            
            if (showInstructions)
            {
                ShowDemoInstructions();
            }
            else
            {
                Debug.Log("[TutorialDemo] Instructions hidden");
            }
        }
        
        private void QuitDemo()
        {
            Debug.Log("[TutorialDemo] Quitting demo...");
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        private void OnGUI()
        {
            if (!showInstructions) return;
            
            // Simple on-screen instructions
            GUI.Box(new Rect(10, 10, 300, 120), "Tutorial Demo Controls");
            
            GUILayout.BeginArea(new Rect(15, 35, 290, 100));
            GUILayout.Label("R - Restart Tutorial");
            GUILayout.Label("H - Toggle Help");
            GUILayout.Label("F4 - Toggle Debug Info");
            GUILayout.Label("Esc - Quit Demo");
            GUILayout.Label("");
            GUILayout.Label("Follow green markers, avoid red cones!");
            GUILayout.EndArea();
        }
    }
}

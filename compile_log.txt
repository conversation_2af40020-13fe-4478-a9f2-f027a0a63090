[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to the License Client on channel: "LicenseClient-Zied" at "2025-06-25T23:49:39.6758143Z"
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.13.1+ca5f299
  Session Id:              80d64a1e0d694e3ca356af0357073216
  Correlation Id:          ce09be280df982597377cb02f00c8db0
  External correlation Id: 197661883826835496
  Machine Id:              DhZ4FUQtahDYOjm+VBIuhh+0VZY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-Zied" (connect: 0.00s, validation: 0.01s, handshake: 0.55s)
[Licensing::IpcConnector] Successfully connected to the License Notification on channel: "LicenseClient-Zied-notifications" at "2025-06-25T23:49:40.2406279Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
Built from '2022.3/staging' branch; Version is '2022.3.9f1 (ea401c316338) revision 15351836'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65262 MB
[Licensing::Client] Successfully resolved entitlements
[Licensing::Module] Serial number assigned to: "F4-9EX7-UASD-MEWC-NJ6K-XXXX"
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\Unity.exe
-batchmode
-quit
-projectPath
c:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove
-logFile
compile_log.txt
Successfully changed project path to: c:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove
C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
Fatal Error! It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
Crash!!!
SymInit: Symbol-SearchPath: 'C:/Program Files/Unity/Hub/Editor/2022.3.9f1/Editor/Data/Mono;.;C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove;C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\BurstCache\JIT;C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor;C:\WINDOWS;C:\WINDOWS\system32;', symOptions: 534, UserName: 'Zied'
OS-Version: 10.0.0
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\Unity.exe:Unity.exe (00007FF7BB020000), size: 82272256 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2022.3.9.16412
C:\WINDOWS\SYSTEM32\ntdll.dll:ntdll.dll (00007FF84E520000), size: 2510848 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNEL32.DLL:KERNEL32.DLL (00007FF84DAF0000), size: 823296 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\KERNELBASE.dll:KERNELBASE.dll (00007FF84B670000), size: 4096000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\CRYPT32.dll:CRYPT32.dll (00007FF84BBD0000), size: 1536000 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3775
C:\WINDOWS\System32\ucrtbase.dll:ucrtbase.dll (00007FF84BD50000), size: 1355776 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\USER32.dll:USER32.dll (00007FF84DEC0000), size: 1875968 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\win32u.dll:win32u.dll (00007FF84BBA0000), size: 159744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\System32\GDI32.dll:GDI32.dll (00007FF84E320000), size: 176128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\s3tcompress.dll:s3tcompress.dll (00007FFF63990000), size: 180224 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\gdi32full.dll:gdi32full.dll (00007FF84C0C0000), size: 1273856 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\etccompress.dll:etccompress.dll (00007FFF27B50000), size: 5066752 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\msvcp_win.dll:msvcp_win.dll (00007FF84BAF0000), size: 667648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\ispc_texcomp.dll:ispc_texcomp.dll (00007FFF2DBD0000), size: 1826816 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\ADVAPI32.dll:ADVAPI32.dll (00007FF84D220000), size: 733184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\libfbxsdk.dll:libfbxsdk.dll (00007FFF23C20000), size: 10067968 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2020.3.3.0
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\compress_bc7e.dll:compress_bc7e.dll (00007FFF2D810000), size: 1433600 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\msvcrt.dll:msvcrt.dll (00007FF84DE10000), size: 692224 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 7.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\FreeImage.dll:FreeImage.dll (0000000180000000), size: 6582272 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: ********
C:\WINDOWS\System32\sechost.dll:sechost.dll (00007FF84C2C0000), size: 679936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\WinPixEventRuntime.dll:WinPixEventRuntime.dll (00007FFFE48D0000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.0.1812.6001
C:\WINDOWS\System32\RPCRT4.dll:RPCRT4.dll (00007FF84DBF0000), size: 1134592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SHELL32.dll:SHELL32.dll (00007FF84C6B0000), size: 7610368 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\wintypes.dll:wintypes.dll (00007FF84BEA0000), size: 1523712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\combase.dll:combase.dll (00007FF84CE10000), size: 3690496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WS2_32.dll:WS2_32.dll (00007FF84C630000), size: 475136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\System32\SHLWAPI.dll:SHLWAPI.dll (00007FF84D7E0000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\umbraoptimizer64.dll:umbraoptimizer64.dll (00007FFF2D5C0000), size: 1187840 (result: 0), SymType: '-deferred-', PDB: ''
C:\WINDOWS\System32\ole32.dll:ole32.dll (00007FF84C370000), size: 1695744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\IMM32.dll:IMM32.dll (00007FF84D8D0000), size: 196608 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\SETUPAPI.dll:SETUPAPI.dll (00007FF84D350000), size: 4743168 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\MSVCP140.dll:MSVCP140.dll (00007FFF5E450000), size: 561152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.44.35211.0
C:\WINDOWS\SYSTEM32\VCRUNTIME140.dll:VCRUNTIME140.dll (00007FFF710F0000), size: 122880 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.44.35211.0
C:\WINDOWS\System32\OLEAUT32.dll:OLEAUT32.dll (00007FF84DD10000), size: 921600 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\WINTRUST.dll:WINTRUST.dll (00007FF84BA60000), size: 540672 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4351
C:\WINDOWS\SYSTEM32\GLU32.dll:GLU32.dll (00007FFFA5860000), size: 184320 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\OPENGL32.dll:OPENGL32.dll (00007FFFA7CB0000), size: 1130496 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL:IPHLPAPI.DLL (00007FF849E90000), size: 208896 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\VCRUNTIME140_1.dll:VCRUNTIME140_1.dll (00007FF832120000), size: 49152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 14.44.35211.0
C:\WINDOWS\SYSTEM32\WINMM.dll:WINMM.dll (00007FF83E410000), size: 217088 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\WINHTTP.dll:WINHTTP.dll (00007FF843DD0000), size: 1171456 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\bcrypt.dll:bcrypt.dll (00007FF84B550000), size: 155648 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\SYSTEM32\HID.DLL:HID.DLL (00007FF849B30000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\dwmapi.dll:dwmapi.dll (00007FF8486F0000), size: 221184 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\VERSION.dll:VERSION.dll (00007FF844F90000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\SketchUpAPI.dll:SketchUpAPI.dll (00007FFF23380000), size: 8990720 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 23.0.0.0
C:\WINDOWS\SYSTEM32\WSOCK32.dll:WSOCK32.dll (00007FFFBDEA0000), size: 40960 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\dxcore.dll:dxcore.dll (00007FF848540000), size: 315392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\SketchUpCommonPreferences.dll:SketchUpCommonPreferences.dll (00007FFF3E560000), size: 499712 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 23.0.0.0
C:\WINDOWS\SYSTEM32\Secur32.dll:Secur32.dll (00007FF848160000), size: 53248 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\SSPICLI.DLL:SSPICLI.DLL (00007FF84A7B0000), size: 299008 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\OpenRL.dll:OpenRL.dll (0000029A63D70000), size: 12779520 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 1.5.0.2907
C:\WINDOWS\SYSTEM32\MSVCP100.dll:MSVCP100.dll (00000000607B0000), size: 622592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
C:\WINDOWS\SYSTEM32\MSVCR100.dll:MSVCR100.dll (00000000606D0000), size: 860160 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.40219.325
C:\WINDOWS\SYSTEM32\cfgmgr32.DLL:cfgmgr32.DLL (00007FF84B2D0000), size: 356352 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\embree.dll:embree.dll (00007FFF22390000), size: 16711680 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.14.0.0
C:\WINDOWS\SYSTEM32\MSWSOCK.DLL:MSWSOCK.DLL (00007FF84AA60000), size: 434176 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\tbb.dll:tbb.dll (00007FFF637E0000), size: 413696 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2017.0.2016.1004
C:\WINDOWS\SYSTEM32\MSVCP120.dll:MSVCP120.dll (00007FFF5DFF0000), size: 679936 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 12.0.40664.0
C:\WINDOWS\SYSTEM32\MSVCR120.dll:MSVCR120.dll (00007FFF5DDA0000), size: 978944 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 12.0.40664.0
C:\Program Files\Unity\Hub\Editor\2022.3.9f1\Editor\OpenRL_pthread.dll:OpenRL_pthread.dll (0000029A649C0000), size: 61440 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2.9.0.0
C:\WINDOWS\SYSTEM32\MSASN1.dll:MSASN1.dll (00007FF84AEA0000), size: 77824 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\System32\bcryptprimitives.dll:bcryptprimitives.dll (00007FF84C020000), size: 626688 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\kernel.appcore.dll:kernel.appcore.dll (00007FF84A510000), size: 110592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\system32\uxtheme.dll:uxtheme.dll (00007FF848360000), size: 716800 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\windows.storage.dll:windows.storage.dll (00007FF8492D0000), size: 8749056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4343
C:\WINDOWS\System32\SHCORE.dll:SHCORE.dll (00007FF84E350000), size: 987136 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\profapi.dll:profapi.dll (00007FF84B580000), size: 192512 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\CRYPTSP.dll:CRYPTSP.dll (00007FF84AE40000), size: 110592 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\system32\rsaenh.dll:rsaenh.dll (00007FF84A470000), size: 237568 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\CRYPTBASE.dll:CRYPTBASE.dll (00007FF84ACA0000), size: 49152 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\System32\imagehlp.dll:imagehlp.dll (00007FF84DBC0000), size: 131072 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\SYSTEM32\gpapi.dll:gpapi.dll (00007FF84AAD0000), size: 159744 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3323
C:\Windows\System32\cryptnet.dll:cryptnet.dll (00007FF843900000), size: 241664 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.3624
C:\WINDOWS\SYSTEM32\WINNSI.DLL:WINNSI.DLL (00007FF848340000), size: 57344 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\NSI.dll:NSI.dll (00007FF84CE00000), size: 40960 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4061
C:\WINDOWS\system32\IconCodecService.dll:IconCodecService.dll (00007FF817D90000), size: 45056 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1
C:\WINDOWS\SYSTEM32\WindowsCodecs.dll:WindowsCodecs.dll (00007FF846990000), size: 2330624 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202
C:\WINDOWS\System32\clbcatq.dll:clbcatq.dll (00007FF84E250000), size: 688128 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 2001.12.10941.16384
C:\WINDOWS\System32\netprofm.dll:netprofm.dll (00007FF845470000), size: 413696 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.2454
C:\WINDOWS\System32\npmproxy.dll:npmproxy.dll (00007FF843030000), size: 102400 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.1150
C:\WINDOWS\SYSTEM32\dbghelp.dll:dbghelp.dll (00007FF848CE0000), size: 2363392 (result: 0), SymType: '-deferred-', PDB: '', fileVersion: 10.0.26100.4202

========== OUTPUTTING STACK TRACE ==================

0x00007FF84B7385EA (KERNELBASE) RaiseException
0x00007FF7BCBD2203 (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FF7BCBD2C43 (Unity) EditorMonoConsole::LogToConsoleImplementation
0x00007FF7BD886C5F (Unity) DebugStringToFilePostprocessedStacktrace
0x00007FF7BD8863BD (Unity) DebugStringToFile
0x00007FF7BCD469B7 (Unity) HandleProjectAlreadyOpenInAnotherInstance
0x00007FF7BCD4952C (Unity) Application::InitializeProject
0x00007FF7BD1D39E5 (Unity) WinMain
0x00007FF7BE5B7FEE (Unity) __scrt_common_main_seh
0x00007FF84DB1E8D7 (KERNEL32) BaseThreadInitThunk
0x00007FF84E55C34C (ntdll) RtlUserThreadStart

========== END OF STACKTRACE ===========

A crash has been intercepted by the crash handler. For call stack and other details, see the latest crash report generated in:
 * C:/Users/<USER>/AppData/Local/Temp/Unity/Editor/Crashes

using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Handles all UI elements for the tutorial system
    /// Displays instructions, objectives, and progress feedback
    /// </summary>
    public class TutorialUI : MonoBehaviour
    {
        [Header("UI Configuration")]
        [SerializeField] private bool showDebugInfo = true;
        [SerializeField] private float messageDisplayTime = 3f;
        [SerializeField] private float fadeSpeed = 2f;
        
        [Header("UI Styling")]
        [SerializeField] private Color backgroundColor = new Color(0, 0, 0, 0.7f);
        [SerializeField] private Color textColor = Color.white;
        [SerializeField] private Color objectiveColor = Color.yellow;
        [SerializeField] private Color completeColor = Color.green;
        
        // UI State
        private string currentInstruction = "";
        private string currentObjective = "";
        private string currentAreaName = "";
        private bool showWelcome = false;
        private bool showComplete = false;
        private bool showAreaComplete = false;
        private float messageAlpha = 0f;
        private float welcomeTimer = 0f;
        private float completeTimer = 0f;

        // Zone System Support
        private TutorialZone currentZone;
        private TutorialZoneManager zoneManager;
        
        // GUI Styles
        private GUIStyle instructionStyle;
        private GUIStyle objectiveStyle;
        private GUIStyle titleStyle;
        private GUIStyle backgroundStyle;
        
        private void Start()
        {
            InitializeStyles();

            // Find zone manager
            zoneManager = FindObjectOfType<TutorialZoneManager>();
        }
        
        private void Update()
        {
            UpdateTimers();
            UpdateAlpha();
            
            // Toggle debug info
            if (Keyboard.current != null && Keyboard.current.f4Key.wasPressedThisFrame)
            {
                showDebugInfo = !showDebugInfo;
            }
        }
        
        private void InitializeStyles()
        {
            // Instruction style
            instructionStyle = new GUIStyle();
            instructionStyle.fontSize = 16;
            instructionStyle.normal.textColor = textColor;
            instructionStyle.alignment = TextAnchor.MiddleCenter;
            instructionStyle.wordWrap = true;
            
            // Objective style
            objectiveStyle = new GUIStyle();
            objectiveStyle.fontSize = 18;
            objectiveStyle.normal.textColor = objectiveColor;
            objectiveStyle.alignment = TextAnchor.MiddleCenter;
            objectiveStyle.fontStyle = FontStyle.Bold;
            objectiveStyle.wordWrap = true;
            
            // Title style
            titleStyle = new GUIStyle();
            titleStyle.fontSize = 24;
            titleStyle.normal.textColor = textColor;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            titleStyle.fontStyle = FontStyle.Bold;
            
            // Background style
            backgroundStyle = new GUIStyle();
            backgroundStyle.normal.background = CreateColorTexture(backgroundColor);
        }
        
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }
        
        private void UpdateTimers()
        {
            if (showWelcome)
            {
                welcomeTimer += Time.deltaTime;
                if (welcomeTimer >= 5f)
                {
                    showWelcome = false;
                    welcomeTimer = 0f;
                }
            }
            
            if (showComplete)
            {
                completeTimer += Time.deltaTime;
                if (completeTimer >= 5f)
                {
                    showComplete = false;
                    completeTimer = 0f;
                }
            }
            
            if (showAreaComplete)
            {
                completeTimer += Time.deltaTime;
                if (completeTimer >= 3f)
                {
                    showAreaComplete = false;
                    completeTimer = 0f;
                }
            }
        }
        
        private void UpdateAlpha()
        {
            bool shouldShow = !string.IsNullOrEmpty(currentInstruction) || 
                             !string.IsNullOrEmpty(currentObjective) ||
                             showWelcome || showComplete || showAreaComplete;
            
            if (shouldShow)
            {
                messageAlpha = Mathf.MoveTowards(messageAlpha, 1f, fadeSpeed * Time.deltaTime);
            }
            else
            {
                messageAlpha = Mathf.MoveTowards(messageAlpha, 0f, fadeSpeed * Time.deltaTime);
            }
        }
        
        private void OnGUI()
        {
            if (messageAlpha <= 0f) return;
            
            // Set alpha for all GUI elements
            Color originalColor = GUI.color;
            GUI.color = new Color(1f, 1f, 1f, messageAlpha);
            
            DrawMainUI();
            
            if (showDebugInfo)
            {
                DrawDebugInfo();
            }
            
            GUI.color = originalColor;
        }
        
        private void DrawMainUI()
        {
            float screenWidth = Screen.width;
            float screenHeight = Screen.height;
            
            // Welcome message
            if (showWelcome)
            {
                DrawCenteredBox(screenWidth * 0.5f, screenHeight * 0.3f, 400, 100);
                GUI.Label(new Rect(screenWidth * 0.5f - 200, screenHeight * 0.3f - 50, 400, 100), 
                         "Welcome to Ashes of the Grove\nStealth Tutorial", titleStyle);
            }
            
            // Tutorial complete message
            if (showComplete)
            {
                DrawCenteredBox(screenWidth * 0.5f, screenHeight * 0.3f, 400, 100);
                GUI.Label(new Rect(screenWidth * 0.5f - 200, screenHeight * 0.3f - 50, 400, 100), 
                         "Tutorial Complete!\nWell done, Agent.", titleStyle);
            }
            
            // Area complete message
            if (showAreaComplete)
            {
                DrawCenteredBox(screenWidth * 0.5f, screenHeight * 0.4f, 300, 60);
                GUI.color = new Color(completeColor.r, completeColor.g, completeColor.b, messageAlpha);
                GUI.Label(new Rect(screenWidth * 0.5f - 150, screenHeight * 0.4f - 30, 300, 60), 
                         $"{currentAreaName} Complete!", objectiveStyle);
                GUI.color = new Color(1f, 1f, 1f, messageAlpha);
            }
            
            // Current objective (top of screen)
            if (!string.IsNullOrEmpty(currentObjective))
            {
                DrawCenteredBox(screenWidth * 0.5f, 80, 500, 40);
                GUI.Label(new Rect(screenWidth * 0.5f - 250, 60, 500, 40), 
                         $"Objective: {currentObjective}", objectiveStyle);
            }
            
            // Current instruction (bottom of screen)
            if (!string.IsNullOrEmpty(currentInstruction))
            {
                float instructionY = screenHeight - 120;
                DrawCenteredBox(screenWidth * 0.5f, instructionY, 600, 80);
                GUI.Label(new Rect(screenWidth * 0.5f - 300, instructionY - 40, 600, 80), 
                         currentInstruction, instructionStyle);
            }
            
            // Controls reminder (bottom right)
            DrawControlsReminder();
        }
        
        private void DrawCenteredBox(float centerX, float centerY, float width, float height)
        {
            GUI.Box(new Rect(centerX - width * 0.5f, centerY - height * 0.5f, width, height), "", backgroundStyle);
        }
        
        private void DrawControlsReminder()
        {
            float x = Screen.width - 220;
            float y = Screen.height - 100;
            
            GUI.Box(new Rect(x, y, 200, 80), "", backgroundStyle);
            
            string controls = "Controls:\nWASD - Move\nCtrl - Crouch\nShift - Run\nF4 - Toggle Debug";
            GUI.Label(new Rect(x + 10, y + 10, 180, 60), controls, instructionStyle);
        }
        
        private void DrawDebugInfo()
        {
            // Debug panel (top left)
            GUI.Box(new Rect(10, 10, 250, 120), "", backgroundStyle);
            
            string debugText = $"Tutorial Debug Info:\n" +
                              $"Area: {currentAreaName}\n" +
                              $"Objective: {(!string.IsNullOrEmpty(currentObjective) ? "Active" : "None")}\n" +
                              $"Instruction: {(!string.IsNullOrEmpty(currentInstruction) ? "Active" : "None")}\n" +
                              $"Alpha: {messageAlpha:F2}";
            
            GUI.Label(new Rect(20, 20, 230, 100), debugText, instructionStyle);
        }
        
        // Public methods for tutorial manager
        public void ShowWelcomeMessage()
        {
            showWelcome = true;
            welcomeTimer = 0f;
            Debug.Log("[TutorialUI] Showing welcome message");
        }
        
        public void ShowInstruction(string instruction)
        {
            currentInstruction = instruction;
            StartCoroutine(ClearInstructionAfterDelay());
            Debug.Log($"[TutorialUI] Showing instruction: {instruction}");
        }
        
        public void UpdateArea(TutorialArea area)
        {
            currentAreaName = area.areaName;
            currentObjective = area.objective;
            Debug.Log($"[TutorialUI] Updated to area: {area.areaName}");
        }
        
        public void ShowAreaComplete(string areaName)
        {
            currentAreaName = areaName;
            showAreaComplete = true;
            completeTimer = 0f;
            Debug.Log($"[TutorialUI] Area complete: {areaName}");
        }
        
        public void ShowTutorialComplete()
        {
            showComplete = true;
            completeTimer = 0f;
            currentInstruction = "";
            currentObjective = "";
            Debug.Log("[TutorialUI] Tutorial complete!");
        }
        
        public void ClearInstruction()
        {
            currentInstruction = "";
        }
        
        public void ClearObjective()
        {
            currentObjective = "";
        }
        
        private IEnumerator ClearInstructionAfterDelay()
        {
            yield return new WaitForSeconds(messageDisplayTime);
            currentInstruction = "";
        }
        
        // Utility methods
        public void SetMessageDisplayTime(float time)
        {
            messageDisplayTime = time;
        }
        
        public void SetShowDebugInfo(bool show)
        {
            showDebugInfo = show;
        }

        // Zone System Support Methods
        public void UpdateZone(TutorialZone zone)
        {
            currentZone = zone;
            if (zone != null)
            {
                currentAreaName = zone.ZoneName;

                // Build objectives text
                string objectivesText = "";
                for (int i = 0; i < zone.Objectives.Count; i++)
                {
                    objectivesText += $"• {zone.Objectives[i]}";
                    if (i < zone.Objectives.Count - 1) objectivesText += "\n";
                }
                currentObjective = objectivesText;
            }
        }

        public void ShowZoneComplete(string zoneName)
        {
            ShowAreaComplete(zoneName);
        }

        public void ShowTutorialComplete()
        {
            ShowComplete();
        }

        public void ShowWelcomeMessage()
        {
            ShowWelcome();
        }
    }
}

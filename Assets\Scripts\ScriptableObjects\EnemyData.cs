using UnityEngine;

namespace AshesOfTheGrove.Data
{
    /// <summary>
    /// ScriptableObject for defining enemy types and their properties
    /// </summary>
    [CreateAssetMenu(fileName = "New Enemy Type", menuName = "Ashes of the Grove/Enemy Data")]
    public class EnemyData : ScriptableObject
    {
        [Header("Basic Info")]
        public string enemyName;
        public EnemyType enemyType;
        public GameObject enemyPrefab;
        
        [<PERSON><PERSON>("AI Behavior")]
        public float patrolSpeed = 2f;
        public float alertSpeed = 5f;
        public float detectionRange = 10f;
        public float fieldOfViewAngle = 90f;
        public float detectionTime = 2f;
        
        [Header("Audio")]
        public AudioClip[] footstepSounds;
        public AudioClip alertSound;
        public AudioClip searchSound;
        
        [Header("Equipment")]
        public bool hasWeapon = false;
        public bool hasRadio = true;
        public bool hasFlashlight = false;
        
        public enum EnemyType
        {
            Guard,
            Patrol,
            Sniper,
            Dog,
            Camera
        }
    }
}

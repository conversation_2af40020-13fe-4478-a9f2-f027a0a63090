using UnityEngine;
using UnityEngine.AI;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.AI
{
    /// <summary>
    /// AI Controller for enemy guards with patrol and alert states
    /// Inspired by Metal Gear Solid stealth mechanics
    /// </summary>
    public class EnemyAIController : MonoBehaviour
    {
        [Header("AI States")]
        public AIState currentState = AIState.Patrol;
        
        [Header("Patrol Settings")]
        [SerializeField] private Transform[] patrolPoints;
        [SerializeField] private float patrolSpeed = 1.5f;
        [SerializeField] private float waitTime = 2f;

        [Header("Detection Settings")]
        [SerializeField] private float detectionRange = 10f;
        [SerializeField] private float alertSpeed = 4f;
        [SerializeField] private float searchTime = 10f;
        
        [Header("Field of View")]
        [SerializeField] private FieldOfView fieldOfView;

        [Header("Tutorial Settings")]
        [SerializeField] private bool showStateIndicator = true;
        [SerializeField] private bool enableDebugLogs = false;

        private NavMeshAgent agent;
        private Animator animator;
        private int currentPatrolIndex = 0;
        private float waitTimer = 0f;
        private float searchTimer = 0f;
        private Vector3 lastKnownPlayerPosition;
        private PlayerController player;

        // Tutorial support
        private Material originalMaterial;
        private Renderer enemyRenderer;
        
        public enum AIState
        {
            Patrol,
            Alert,
            Search,
            Chase
        }
        
        private void Awake()
        {
            agent = GetComponent<NavMeshAgent>();
            animator = GetComponent<Animator>();
            fieldOfView = GetComponent<FieldOfView>();
            player = FindObjectOfType<PlayerController>();

            // Warn if NavMeshAgent is missing
            if (agent == null)
            {
                Debug.LogWarning($"[EnemyAIController] NavMeshAgent component missing on {gameObject.name}. AI movement will not work properly.");
            }
            else
            {
                // Check if NavMesh is available
                if (!NavMesh.SamplePosition(transform.position, out NavMeshHit hit, 1.0f, NavMesh.AllAreas))
                {
                    Debug.LogWarning($"[EnemyAIController] No NavMesh found near {gameObject.name}. Please bake NavMesh for proper AI navigation.");
                    Debug.LogWarning("[EnemyAIController] To fix: Window > AI > Navigation, select ground objects, mark as 'Navigation Static', then click 'Bake'");
                }
            }
        }
        
        private void Start()
        {
            // Try to place agent on NavMesh if it's not already
            if (agent != null && !agent.isOnNavMesh)
            {
                TryPlaceOnNavMesh();
            }

            if (agent != null && agent.isOnNavMesh && patrolPoints.Length > 0)
            {
                agent.SetDestination(patrolPoints[0].position);
            }
        }
        
        private void Update()
        {
            switch (currentState)
            {
                case AIState.Patrol:
                    HandlePatrol();
                    break;
                case AIState.Alert:
                    HandleAlert();
                    break;
                case AIState.Search:
                    HandleSearch();
                    break;
                case AIState.Chase:
                    HandleChase();
                    break;
            }
            
            CheckForPlayer();
            UpdateAnimator();
        }
        
        private void HandlePatrol()
        {
            if (agent == null || !agent.isOnNavMesh) return;

            agent.speed = patrolSpeed;

            if (!agent.pathPending && agent.remainingDistance < 0.5f)
            {
                waitTimer += Time.deltaTime;

                if (waitTimer >= waitTime)
                {
                    waitTimer = 0f;
                    currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
                    agent.SetDestination(patrolPoints[currentPatrolIndex].position);
                }
            }
        }
        
        private void HandleAlert()
        {
            if (agent == null || !agent.isOnNavMesh) return;

            agent.speed = alertSpeed;
            agent.SetDestination(lastKnownPlayerPosition);

            if (!agent.pathPending && agent.remainingDistance < 1f)
            {
                ChangeState(AIState.Search);
            }
        }
        
        private void HandleSearch()
        {
            if (agent == null) return;

            agent.speed = patrolSpeed;
            searchTimer += Time.deltaTime;

            if (searchTimer >= searchTime)
            {
                searchTimer = 0f;
                ChangeState(AIState.Patrol);
            }
        }
        
        private void HandleChase()
        {
            if (agent == null || !agent.isOnNavMesh) return;

            agent.speed = alertSpeed;

            if (player != null)
            {
                agent.SetDestination(player.transform.position);
                lastKnownPlayerPosition = player.transform.position;
            }
        }
        
        private void CheckForPlayer()
        {
            if (fieldOfView != null && fieldOfView.CanSeePlayer())
            {
                if (currentState != AIState.Chase)
                {
                    lastKnownPlayerPosition = player.transform.position;
                    ChangeState(AIState.Chase);
                }
            }
            else if (currentState == AIState.Chase)
            {
                ChangeState(AIState.Alert);
            }
        }
        
        private void ChangeState(AIState newState)
        {
            currentState = newState;
            
            switch (newState)
            {
                case AIState.Search:
                    searchTimer = 0f;
                    break;
            }
        }
        
        private void UpdateAnimator()
        {
            if (animator != null && agent != null && agent.isOnNavMesh)
            {
                animator.SetFloat("Speed", agent.velocity.magnitude);
                animator.SetBool("IsAlert", currentState != AIState.Patrol);
            }

            // Update state visualization for tutorial
            UpdateStateVisualization();
        }

        // Tutorial support methods
        public void SetPatrolPoints(Vector3[] points)
        {
            patrolPoints = new Transform[points.Length];
            for (int i = 0; i < points.Length; i++)
            {
                GameObject patrolPoint = new GameObject($"PatrolPoint_{i}");
                patrolPoint.transform.position = points[i];
                patrolPoint.transform.SetParent(transform);
                patrolPoints[i] = patrolPoint.transform;
            }

            currentPatrolIndex = 0;
            if (agent != null && agent.isOnNavMesh && patrolPoints.Length > 0)
            {
                agent.SetDestination(patrolPoints[0].position);
            }

            if (enableDebugLogs)
                Debug.Log($"[EnemyAI] Set {points.Length} patrol points for {gameObject.name}");
        }

        public void SetTutorialMode(bool enabled)
        {
            showStateIndicator = enabled;
            enableDebugLogs = enabled;

            if (enabled)
            {
                // Get renderer for state visualization
                enemyRenderer = GetComponentInChildren<Renderer>();
                if (enemyRenderer != null)
                {
                    originalMaterial = enemyRenderer.material;
                }
            }
        }

        public AIState GetCurrentState()
        {
            return currentState;
        }

        public float GetDetectionRange()
        {
            return detectionRange;
        }

        public bool IsPlayerDetected()
        {
            return currentState == AIState.Alert || currentState == AIState.Chase;
        }

        public Vector3 GetCurrentDestination()
        {
            return (agent != null && agent.isOnNavMesh) ? agent.destination : transform.position;
        }

        [ContextMenu("Try Place On NavMesh")]
        private void TryPlaceOnNavMesh()
        {
            if (agent == null) return;

            // Try to find a nearby NavMesh position
            if (NavMesh.SamplePosition(transform.position, out NavMeshHit hit, 5.0f, NavMesh.AllAreas))
            {
                // Move the agent to the valid NavMesh position
                transform.position = hit.position;
                Debug.Log($"[EnemyAIController] Placed {gameObject.name} on NavMesh at {hit.position}");
            }
            else
            {
                Debug.LogWarning($"[EnemyAIController] Could not place {gameObject.name} on NavMesh. No valid NavMesh found within 5 units.");
                Debug.LogWarning("[EnemyAIController] Please ensure NavMesh is baked and covers the enemy spawn area.");
            }
        }

        [ContextMenu("Check NavMesh Status")]
        private void CheckNavMeshStatus()
        {
            if (agent == null)
            {
                Debug.LogWarning($"[EnemyAIController] {gameObject.name} has no NavMeshAgent component");
                return;
            }

            Debug.Log($"[EnemyAIController] {gameObject.name} NavMesh Status:");
            Debug.Log($"  - Is on NavMesh: {agent.isOnNavMesh}");
            Debug.Log($"  - Position: {transform.position}");

            if (agent.isOnNavMesh)
            {
                Debug.Log($"  - Current destination: {agent.destination}");
                Debug.Log($"  - Remaining distance: {agent.remainingDistance}");
                Debug.Log($"  - Path pending: {agent.pathPending}");
            }
        }

        private void UpdateStateVisualization()
        {
            if (!showStateIndicator || enemyRenderer == null) return;

            Color stateColor = Color.white;
            switch (currentState)
            {
                case AIState.Patrol:
                    stateColor = Color.green;
                    break;
                case AIState.Alert:
                    stateColor = Color.yellow;
                    break;
                case AIState.Search:
                    stateColor = new Color(1f, 0.5f, 0f); // Orange
                    break;
                case AIState.Chase:
                    stateColor = Color.red;
                    break;
            }

            // Create a new material with the state color
            if (originalMaterial != null)
            {
                Material stateMaterial = new Material(originalMaterial);
                stateMaterial.color = stateColor;
                enemyRenderer.material = stateMaterial;
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Draw patrol route
            if (patrolPoints != null && patrolPoints.Length > 1)
            {
                for (int i = 0; i < patrolPoints.Length; i++)
                {
                    Gizmos.color = Color.blue;
                    Gizmos.DrawWireSphere(patrolPoints[i].position, 0.5f);
                    
                    if (i < patrolPoints.Length - 1)
                    {
                        Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[i + 1].position);
                    }
                    else
                    {
                        Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[0].position);
                    }
                }
            }
            
            // Draw detection range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
        }
    }
}

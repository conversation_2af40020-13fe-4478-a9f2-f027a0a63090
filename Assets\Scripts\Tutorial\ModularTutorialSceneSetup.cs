using UnityEngine;
using AshesOfTheGrove.GameSystems;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Complete tutorial scene setup using the new modular architecture
    /// Sets up all systems, builds zones, and initializes the tutorial experience
    /// </summary>
    public class ModularTutorialSceneSetup : MonoBehaviour
    {
        [Header("Scene Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool clearExistingObjects = true;
        
        [Header("System Prefabs")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private GameObject inputManagerPrefab;
        [SerializeField] private GameObject cameraControllerPrefab;
        
        [Header("Zone Configuration")]
        [SerializeField] private TutorialZoneConfigurations zoneConfigurations;
        [SerializeField] private bool createZoneConfigurationAsset = true;
        
        [Header("Tutorial Settings")]
        [SerializeField] private bool autoStartTutorial = true;
        [SerializeField] private bool enableDebugMode = false;
        
        // System References
        private GameObject player;
        private InputManager inputManager;
        private CameraController cameraController;
        private TutorialZoneManager zoneManager;
        private TutorialZoneBuilder zoneBuilder;
        private TutorialUI tutorialUI;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupTutorialScene();
            }
        }
        
        [ContextMenu("Setup Tutorial Scene")]
        public void SetupTutorialScene()
        {
            Debug.Log("[ModularTutorialSceneSetup] Starting tutorial scene setup...");
            
            if (clearExistingObjects)
            {
                ClearExistingTutorialObjects();
            }
            
            // Step 1: Setup core systems
            SetupCoreSystems();
            
            // Step 2: Setup zone configuration
            SetupZoneConfiguration();
            
            // Step 3: Build tutorial zones
            BuildTutorialZones();
            
            // Step 4: Setup zone management
            SetupZoneManagement();
            
            // Step 5: Setup UI system
            SetupUISystem();
            
            // Step 6: Initialize tutorial
            InitializeTutorial();
            
            Debug.Log("[ModularTutorialSceneSetup] Tutorial scene setup complete!");
        }
        
        private void ClearExistingTutorialObjects()
        {
            // Clear existing tutorial objects
            GameObject[] existingZones = GameObject.FindGameObjectsWithTag("TutorialZone");
            foreach (var zone in existingZones)
            {
                DestroyImmediate(zone);
            }
            
            // Clear existing enemies, obstacles, etc.
            string[] tagsToClean = { "Enemy", "Obstacle", "HidingSpot", "Objective" };
            foreach (string tag in tagsToClean)
            {
                GameObject[] objects = GameObject.FindGameObjectsWithTag(tag);
                foreach (var obj in objects)
                {
                    DestroyImmediate(obj);
                }
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Cleared existing tutorial objects");
        }
        
        private void SetupCoreSystems()
        {
            // Setup Player
            SetupPlayer();
            
            // Setup Input Manager
            SetupInputManager();
            
            // Setup Camera Controller
            SetupCameraController();
            
            Debug.Log("[ModularTutorialSceneSetup] Core systems setup complete");
        }
        
        private void SetupPlayer()
        {
            player = GameObject.FindGameObjectWithTag("Player");
            
            if (player == null)
            {
                if (playerPrefab != null)
                {
                    player = Instantiate(playerPrefab, Vector3.zero, Quaternion.identity);
                }
                else
                {
                    // Create basic player
                    player = CreateBasicPlayer();
                }
                
                player.tag = "Player";
                player.name = "Player";
            }
            
            // Ensure player has modular controller
            PlayerControllerModular modularController = player.GetComponent<PlayerControllerModular>();
            if (modularController == null)
            {
                // Remove old controller if exists
                PlayerController oldController = player.GetComponent<PlayerController>();
                if (oldController != null)
                {
                    DestroyImmediate(oldController);
                }
                
                // Add new modular controller
                player.AddComponent<PlayerControllerModular>();
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Player setup complete");
        }
        
        private GameObject CreateBasicPlayer()
        {
            GameObject playerObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            playerObj.transform.position = Vector3.zero;
            
            // Add Rigidbody
            Rigidbody rb = playerObj.AddComponent<Rigidbody>();
            rb.freezeRotation = true;
            
            // Set material
            Renderer renderer = playerObj.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = Color.blue;
            }
            
            return playerObj;
        }
        
        private void SetupInputManager()
        {
            inputManager = FindObjectOfType<InputManager>();
            
            if (inputManager == null)
            {
                if (inputManagerPrefab != null)
                {
                    GameObject inputObj = Instantiate(inputManagerPrefab);
                    inputManager = inputObj.GetComponent<InputManager>();
                }
                else
                {
                    // Create input manager
                    GameObject inputObj = new GameObject("InputManager");
                    inputManager = inputObj.AddComponent<InputManager>();
                }
            }
            
            if (enableDebugMode)
            {
                inputManager.EnableDebugInput(true);
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Input Manager setup complete");
        }
        
        private void SetupCameraController()
        {
            cameraController = FindObjectOfType<CameraController>();
            
            if (cameraController == null)
            {
                Camera mainCamera = Camera.main;
                if (mainCamera == null)
                {
                    // Create camera
                    GameObject cameraObj = new GameObject("Main Camera");
                    mainCamera = cameraObj.AddComponent<Camera>();
                    cameraObj.tag = "MainCamera";
                }
                
                if (cameraControllerPrefab != null)
                {
                    GameObject controllerObj = Instantiate(cameraControllerPrefab);
                    cameraController = controllerObj.GetComponent<CameraController>();
                }
                else
                {
                    cameraController = mainCamera.gameObject.AddComponent<CameraController>();
                }
            }
            
            // Set camera target to player
            if (player != null)
            {
                cameraController.SetTarget(player.transform);
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Camera Controller setup complete");
        }
        
        private void SetupZoneConfiguration()
        {
            if (zoneConfigurations == null && createZoneConfigurationAsset)
            {
                // Create zone configuration asset
                zoneConfigurations = ScriptableObject.CreateInstance<TutorialZoneConfigurations>();
                
#if UNITY_EDITOR
                // Save as asset
                string assetPath = "Assets/ScriptableObjects/TutorialZoneConfigurations.asset";
                UnityEditor.AssetDatabase.CreateAsset(zoneConfigurations, assetPath);
                UnityEditor.AssetDatabase.SaveAssets();
                Debug.Log($"[ModularTutorialSceneSetup] Created zone configuration asset at: {assetPath}");
#endif
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Zone configuration setup complete");
        }
        
        private void BuildTutorialZones()
        {
            // Setup zone builder
            zoneBuilder = GetComponent<TutorialZoneBuilder>();
            if (zoneBuilder == null)
            {
                zoneBuilder = gameObject.AddComponent<TutorialZoneBuilder>();
            }
            
            // Build all zones
            if (zoneConfigurations != null)
            {
                zoneBuilder.BuildAllZones(zoneConfigurations);
            }
            else
            {
                Debug.LogWarning("[ModularTutorialSceneSetup] No zone configurations found. Cannot build zones.");
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Tutorial zones built");
        }
        
        private void SetupZoneManagement()
        {
            // Setup zone manager
            zoneManager = GetComponent<TutorialZoneManager>();
            if (zoneManager == null)
            {
                zoneManager = gameObject.AddComponent<TutorialZoneManager>();
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Zone management setup complete");
        }
        
        private void SetupUISystem()
        {
            tutorialUI = FindObjectOfType<TutorialUI>();
            if (tutorialUI == null)
            {
                GameObject uiObj = new GameObject("TutorialUI");
                tutorialUI = uiObj.AddComponent<TutorialUI>();
            }
            
            Debug.Log("[ModularTutorialSceneSetup] UI system setup complete");
        }
        
        private void InitializeTutorial()
        {
            if (autoStartTutorial && zoneManager != null)
            {
                // Start tutorial after a brief delay
                Invoke(nameof(StartTutorial), 1f);
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Tutorial initialization complete");
        }
        
        private void StartTutorial()
        {
            if (zoneManager != null)
            {
                zoneManager.StartTutorial();
            }
        }
        
        // Public utility methods
        public void RestartTutorial()
        {
            if (zoneManager != null)
            {
                zoneManager.RestartTutorial();
            }
        }
        
        public void SkipToZone(int zoneIndex)
        {
            if (zoneManager != null)
            {
                zoneManager.SkipToZone(zoneIndex);
            }
        }
        
        public void ToggleDebugMode()
        {
            enableDebugMode = !enableDebugMode;
            
            if (inputManager != null)
            {
                inputManager.EnableDebugInput(enableDebugMode);
            }
            
            Debug.Log($"[ModularTutorialSceneSetup] Debug mode: {enableDebugMode}");
        }
        
        // Editor utilities
#if UNITY_EDITOR
        [ContextMenu("Clear Tutorial Scene")]
        public void ClearTutorialScene()
        {
            ClearExistingTutorialObjects();
            
            // Also clear built zone objects
            if (zoneBuilder != null)
            {
                zoneBuilder.ClearExistingZones();
            }
            
            Debug.Log("[ModularTutorialSceneSetup] Tutorial scene cleared");
        }
        
        [ContextMenu("Rebuild Zones Only")]
        public void RebuildZonesOnly()
        {
            if (zoneBuilder != null && zoneConfigurations != null)
            {
                zoneBuilder.ClearExistingZones();
                zoneBuilder.BuildAllZones(zoneConfigurations);
                Debug.Log("[ModularTutorialSceneSetup] Zones rebuilt");
            }
        }
#endif
        
        // Debug info
        private void OnGUI()
        {
            if (!enableDebugMode) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("Tutorial Debug Info", GUI.skin.box);
            
            if (zoneManager != null)
            {
                GUILayout.Label($"Current Zone: {zoneManager.CurrentZoneIndex + 1}/{zoneManager.TotalZones}");
                GUILayout.Label($"Progress: {zoneManager.GetTutorialProgress() * 100:F0}%");
                
                if (GUILayout.Button("Restart Tutorial"))
                {
                    RestartTutorial();
                }
                
                if (GUILayout.Button("Skip to Next Zone"))
                {
                    SkipToZone(zoneManager.CurrentZoneIndex + 1);
                }
            }
            
            GUILayout.EndArea();
        }
    }
}

Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.17f1 (4fc78088f837) revision 5228416'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65262 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.17f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
-logFile
Logs/AssetImportWorker1.log
-srvPort
56828
Successfully changed project path to: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10013).
Player connection [38080] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2310130998 [EditorId] 2310130998 [Version] 1048832 [Id] WindowsEditor(7,zoctopus-pc) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with 31 workers.
Refreshing native plugins compatible for Editor in 7.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.17f1 (4fc78088f837)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.17f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.17f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.17f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.17f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56728
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.17f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003171 seconds.
- Loaded All Assemblies, in  0.191 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.162 seconds
Domain Reload Profiling: 352ms
	BeginReloadAssembly (57ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (75ms)
		LoadAssemblies (56ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (74ms)
			TypeCache.Refresh (73ms)
				TypeCache.ScanAssembly (66ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (162ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (129ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (87ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.404 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.402 seconds
Domain Reload Profiling: 805ms
	BeginReloadAssembly (83ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (17ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (134ms)
			TypeCache.Refresh (114ms)
				TypeCache.ScanAssembly (105ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (403ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (44ms)
			ProcessInitializeOnLoadAttributes (226ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 4.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5495 Unused Serialized files (Serialized files now loaded: 0)
Unloading 90 unused Assets / (0.8 MB). Loaded Objects now: 5948.
Memory consumption went from 204.5 MB to 203.7 MB.
Total: 3.368800 ms (FindLiveObjects: 0.415000 ms CreateObjectMapping: 0.083600 ms MarkObjects: 2.539200 ms  DeleteObjects: 0.330400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 62b5816608c3102f162a1797e29626cf -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 38648.134852 seconds.
  path: Assets/Scripts/Testing/InputTester.cs
  artifactKey: Guid(3b52145e5d988e04c81ab94449687e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Testing/InputTester.cs using Guid(3b52145e5d988e04c81ab94449687e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb189917c8e584b88ed0580c99c0aec8') in 0.001871 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0

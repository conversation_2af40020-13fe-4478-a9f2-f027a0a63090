using UnityEngine;
using UnityEngine.InputSystem;

namespace AshesOfTheGrove.Player
{
    /// <summary>
    /// Main player controller for isometric stealth gameplay
    /// Handles movement, stealth mechanics, and player interactions
    /// </summary>
    public class PlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float moveSpeed = 15f;  // Good walking speed
        [SerializeField] private float crouchSpeed = 8f;  // Slower when crouching
        [SerializeField] private float runSpeed = 25f;   // Fast running speed
        
        [Header("Stealth Settings")]
        [SerializeField] private bool isCrouching = false;
        [SerializeField] private bool isRunning = false;
        [SerializeField] private bool isHiding = false;
        [SerializeField] private float noiseLevel = 1f;
        
        private Rigidbody rb;
        private Animator animator;
        private Vector3 movement;
        
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            animator = GetComponent<Animator>();

            // Optional: Log if animator is missing (useful for development)
            if (animator == null)
            {
                Debug.Log($"[PlayerController] No Animator component found on {gameObject.name}. Animation features will be disabled.");
            }
        }
        
        private void Update()
        {
            HandleInput();
            HandleMovement();
            HandleStealth();
        }
        
        private void HandleInput()
        {
            // Use new Input System consistently for all inputs
            if (Keyboard.current != null)
            {
                // Get movement input using new Input System
                Vector2 moveInput = Vector2.zero;

                if (Keyboard.current.wKey.isPressed) moveInput.y += 1f;
                if (Keyboard.current.sKey.isPressed) moveInput.y -= 1f;
                if (Keyboard.current.aKey.isPressed) moveInput.x -= 1f;
                if (Keyboard.current.dKey.isPressed) moveInput.x += 1f;

                // Also support arrow keys
                if (Keyboard.current.upArrowKey.isPressed) moveInput.y += 1f;
                if (Keyboard.current.downArrowKey.isPressed) moveInput.y -= 1f;
                if (Keyboard.current.leftArrowKey.isPressed) moveInput.x -= 1f;
                if (Keyboard.current.rightArrowKey.isPressed) moveInput.x += 1f;

                // Convert to isometric movement (don't normalize to preserve input magnitude)
                movement = new Vector3(moveInput.x, 0, moveInput.y);
                // Clamp magnitude to 1 for diagonal movement
                if (movement.magnitude > 1f)
                {
                    movement = movement.normalized;
                }

                // Stealth inputs
                if (Keyboard.current.leftCtrlKey.wasPressedThisFrame)
                {
                    ToggleCrouch();
                    Debug.Log($"[PlayerController] Crouch toggled - Now crouching: {isCrouching}");
                }

                // Run input handling
                HandleRunInput();
            }
            else
            {
                // Fallback to legacy Input system if new Input System is not available
                float horizontal = Input.GetAxis("Horizontal");
                float vertical = Input.GetAxis("Vertical");
                movement = new Vector3(horizontal, 0, vertical);
                // Input.GetAxis already provides proper magnitude, no need to normalize
            }
        }
        
        private void HandleMovement()
        {
            if (movement.magnitude > 0.1f)
            {
                float currentSpeed = GetCurrentSpeed();
                Vector3 moveDirection = movement * currentSpeed * Time.deltaTime;
                
                rb.MovePosition(transform.position + moveDirection);
                
                // Rotate player to face movement direction
                transform.rotation = Quaternion.LookRotation(movement);
                
                // Update noise level based on movement
                UpdateNoiseLevel();
            }
        }
        
        private void HandleStealth()
        {
            // Update animator parameters (only if animator exists)
            if (animator != null)
            {
                animator.SetBool("IsCrouching", isCrouching);
                animator.SetFloat("Speed", movement.magnitude);
            }
        }
        
        private void HandleRunInput()
        {
            if (Keyboard.current != null)
            {
                bool runKeyPressed = Keyboard.current.leftShiftKey.isPressed;

                // Can't run while crouching
                if (isCrouching && runKeyPressed)
                {
                    runKeyPressed = false;
                }

                // Update running state
                if (runKeyPressed != isRunning)
                {
                    isRunning = runKeyPressed;
                    UpdateRunVisuals();

                    if (isRunning)
                    {
                        Debug.Log("[PlayerController] Started running");
                    }
                    else
                    {
                        Debug.Log("[PlayerController] Stopped running");
                    }
                }
            }
        }

        private float GetCurrentSpeed()
        {
            if (isCrouching) return crouchSpeed;
            if (isRunning) return runSpeed;
            return moveSpeed;
        }
        
        private void ToggleCrouch()
        {
            isCrouching = !isCrouching;

            // Stop running when crouching
            if (isCrouching && isRunning)
            {
                isRunning = false;
                UpdateRunVisuals();
            }

            UpdateCrouchVisuals();
        }

        private void UpdateCrouchVisuals()
        {
            // Visual feedback for crouching
            if (isCrouching)
            {
                transform.localScale = new Vector3(1f, 0.7f, 1f); // Make player shorter when crouching
            }
            else
            {
                transform.localScale = Vector3.one; // Return to normal height
            }
        }

        private void UpdateRunVisuals()
        {
            // Visual feedback for running - could add particle effects, animation speed, etc.
            // For now, we'll just update the scale slightly to show urgency
            if (isRunning && !isCrouching)
            {
                // Slightly lean forward when running
                transform.localScale = new Vector3(1f, 1f, 1.1f);
            }
            else if (!isCrouching)
            {
                transform.localScale = Vector3.one;
            }
        }
        
        private void UpdateNoiseLevel()
        {
            if (isCrouching)
                noiseLevel = 0.3f;  // Very quiet when crouching
            else if (isRunning)
                noiseLevel = 2.5f;  // Loud when running
            else
                noiseLevel = 1f;    // Normal noise when walking
        }
        
        public float GetNoiseLevel()
        {
            return noiseLevel;
        }
        
        public bool IsCrouching()
        {
            return isCrouching;
        }

        public bool IsRunning()
        {
            return isRunning;
        }
        
        public bool IsHiding()
        {
            return isHiding;
        }
        
        public void SetHiding(bool hiding)
        {
            isHiding = hiding;
        }
    }
}

using UnityEngine;

namespace AshesOfTheGrove.Player
{
    /// <summary>
    /// Main player controller for isometric stealth gameplay
    /// Handles movement, stealth mechanics, and player interactions
    /// </summary>
    public class PlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float crouchSpeed = 2f;
        [SerializeField] private float runSpeed = 8f;
        
        [<PERSON><PERSON>("Stealth Settings")]
        [SerializeField] private bool isCrouching = false;
        [SerializeField] private bool isHiding = false;
        [SerializeField] private float noiseLevel = 1f;
        
        private Rigidbody rb;
        private Animator animator;
        private Vector3 movement;
        
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            animator = GetComponent<Animator>();

            // Optional: Log if animator is missing (useful for development)
            if (animator == null)
            {
                Debug.Log($"[PlayerController] No Animator component found on {gameObject.name}. Animation features will be disabled.");
            }
        }
        
        private void Update()
        {
            HandleInput();
            HandleMovement();
            HandleStealth();
        }
        
        private void HandleInput()
        {
            // Get input for isometric movement
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            // Convert to isometric movement
            movement = new Vector3(horizontal, 0, vertical).normalized;
            
            // Stealth inputs
            if (Input.GetKeyDown(KeyCode.LeftControl))
            {
                ToggleCrouch();
            }
        }
        
        private void HandleMovement()
        {
            if (movement.magnitude > 0.1f)
            {
                float currentSpeed = GetCurrentSpeed();
                Vector3 moveDirection = movement * currentSpeed * Time.deltaTime;
                
                rb.MovePosition(transform.position + moveDirection);
                
                // Rotate player to face movement direction
                transform.rotation = Quaternion.LookRotation(movement);
                
                // Update noise level based on movement
                UpdateNoiseLevel();
            }
        }
        
        private void HandleStealth()
        {
            // Update animator parameters (only if animator exists)
            if (animator != null)
            {
                animator.SetBool("IsCrouching", isCrouching);
                animator.SetFloat("Speed", movement.magnitude);
            }
        }
        
        private float GetCurrentSpeed()
        {
            if (isCrouching) return crouchSpeed;
            if (Input.GetKey(KeyCode.LeftShift)) return runSpeed;
            return moveSpeed;
        }
        
        private void ToggleCrouch()
        {
            isCrouching = !isCrouching;
        }
        
        private void UpdateNoiseLevel()
        {
            if (isCrouching)
                noiseLevel = 0.3f;
            else if (Input.GetKey(KeyCode.LeftShift))
                noiseLevel = 2f;
            else
                noiseLevel = 1f;
        }
        
        public float GetNoiseLevel()
        {
            return noiseLevel;
        }
        
        public bool IsCrouching()
        {
            return isCrouching;
        }
        
        public bool IsHiding()
        {
            return isHiding;
        }
        
        public void SetHiding(bool hiding)
        {
            isHiding = hiding;
        }
    }
}

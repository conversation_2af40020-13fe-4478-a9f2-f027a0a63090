using UnityEngine;
using UnityEngine.AI;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.AI;
#endif

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Helper script to assist with NavMesh setup for the tutorial system
    /// </summary>
    public class NavMeshSetupHelper : MonoBehaviour
    {
        [Header("NavMesh Setup Helper")]
        [SerializeField] private bool showInstructions = true;
        [SerializeField] private bool autoMarkStaticObjects = true;
        [SerializeField] private bool autoBakeNavMesh = true;

        [Header("Debug Info")]
        [SerializeField] private bool showNavMeshStatus = true;
        
        private void Start()
        {
            if (showInstructions)
            {
                ShowSetupInstructions();
            }
            
            if (autoMarkStaticObjects)
            {
                MarkObjectsForNavMesh();
            }
            
            if (showNavMeshStatus)
            {
                CheckNavMeshStatus();
            }

            // Try automatic NavMesh baking if enabled and no NavMesh exists
            if (autoBakeNavMesh)
            {
                TryAutoBakeNavMesh();
            }
        }
        
        private void ShowSetupInstructions()
        {
            Debug.Log("=== NAVMESH SETUP INSTRUCTIONS ===");
            Debug.Log("To enable AI navigation in the tutorial:");
            Debug.Log("1. Open Window > AI > Navigation");
            Debug.Log("2. Select the Ground object in the scene");
            Debug.Log("3. In the Navigation window, check 'Navigation Static'");
            Debug.Log("4. Select any Wall or Cover objects and mark them as 'Navigation Static' too");
            Debug.Log("5. Go to the 'Bake' tab in the Navigation window");
            Debug.Log("6. Click 'Bake' to generate the NavMesh");
            Debug.Log("7. You should see blue areas indicating walkable NavMesh");
            Debug.Log("================================");
        }
        
        private void MarkObjectsForNavMesh()
        {
            // This method provides guidance but cannot actually set static flags at runtime
            Debug.Log("[NavMeshSetupHelper] Looking for objects that should be marked as Navigation Static:");
            
            GameObject[] allObjects = FindObjectsOfType<GameObject>();
            int groundObjects = 0;
            int wallObjects = 0;
            int coverObjects = 0;
            
            foreach (GameObject obj in allObjects)
            {
                if (obj.name.ToLower().Contains("ground"))
                {
                    groundObjects++;
                    Debug.Log($"[NavMeshSetupHelper] Found ground object: {obj.name} - Should be marked Navigation Static");
                }
                else if (obj.name.ToLower().Contains("wall"))
                {
                    wallObjects++;
                    Debug.Log($"[NavMeshSetupHelper] Found wall object: {obj.name} - Should be marked Navigation Static");
                }
                else if (obj.name.ToLower().Contains("cover"))
                {
                    coverObjects++;
                    Debug.Log($"[NavMeshSetupHelper] Found cover object: {obj.name} - Should be marked Navigation Static");
                }
            }
            
            Debug.Log($"[NavMeshSetupHelper] Summary: {groundObjects} ground, {wallObjects} walls, {coverObjects} cover objects found");
            Debug.Log("[NavMeshSetupHelper] Please mark these objects as 'Navigation Static' in the Inspector");
        }
        
        private void CheckNavMeshStatus()
        {
            // Check if NavMesh exists in the scene
            NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
            
            if (triangulation.vertices.Length == 0)
            {
                Debug.LogWarning("[NavMeshSetupHelper] No NavMesh found in scene!");
                Debug.LogWarning("[NavMeshSetupHelper] AI enemies will not be able to navigate properly");
                Debug.LogWarning("[NavMeshSetupHelper] Please follow the setup instructions above");
            }
            else
            {
                Debug.Log($"[NavMeshSetupHelper] NavMesh found! {triangulation.vertices.Length} vertices, {triangulation.indices.Length/3} triangles");
                Debug.Log("[NavMeshSetupHelper] AI navigation should work correctly");
            }
        }
        
        [ContextMenu("Check NavMesh Status")]
        public void ManualNavMeshCheck()
        {
            CheckNavMeshStatus();
        }
        
        [ContextMenu("Show Setup Instructions")]
        public void ManualShowInstructions()
        {
            ShowSetupInstructions();
        }
        
        private void OnGUI()
        {
            if (!showInstructions) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 500, 200));
            GUILayout.Label("NavMesh Setup Required", GUI.skin.box);
            GUILayout.Label("For AI navigation to work:");
            GUILayout.Label("1. Window > AI > Navigation");
            GUILayout.Label("2. Select Ground/Wall objects");
            GUILayout.Label("3. Check 'Navigation Static'");
            GUILayout.Label("4. Click 'Bake' tab > 'Bake'");
            GUILayout.Label("5. Look for blue NavMesh areas");
            
            if (GUILayout.Button("Hide Instructions"))
            {
                showInstructions = false;
            }
            
            if (GUILayout.Button("Check NavMesh Status"))
            {
                CheckNavMeshStatus();
            }

            if (GUILayout.Button("Auto Bake NavMesh"))
            {
                TryAutoBakeNavMesh();
            }
            
            GUILayout.EndArea();
        }

        private void TryAutoBakeNavMesh()
        {
#if UNITY_EDITOR
            // Check if NavMesh already exists
            NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();

            if (triangulation.vertices.Length == 0)
            {
                Debug.Log("[NavMeshSetupHelper] No NavMesh found. Attempting automatic baking...");

                // Try to bake NavMesh automatically
                try
                {
                    UnityEditor.AI.NavMeshBuilder.BuildNavMesh();
                    Debug.Log("[NavMeshSetupHelper] NavMesh baked successfully!");

                    // Check again after baking
                    triangulation = NavMesh.CalculateTriangulation();
                    if (triangulation.vertices.Length > 0)
                    {
                        Debug.Log($"[NavMeshSetupHelper] NavMesh verification: {triangulation.vertices.Length} vertices, {triangulation.indices.Length/3} triangles");
                    }
                    else
                    {
                        Debug.LogWarning("[NavMeshSetupHelper] NavMesh baking completed but no walkable areas found. Check that objects are marked as Navigation Static.");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"[NavMeshSetupHelper] Automatic NavMesh baking failed: {e.Message}");
                    Debug.LogWarning("[NavMeshSetupHelper] Please bake NavMesh manually using Window > AI > Navigation");
                }
            }
            else
            {
                Debug.Log("[NavMeshSetupHelper] NavMesh already exists, skipping automatic baking");
            }
#else
            Debug.LogWarning("[NavMeshSetupHelper] Automatic NavMesh baking only works in the Unity Editor");
#endif
        }

        [ContextMenu("Try Auto Bake NavMesh")]
        public void ManualAutoBake()
        {
            TryAutoBakeNavMesh();
        }
    }
}

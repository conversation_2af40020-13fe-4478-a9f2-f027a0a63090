using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections;
using System.Collections.Generic;
using AshesOfTheGrove.Tutorial;

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Validation script to test tutorial system functionality
    /// </summary>
    public class TutorialSystemValidator : MonoBehaviour
    {
        [Header("Validation Settings")]
        [SerializeField] private bool runValidationOnStart = true;
        [SerializeField] private bool logDetailedResults = true;
        
        [Header("Component References")]
        [SerializeField] private TutorialZoneManager tutorialManager;
        [SerializeField] private TutorialUI tutorialUI;
        [SerializeField] private TutorialZoneBuilder sceneBuilder;
        
        private List<string> validationResults = new List<string>();
        private bool validationComplete = false;
        
        void Start()
        {
            if (runValidationOnStart)
            {
                StartCoroutine(ValidateTutorialSystem());
            }
        }
        
        void Update()
        {
            // Press V to run validation manually
            if (Keyboard.current != null && Keyboard.current.vKey.wasPressedThisFrame)
            {
                StartCoroutine(ValidateTutorialSystem());
            }

            // Press L to show validation log
            if (Keyboard.current != null && Keyboard.current.lKey.wasPressedThisFrame && validationComplete)
            {
                ShowValidationResults();
            }
        }
        
        private IEnumerator ValidateTutorialSystem()
        {
            validationResults.Clear();
            validationComplete = false;
            
            Debug.Log("[TutorialValidator] Starting tutorial system validation...");
            
            // Test 1: Component Discovery
            yield return StartCoroutine(ValidateComponentDiscovery());
            
            // Test 2: Scene Building
            yield return StartCoroutine(ValidateSceneBuilding());
            
            // Test 3: Tutorial Zone Manager
            yield return StartCoroutine(ValidateTutorialZoneManager());
            
            // Test 4: UI System
            yield return StartCoroutine(ValidateUISystem());
            
            // Test 5: AI Integration
            yield return StartCoroutine(ValidateAIIntegration());
            
            validationComplete = true;
            ShowValidationResults();
        }
        
        private IEnumerator ValidateComponentDiscovery()
        {
            validationResults.Add("=== Component Discovery Test ===");
            
            // Find tutorial components
            tutorialManager = FindObjectOfType<TutorialZoneManager>();
            tutorialUI = FindObjectOfType<TutorialUI>();
            sceneBuilder = FindObjectOfType<TutorialZoneBuilder>();

            var tutorialDemo = FindObjectOfType<TutorialDemo>();
            var sceneSetup = FindObjectOfType<ModularTutorialSceneSetup>();

            // Validate components
            AddResult("TutorialZoneManager", tutorialManager != null);
            AddResult("TutorialUI", tutorialUI != null);
            AddResult("TutorialZoneBuilder", sceneBuilder != null);
            AddResult("TutorialDemo", tutorialDemo != null);
            AddResult("ModularTutorialSceneSetup", sceneSetup != null);
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateSceneBuilding()
        {
            validationResults.Add("\n=== Scene Building Test ===");
            
            // Check if scene has been built
            var player = GameObject.FindWithTag("Player");
            var enemies = GameObject.FindGameObjectsWithTag("Enemy");
            var walls = GameObject.FindGameObjectsWithTag("Wall");
            
            AddResult("Player Created", player != null);
            AddResult("Enemies Created", enemies != null && enemies.Length > 0);
            AddResult("Environment Created", walls != null && walls.Length > 0);
            
            // Check for interactive elements
            var hidingSpots = FindObjectsOfType<HidingSpot>();
            var objectives = FindObjectsOfType<ObjectiveMarker>();
            
            AddResult("Hiding Spots Created", hidingSpots != null && hidingSpots.Length > 0);
            AddResult("Objectives Created", objectives != null && objectives.Length > 0);
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateTutorialZoneManager()
        {
            validationResults.Add("\n=== Tutorial Zone Manager Test ===");

            if (tutorialManager != null)
            {
                // Check tutorial zones
                var zoneConfigs = FindObjectOfType<TutorialZoneConfigurations>();
                AddResult("Zone Configurations Available", zoneConfigs != null);

                if (zoneConfigs != null)
                {
                    // Check if zones are configured
                    AddResult("Tutorial Zones Configured", true); // Zone configs exist
                }

                // Check if tutorial can be started
                AddResult("Tutorial Zone Manager Active", tutorialManager.gameObject.activeInHierarchy);
            }
            else
            {
                AddResult("Tutorial Zone Manager", false);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateUISystem()
        {
            validationResults.Add("\n=== UI System Test ===");
            
            if (tutorialUI != null)
            {
                AddResult("Tutorial UI Active", tutorialUI.gameObject.activeInHierarchy);
                
                // Test UI methods
                try
                {
                    tutorialUI.ShowWelcomeMessage();
                    AddResult("Welcome Message Method", true);
                    
                    tutorialUI.ShowInstruction("Test instruction");
                    AddResult("Show Instruction Method", true);
                }
                catch (System.Exception e)
                {
                    AddResult("UI Methods", false);
                    Debug.LogError($"UI Method Error: {e.Message}");
                }
            }
            else
            {
                AddResult("Tutorial UI", false);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateAIIntegration()
        {
            validationResults.Add("\n=== AI Integration Test ===");
            
            // Find AI controllers
            var aiControllers = FindObjectsOfType<AshesOfTheGrove.AI.EnemyAIController>();
            AddResult("AI Controllers Found", aiControllers != null && aiControllers.Length > 0);
            
            if (aiControllers != null && aiControllers.Length > 0)
            {
                foreach (var ai in aiControllers)
                {
                    // Test AI tutorial methods
                    try
                    {
                        ai.SetTutorialMode(true);
                        var state = ai.GetCurrentState();
                        AddResult($"AI {ai.name} Tutorial Mode", true);
                    }
                    catch (System.Exception e)
                    {
                        AddResult($"AI {ai.name} Tutorial Mode", false);
                        Debug.LogError($"AI Tutorial Error: {e.Message}");
                    }
                }
            }
            
            // Check Field of View components
            var fovComponents = FindObjectsOfType<AshesOfTheGrove.AI.FieldOfView>();
            AddResult("Field of View Components", fovComponents != null && fovComponents.Length > 0);
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private void AddResult(string testName, bool passed)
        {
            string result = passed ? "✓ PASS" : "✗ FAIL";
            string logEntry = $"{result}: {testName}";
            validationResults.Add(logEntry);
            
            if (logDetailedResults)
            {
                Debug.Log($"[TutorialValidator] {logEntry}");
            }
        }
        
        private void ShowValidationResults()
        {
            Debug.Log("[TutorialValidator] === VALIDATION COMPLETE ===");
            
            int passCount = 0;
            int totalCount = 0;
            
            foreach (string result in validationResults)
            {
                if (result.Contains("✓") || result.Contains("✗"))
                {
                    totalCount++;
                    if (result.Contains("✓"))
                        passCount++;
                }
                
                Debug.Log($"[TutorialValidator] {result}");
            }
            
            float successRate = totalCount > 0 ? (float)passCount / totalCount * 100f : 0f;
            Debug.Log($"[TutorialValidator] === SUMMARY: {passCount}/{totalCount} tests passed ({successRate:F1}%) ===");
            
            if (successRate >= 80f)
            {
                Debug.Log("[TutorialValidator] 🎉 Tutorial system validation SUCCESSFUL!");
            }
            else
            {
                Debug.LogWarning("[TutorialValidator] ⚠️ Tutorial system validation needs attention.");
            }
        }
        
        void OnGUI()
        {
            if (!validationComplete) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 100));
            GUILayout.Label("Tutorial System Validator", GUI.skin.box);
            
            if (GUILayout.Button("Run Validation (V)"))
            {
                StartCoroutine(ValidateTutorialSystem());
            }
            
            if (GUILayout.Button("Show Results (L)"))
            {
                ShowValidationResults();
            }
            
            GUILayout.EndArea();
        }
    }
}

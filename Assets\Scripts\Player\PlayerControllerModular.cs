using UnityEngine;
using AshesOfTheGrove.GameSystems;
using AshesOfTheGrove.Tutorial;

namespace AshesOfTheGrove.Player
{
    /// <summary>
    /// Modular player controller that uses the new GameSystems architecture
    /// Focuses on player-specific logic while delegating movement, input, and camera to dedicated systems
    /// </summary>
    public class PlayerControllerModular : MonoBehaviour
    {
        [Header("Stealth Settings")]
        [SerializeField] private bool isHiding = false;
        [SerializeField] private float baseNoiseLevel = 1f;
        [SerializeField] private float crouchNoiseMultiplier = 0.3f;
        [SerializeField] private float runNoiseMultiplier = 2f;
        
        [Header("Interaction Settings")]
        [SerializeField] private float interactionRange = 2f;
        [SerializeField] private LayerMask interactableLayer = -1;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        // Modular Systems (automatically found)
        private MovementController movementController;
        private InputManager inputManager;
        
        // Player State
        private float currentNoiseLevel;
        private bool isInteracting = false;
        
        // Events
        public System.Action<float> OnNoiseLevelChanged;
        public System.Action<bool> OnHidingStateChanged;
        public System.Action<GameObject> OnInteractionAvailable;
        public System.Action OnInteractionUnavailable;
        
        // Properties
        public bool IsCrouching => movementController != null ? movementController.IsCrouching : false;
        public bool IsRunning => movementController != null ? movementController.IsRunning : false;
        public bool IsMoving => movementController != null ? movementController.IsMoving : false;
        public bool IsHiding => isHiding;
        public float GetNoiseLevel() => currentNoiseLevel;
        
        private void Awake()
        {
            InitializeSystems();
        }
        
        private void Start()
        {
            SetupEventListeners();
        }
        
        private void Update()
        {
            UpdatePlayerState();
            HandleInteractions();
            
            if (showDebugInfo)
            {
                DebugPlayerState();
            }
        }
        
        private void InitializeSystems()
        {
            // Find or add MovementController
            movementController = GetComponent<MovementController>();
            if (movementController == null)
            {
                movementController = gameObject.AddComponent<MovementController>();
                Debug.Log("[PlayerControllerModular] Added MovementController component");
            }
            
            // Find InputManager in scene
            inputManager = FindObjectOfType<InputManager>();
            if (inputManager == null)
            {
                Debug.LogWarning("[PlayerControllerModular] No InputManager found in scene. Creating one.");
                GameObject inputManagerObj = new GameObject("InputManager");
                inputManager = inputManagerObj.AddComponent<InputManager>();
            }
            
            Debug.Log("[PlayerControllerModular] Systems initialized");
        }
        
        private void SetupEventListeners()
        {
            if (movementController != null)
            {
                movementController.OnCrouchStateChanged += OnCrouchStateChanged;
                movementController.OnRunStateChanged += OnRunStateChanged;
                movementController.OnMovementChanged += OnMovementChanged;
            }
            
            if (inputManager != null)
            {
                inputManager.OnInteractPressed += HandleInteractionInput;
            }
        }
        
        private void UpdatePlayerState()
        {
            UpdateNoiseLevel();
            UpdateHidingState();
        }
        
        private void UpdateNoiseLevel()
        {
            float newNoiseLevel = baseNoiseLevel;
            
            if (movementController != null && movementController.IsMoving)
            {
                if (movementController.IsCrouching)
                {
                    newNoiseLevel *= crouchNoiseMultiplier;
                }
                else if (movementController.IsRunning)
                {
                    newNoiseLevel *= runNoiseMultiplier;
                }
            }
            else
            {
                newNoiseLevel = 0f; // No noise when not moving
            }
            
            if (Mathf.Abs(currentNoiseLevel - newNoiseLevel) > 0.1f)
            {
                currentNoiseLevel = newNoiseLevel;
                OnNoiseLevelChanged?.Invoke(currentNoiseLevel);
            }
        }
        
        private void UpdateHidingState()
        {
            // Check if player is in a hiding spot
            bool wasHiding = isHiding;
            isHiding = CheckForHidingSpot();
            
            if (wasHiding != isHiding)
            {
                OnHidingStateChanged?.Invoke(isHiding);
                Debug.Log($"[PlayerControllerModular] Hiding state changed: {isHiding}");
            }
        }
        
        private bool CheckForHidingSpot()
        {
            // Check for nearby hiding spots
            Collider[] hidingSpots = Physics.OverlapSphere(transform.position, 1f);
            foreach (var spot in hidingSpots)
            {
                if (spot.GetComponent<HidingSpot>() != null)
                {
                    return true;
                }
            }
            return false;
        }
        
        private void HandleInteractions()
        {
            if (inputManager == null) return;
            
            // Check for nearby interactables
            GameObject nearestInteractable = FindNearestInteractable();
            
            if (nearestInteractable != null && !isInteracting)
            {
                OnInteractionAvailable?.Invoke(nearestInteractable);
            }
            else if (nearestInteractable == null && !isInteracting)
            {
                OnInteractionUnavailable?.Invoke();
            }
        }
        
        private GameObject FindNearestInteractable()
        {
            Collider[] interactables = Physics.OverlapSphere(transform.position, interactionRange, interactableLayer);
            
            GameObject nearest = null;
            float nearestDistance = float.MaxValue;
            
            foreach (var interactable in interactables)
            {
                if (interactable.gameObject == gameObject) continue; // Skip self
                
                float distance = Vector3.Distance(transform.position, interactable.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearest = interactable.gameObject;
                }
            }
            
            return nearest;
        }
        
        private void HandleInteractionInput()
        {
            GameObject interactable = FindNearestInteractable();
            if (interactable != null)
            {
                // Try to interact with the object
                var interactableComponent = interactable.GetComponent<IInteractable>();
                if (interactableComponent != null)
                {
                    interactableComponent.Interact(gameObject);
                    Debug.Log($"[PlayerControllerModular] Interacted with {interactable.name}");
                }
            }
        }
        
        // Event handlers
        private void OnCrouchStateChanged(bool isCrouching)
        {
            Debug.Log($"[PlayerControllerModular] Crouch state changed: {isCrouching}");
        }
        
        private void OnRunStateChanged(bool isRunning)
        {
            Debug.Log($"[PlayerControllerModular] Run state changed: {isRunning}");
        }
        
        private void OnMovementChanged(Vector3 movement)
        {
            // Player-specific movement logic can go here
        }
        
        // Public methods for external systems
        public void SetHidingState(bool hiding)
        {
            if (isHiding != hiding)
            {
                isHiding = hiding;
                OnHidingStateChanged?.Invoke(isHiding);
            }
        }
        
        public void EnableMovement(bool enable)
        {
            if (movementController != null)
            {
                movementController.SetMovementEnabled(enable);
            }
        }
        
        public Vector3 GetVelocity()
        {
            return movementController != null ? movementController.GetVelocity() : Vector3.zero;
        }
        
        public Vector3 GetMovementDirection()
        {
            return movementController != null ? movementController.MovementDirection : Vector3.zero;
        }
        
        // Debug
        private void DebugPlayerState()
        {
            Debug.Log($"[PlayerControllerModular] State - Moving: {IsMoving}, Crouching: {IsCrouching}, Running: {IsRunning}, Hiding: {IsHiding}, Noise: {currentNoiseLevel:F1}");
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw interaction range
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(transform.position, interactionRange);
            
            // Draw hiding detection range
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, 1f);
        }
        
        private void OnDestroy()
        {
            // Clean up event listeners
            if (movementController != null)
            {
                movementController.OnCrouchStateChanged -= OnCrouchStateChanged;
                movementController.OnRunStateChanged -= OnRunStateChanged;
                movementController.OnMovementChanged -= OnMovementChanged;
            }
            
            if (inputManager != null)
            {
                inputManager.OnInteractPressed -= HandleInteractionInput;
            }
        }
    }
    
    // Interface for interactable objects
    public interface IInteractable
    {
        void Interact(GameObject interactor);
    }
}

using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing
{
    /// <summary>
    /// Helper script to quickly set up a test player with all required components
    /// </summary>
    [System.Serializable]
    public class PlayerSetupHelper : MonoBehaviour
    {
        [Header("Setup Options")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createVisualRepresentation = true;
        [SerializeField] private Material playerMaterial;
        
        [Head<PERSON>("Player Settings")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float crouchSpeed = 2f;
        [SerializeField] private float runSpeed = 8f;
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupPlayer();
            }
        }
        
        [ContextMenu("Setup Player")]
        public void SetupPlayer()
        {
            GameObject player = CreatePlayerGameObject();
            SetupPlayerComponents(player);
            SetupInputTester(player);
            
            Debug.Log("[PlayerSetupHelper] Player setup complete! Use WASD to move, Left Ctrl to crouch, Left Shift to run.");
        }
        
        private GameObject CreatePlayerGameObject()
        {
            // Check if player already exists
            GameObject existingPlayer = GameObject.FindGameObjectWithTag("Player");
            if (existingPlayer != null)
            {
                Debug.Log("[PlayerSetupHelper] Player already exists, updating existing player.");
                return existingPlayer;
            }
            
            // Create new player GameObject
            GameObject player = new GameObject("Player");
            player.tag = "Player";
            player.layer = LayerMask.NameToLayer("Player") != -1 ? LayerMask.NameToLayer("Player") : 0;
            
            // Position player at origin
            player.transform.position = Vector3.zero;
            
            return player;
        }
        
        private void SetupPlayerComponents(GameObject player)
        {
            // Add Rigidbody
            Rigidbody rb = player.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = player.AddComponent<Rigidbody>();
            }
            rb.mass = 1f;
            rb.drag = 5f; // Add some drag for better control
            rb.freezeRotation = true; // Prevent physics rotation
            
            // Add Collider
            CapsuleCollider collider = player.GetComponent<CapsuleCollider>();
            if (collider == null)
            {
                collider = player.AddComponent<CapsuleCollider>();
            }
            collider.height = 2f;
            collider.radius = 0.5f;
            collider.center = new Vector3(0, 1f, 0);
            
            // Add PlayerController
            PlayerController playerController = player.GetComponent<PlayerController>();
            if (playerController == null)
            {
                playerController = player.AddComponent<PlayerController>();
            }
            
            // Create visual representation
            if (createVisualRepresentation)
            {
                CreatePlayerVisual(player);
            }
            
            Debug.Log("[PlayerSetupHelper] Player components added successfully.");
        }
        
        private void CreatePlayerVisual(GameObject player)
        {
            // Check if visual already exists
            Transform existingVisual = player.transform.Find("PlayerVisual");
            if (existingVisual != null)
            {
                return; // Visual already exists
            }
            
            // Create visual container
            GameObject visual = new GameObject("PlayerVisual");
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = Vector3.zero;
            
            // Create body (capsule)
            GameObject body = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            body.name = "Body";
            body.transform.SetParent(visual.transform);
            body.transform.localPosition = new Vector3(0, 1f, 0);
            body.transform.localScale = new Vector3(0.8f, 1f, 0.8f);
            
            // Remove the collider from visual (we have one on the main player)
            Destroy(body.GetComponent<CapsuleCollider>());
            
            // Apply material if provided
            if (playerMaterial != null)
            {
                body.GetComponent<Renderer>().material = playerMaterial;
            }
            else
            {
                // Create a simple colored material
                Material mat = new Material(Shader.Find("Standard"));
                mat.color = Color.blue;
                body.GetComponent<Renderer>().material = mat;
            }
            
            // Create direction indicator (small cube in front)
            GameObject directionIndicator = GameObject.CreatePrimitive(PrimitiveType.Cube);
            directionIndicator.name = "DirectionIndicator";
            directionIndicator.transform.SetParent(visual.transform);
            directionIndicator.transform.localPosition = new Vector3(0, 1f, 0.6f);
            directionIndicator.transform.localScale = new Vector3(0.2f, 0.2f, 0.2f);
            
            // Remove collider and set color
            Destroy(directionIndicator.GetComponent<BoxCollider>());
            Material dirMat = new Material(Shader.Find("Standard"));
            dirMat.color = Color.red;
            directionIndicator.GetComponent<Renderer>().material = dirMat;
        }
        
        private void SetupInputTester(GameObject player)
        {
            // Add InputTester component
            InputTester inputTester = player.GetComponent<InputTester>();
            if (inputTester == null)
            {
                inputTester = player.AddComponent<InputTester>();
            }
            
            // The InputTester will automatically find the PlayerController
            Debug.Log("[PlayerSetupHelper] InputTester added. Press F1 in play mode to toggle debug info.");
        }
        
        [ContextMenu("Create Test Ground")]
        public void CreateTestGround()
        {
            // Create a simple ground plane for testing
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "TestGround";
            ground.transform.position = new Vector3(0, 0, 0);
            ground.transform.localScale = new Vector3(5, 1, 5); // 50x50 units
            
            // Set material
            Material groundMat = new Material(Shader.Find("Standard"));
            groundMat.color = Color.gray;
            ground.GetComponent<Renderer>().material = groundMat;
            
            Debug.Log("[PlayerSetupHelper] Test ground created.");
        }
        
        [ContextMenu("Setup Complete Test Scene")]
        public void SetupCompleteTestScene()
        {
            SetupPlayer();
            CreateTestGround();
            
            // Position camera for isometric view
            Camera mainCamera = Camera.main;
            if (mainCamera != null)
            {
                mainCamera.transform.position = new Vector3(0, 10, -10);
                mainCamera.transform.rotation = Quaternion.Euler(30, 0, 0);
                mainCamera.orthographic = true;
                mainCamera.orthographicSize = 10;
            }
            
            Debug.Log("[PlayerSetupHelper] Complete test scene setup finished!");
            Debug.Log("Controls: WASD/Arrow Keys = Move, Left Ctrl = Crouch, Left Shift = Run, F1 = Toggle Debug");
        }
    }
}

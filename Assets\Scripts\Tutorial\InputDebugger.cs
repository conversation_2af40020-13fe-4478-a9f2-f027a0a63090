using UnityEngine;
using UnityEngine.InputSystem;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Debug utility to help diagnose input conflicts and issues
    /// Press F1 to toggle debug display
    /// </summary>
    public class InputDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool showDebugOnStart = false;
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;
        
        private bool showDebug = false;
        private GUIStyle debugStyle;
        private string debugText = "";
        
        private void Start()
        {
            showDebug = showDebugOnStart;
            
            // Setup GUI style
            debugStyle = new GUIStyle();
            debugStyle.fontSize = 14;
            debugStyle.normal.textColor = Color.white;
            debugStyle.wordWrap = true;
        }
        
        private void Update()
        {
            // Toggle debug display
            if (Input.GetKeyDown(toggleKey))
            {
                showDebug = !showDebug;
                Debug.Log($"[InputDebugger] Debug display {(showDebug ? "enabled" : "disabled")}");
            }
            
            if (showDebug)
            {
                UpdateDebugInfo();
            }
        }
        
        private void UpdateDebugInfo()
        {
            debugText = "=== INPUT DEBUG INFO ===\n\n";
            
            // Legacy Input System
            debugText += "LEGACY INPUT SYSTEM:\n";
            debugText += $"Horizontal: {Input.GetAxis("Horizontal"):F2}\n";
            debugText += $"Vertical: {Input.GetAxis("Vertical"):F2}\n";
            debugText += $"Left Shift: {Input.GetKey(KeyCode.LeftShift)}\n";
            debugText += $"Left Ctrl: {Input.GetKey(KeyCode.LeftControl)}\n";
            debugText += $"WASD: W={Input.GetKey(KeyCode.W)} A={Input.GetKey(KeyCode.A)} S={Input.GetKey(KeyCode.S)} D={Input.GetKey(KeyCode.D)}\n\n";
            
            // New Input System
            debugText += "NEW INPUT SYSTEM:\n";
            if (Keyboard.current != null)
            {
                debugText += $"WASD: W={Keyboard.current.wKey.isPressed} A={Keyboard.current.aKey.isPressed} S={Keyboard.current.sKey.isPressed} D={Keyboard.current.dKey.isPressed}\n";
                debugText += $"Left Shift: {Keyboard.current.leftShiftKey.isPressed}\n";
                debugText += $"Left Ctrl: {Keyboard.current.leftCtrlKey.isPressed}\n";
                debugText += $"Arrow Keys: ↑={Keyboard.current.upArrowKey.isPressed} ↓={Keyboard.current.downArrowKey.isPressed} ←={Keyboard.current.leftArrowKey.isPressed} →={Keyboard.current.rightArrowKey.isPressed}\n";
            }
            else
            {
                debugText += "New Input System: NOT AVAILABLE\n";
            }
            
            debugText += "\n";
            
            // Player Controller Info
            var playerController = FindObjectOfType<AshesOfTheGrove.Player.PlayerController>();
            if (playerController != null)
            {
                debugText += "PLAYER CONTROLLER:\n";
                debugText += $"Is Crouching: {playerController.IsCrouching()}\n";
                debugText += $"Is Running: {playerController.IsRunning()}\n";
                debugText += $"Noise Level: {playerController.GetNoiseLevel():F1}\n";
            }
            else
            {
                debugText += "PLAYER CONTROLLER: NOT FOUND\n";
            }
            
            debugText += "\n";
            
            // Camera Info
            var cameraController = FindObjectOfType<CameraFollowController>();
            if (cameraController != null)
            {
                debugText += "CAMERA CONTROLLER: ACTIVE\n";
            }
            else
            {
                debugText += "CAMERA CONTROLLER: NOT FOUND\n";
            }
            
            // Cinemachine Info
            var cinemachineBrain = FindObjectOfType<Cinemachine.CinemachineBrain>();
            if (cinemachineBrain != null)
            {
                debugText += $"CINEMACHINE BRAIN: {(cinemachineBrain.enabled ? "ENABLED" : "DISABLED")}\n";
                debugText += $"Active Virtual Camera: {(cinemachineBrain.ActiveVirtualCamera != null ? cinemachineBrain.ActiveVirtualCamera.Name : "NONE")}\n";
            }
            else
            {
                debugText += "CINEMACHINE BRAIN: NOT FOUND\n";
            }
            
            debugText += "\n";
            debugText += $"Press {toggleKey} to toggle this debug display\n";
            debugText += "Press F2 to disable camera follow temporarily\n";
            debugText += "Press F3 to re-enable camera follow\n";
            
            // Handle debug commands
            if (Input.GetKeyDown(KeyCode.F2))
            {
                if (cameraController != null)
                {
                    cameraController.DisableCameraSystem();
                    Debug.Log("[InputDebugger] Camera system disabled via F2");
                }
            }
            
            if (Input.GetKeyDown(KeyCode.F3))
            {
                if (cameraController != null)
                {
                    cameraController.EnableCameraFollow(true);
                    Debug.Log("[InputDebugger] Camera system re-enabled via F3");
                }
            }
        }
        
        private void OnGUI()
        {
            if (!showDebug) return;
            
            // Create a semi-transparent background
            GUI.Box(new Rect(10, 10, 400, Screen.height - 20), "");
            
            // Display debug text
            GUI.Label(new Rect(20, 20, 380, Screen.height - 40), debugText, debugStyle);
        }
        
        // Public methods for external debugging
        public void ToggleDebug()
        {
            showDebug = !showDebug;
        }
        
        public void EnableDebug(bool enable)
        {
            showDebug = enable;
        }
        
        public static void LogInputState()
        {
            Debug.Log("=== INPUT STATE LOG ===");
            Debug.Log($"Legacy - Horizontal: {Input.GetAxis("Horizontal")}, Vertical: {Input.GetAxis("Vertical")}");
            
            if (Keyboard.current != null)
            {
                Debug.Log($"New Input - WASD: W={Keyboard.current.wKey.isPressed} A={Keyboard.current.aKey.isPressed} S={Keyboard.current.sKey.isPressed} D={Keyboard.current.dKey.isPressed}");
            }
            else
            {
                Debug.Log("New Input System not available");
            }
        }
    }
}

using UnityEngine;
using AshesOfTheGrove.Tutorial;
using AshesOfTheGrove.Player;
using AshesOfTheGrove.GameSystems;
using AshesOfTheGrove.AI;
using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Comprehensive diagnostics tool for the tutorial system
    /// Helps identify what's not working and provides detailed feedback
    /// </summary>
    public class TutorialDiagnostics : MonoBehaviour
    {
        [Header("Diagnostic Settings")]
        [SerializeField] private bool runDiagnosticsOnStart = true;
        [SerializeField] private bool showDetailedLogs = true;
        [SerializeField] private bool autoFixIssues = true;
        
        [Header("Manual Controls")]
        [SerializeField] private KeyCode diagnosticsKey = KeyCode.F12;
        [SerializeField] private KeyCode startTutorialKey = KeyCode.F11;
        [SerializeField] private KeyCode resetTutorialKey = KeyCode.F10;
        
        // System References
        private ModularTutorialSceneSetup sceneSetup;
        private TutorialZoneManager zoneManager;
        private TutorialZoneBuilder zoneBuilder;
        private TutorialUI tutorialUI;
        private PlayerControllerModular player;
        private InputManager inputManager;
        private CameraController cameraController;
        private TutorialZoneConfigurations zoneConfigs;
        
        // Diagnostic Results
        private System.Text.StringBuilder diagnosticResults = new System.Text.StringBuilder();
        private bool diagnosticsComplete = false;
        
        private void Start()
        {
            if (runDiagnosticsOnStart)
            {
                StartCoroutine(RunCompleteDiagnostics());
            }
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(diagnosticsKey))
            {
                StartCoroutine(RunCompleteDiagnostics());
            }
            
            if (Input.GetKeyDown(startTutorialKey))
            {
                StartTutorialManually();
            }
            
            if (Input.GetKeyDown(resetTutorialKey))
            {
                ResetTutorialSystem();
            }
        }
        
        private IEnumerator RunCompleteDiagnostics()
        {
            Debug.Log("=== TUTORIAL SYSTEM DIAGNOSTICS STARTING ===");
            diagnosticResults.Clear();
            diagnosticsComplete = false;
            
            // Step 1: Find all system components
            yield return StartCoroutine(FindSystemComponents());
            
            // Step 2: Validate system setup
            yield return StartCoroutine(ValidateSystemSetup());
            
            // Step 3: Check scene configuration
            yield return StartCoroutine(CheckSceneConfiguration());
            
            // Step 4: Validate tutorial zones
            yield return StartCoroutine(ValidateTutorialZones());
            
            // Step 5: Check player setup
            yield return StartCoroutine(CheckPlayerSetup());

            // Step 6: Check enemy AI
            yield return StartCoroutine(CheckEnemyAI());

            // Step 7: Auto-fix issues if enabled
            if (autoFixIssues)
            {
                yield return StartCoroutine(AutoFixIssues());
            }

            // Step 8: Final report
            GenerateFinalReport();
            diagnosticsComplete = true;
            
            Debug.Log("=== TUTORIAL SYSTEM DIAGNOSTICS COMPLETE ===");
        }
        
        private IEnumerator FindSystemComponents()
        {
            AddResult("=== FINDING SYSTEM COMPONENTS ===");

            sceneSetup = FindObjectOfType<ModularTutorialSceneSetup>();
            AddResult("ModularTutorialSceneSetup", sceneSetup != null);

            zoneManager = FindObjectOfType<TutorialZoneManager>();
            AddResult("TutorialZoneManager", zoneManager != null);

            zoneBuilder = FindObjectOfType<TutorialZoneBuilder>();
            AddResult("TutorialZoneBuilder", zoneBuilder != null);

            tutorialUI = FindObjectOfType<TutorialUI>();
            AddResult("TutorialUI", tutorialUI != null);

            player = FindObjectOfType<PlayerControllerModular>();
            AddResult("PlayerControllerModular", player != null);

            inputManager = FindObjectOfType<InputManager>();
            AddResult("InputManager", inputManager != null);

            cameraController = FindObjectOfType<CameraController>();
            AddResult("CameraController", cameraController != null);

            // Find zone configurations asset
            zoneConfigs = Resources.Load<TutorialZoneConfigurations>("TutorialZoneConfigurations");
            if (zoneConfigs == null)
            {
#if UNITY_EDITOR
                // Try to find it in the scene or project
                string[] guids = AssetDatabase.FindAssets("t:TutorialZoneConfigurations");
                if (guids.Length > 0)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                    zoneConfigs = AssetDatabase.LoadAssetAtPath<TutorialZoneConfigurations>(path);
                }
#endif
            }
            AddResult("TutorialZoneConfigurations Asset", zoneConfigs != null);

            if (zoneConfigs != null)
            {
                AddResult($"Zone Configs Count", zoneConfigs.GetAllConfigs().Count);
            }

            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateSystemSetup()
        {
            AddResult("\n=== VALIDATING SYSTEM SETUP ===");
            
            // Check if scene setup is configured properly
            if (sceneSetup != null)
            {
                AddResult("Scene Setup - Setup On Start", true); // We can see it exists
                AddResult("Scene Setup - Auto Start Tutorial", true);
            }
            
            // Check player tag and layer
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            AddResult("Player GameObject Tagged", playerObj != null);
            
            if (playerObj != null)
            {
                AddResult("Player Layer Correct", playerObj.layer == 8); // Player layer
                AddResult("Player Has Rigidbody", playerObj.GetComponent<Rigidbody>() != null);
                AddResult("Player Has Collider", playerObj.GetComponent<Collider>() != null);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator CheckSceneConfiguration()
        {
            AddResult("\n=== CHECKING SCENE CONFIGURATION ===");
            
            // Check if NavMesh exists
            UnityEngine.AI.NavMeshTriangulation navMesh = UnityEngine.AI.NavMesh.CalculateTriangulation();
            AddResult("NavMesh Built", navMesh.vertices.Length > 0);
            
            // Check camera setup
            Camera mainCamera = Camera.main;
            AddResult("Main Camera Exists", mainCamera != null);
            
            if (mainCamera != null)
            {
                AddResult("Camera Orthographic", mainCamera.orthographic);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator ValidateTutorialZones()
        {
            AddResult("\n=== VALIDATING TUTORIAL ZONES ===");

            if (zoneConfigs != null)
            {
                var configs = zoneConfigs.GetAllConfigs();
                AddResult($"Zone Configurations Count", configs.Count > 0);
                AddResult($"Total Zones Configured", configs.Count);

                foreach (var config in configs)
                {
                    AddResult($"Zone {config.zoneIndex}: {config.zoneName}", true);
                    AddResult($"  - Enemies: {config.enemyPositions.Count}");
                    AddResult($"  - Objectives: {config.objectivePositions.Count}");
                    AddResult($"  - Hiding Spots: {config.hidingSpotPositions.Count}");
                }
            }

            // Check for built zones in scene
            TutorialZone[] builtZones = FindObjectsOfType<TutorialZone>();
            AddResult("Built Tutorial Zones", builtZones.Length);

            // Check zone manager's zone list
            if (zoneManager != null)
            {
                AddResult("Zone Manager Has Zones", zoneManager.GetZoneCount() > 0);
                AddResult("Zone Manager Zone Count", zoneManager.GetZoneCount());
                AddResult("Zone Manager Active", zoneManager.IsTutorialActive());
                AddResult("Current Zone Index", zoneManager.GetCurrentZoneIndex());
            }

            // Check individual zones
            foreach (var zone in builtZones)
            {
                AddResult($"Zone '{zone.ZoneName}' Active", zone.IsActive);
                AddResult($"Zone '{zone.ZoneName}' Completed", zone.IsCompleted);
                AddResult($"Zone '{zone.ZoneName}' Player In Zone", zone.PlayerInZone);
            }

            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator CheckPlayerSetup()
        {
            AddResult("\n=== CHECKING PLAYER SETUP ===");

            if (player != null)
            {
                AddResult("Player Movement System", true);
                AddResult("Player Input System", inputManager != null);
                AddResult("Player Camera System", cameraController != null);
                AddResult("Player Position", player.transform.position);
                AddResult("Player Is Crouching", player.IsCrouching);
                AddResult("Player Is Running", player.IsRunning);
                AddResult("Player Is Moving", player.IsMoving);
                AddResult("Player Noise Level", player.GetNoiseLevel());
            }

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator CheckEnemyAI()
        {
            AddResult("\n=== CHECKING ENEMY AI ===");

            EnemyAIController[] enemies = FindObjectsOfType<EnemyAIController>();
            AddResult("Total Enemies in Scene", enemies.Length);

            foreach (var enemy in enemies)
            {
                AddResult($"Enemy '{enemy.name}' State", enemy.GetCurrentState().ToString());
                AddResult($"Enemy '{enemy.name}' Has NavMeshAgent", enemy.GetComponent<UnityEngine.AI.NavMeshAgent>() != null);
                AddResult($"Enemy '{enemy.name}' On NavMesh", enemy.GetComponent<UnityEngine.AI.NavMeshAgent>()?.isOnNavMesh ?? false);

                FieldOfView fov = enemy.GetComponent<FieldOfView>();
                if (fov != null)
                {
                    AddResult($"Enemy '{enemy.name}' Has FieldOfView", true);
                    AddResult($"Enemy '{enemy.name}' Can See Player", fov.CanSeePlayer());
                    AddResult($"Enemy '{enemy.name}' Detection Progress", fov.GetDetectionProgress());
                }
                else
                {
                    AddResult($"Enemy '{enemy.name}' Has FieldOfView", false);
                }
            }

            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator AutoFixIssues()
        {
            AddResult("\n=== AUTO-FIXING ISSUES ===");
            
            // Create scene setup if missing
            if (sceneSetup == null)
            {
                GameObject setupObj = new GameObject("ModularTutorialSceneSetup");
                sceneSetup = setupObj.AddComponent<ModularTutorialSceneSetup>();
                AddResult("Created ModularTutorialSceneSetup", true);
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private void AddResult(string testName, bool passed)
        {
            string result = passed ? "✓ PASS" : "✗ FAIL";
            string logEntry = $"{result}: {testName}";
            diagnosticResults.AppendLine(logEntry);
            
            if (showDetailedLogs)
            {
                Debug.Log($"[TutorialDiagnostics] {logEntry}");
            }
        }
        
        private void AddResult(string testName, int count)
        {
            string logEntry = $"INFO: {testName} = {count}";
            diagnosticResults.AppendLine(logEntry);

            if (showDetailedLogs)
            {
                Debug.Log($"[TutorialDiagnostics] {logEntry}");
            }
        }

        private void AddResult(string testName, Vector3 position)
        {
            string logEntry = $"INFO: {testName} = {position}";
            diagnosticResults.AppendLine(logEntry);

            if (showDetailedLogs)
            {
                Debug.Log($"[TutorialDiagnostics] {logEntry}");
            }
        }

        private void AddResult(string testName, float value)
        {
            string logEntry = $"INFO: {testName} = {value:F2}";
            diagnosticResults.AppendLine(logEntry);

            if (showDetailedLogs)
            {
                Debug.Log($"[TutorialDiagnostics] {logEntry}");
            }
        }
        
        private void GenerateFinalReport()
        {
            Debug.Log("=== TUTORIAL DIAGNOSTICS FINAL REPORT ===");
            Debug.Log(diagnosticResults.ToString());
            Debug.Log("=== END REPORT ===");
            
            // Provide recommendations
            if (sceneSetup == null)
            {
                Debug.LogWarning("RECOMMENDATION: Add ModularTutorialSceneSetup component to a GameObject in the scene");
            }
            
            if (zoneConfigs == null)
            {
                Debug.LogWarning("RECOMMENDATION: Assign TutorialZoneConfigurations asset to the scene setup");
            }
            
            if (player == null)
            {
                Debug.LogWarning("RECOMMENDATION: Ensure PlayerControllerModular is in the scene with 'Player' tag");
            }
        }
        
        [ContextMenu("Start Tutorial Manually")]
        public void StartTutorialManually()
        {
            Debug.Log("[TutorialDiagnostics] Starting tutorial manually...");
            
            if (sceneSetup != null)
            {
                sceneSetup.SetupTutorialScene();
            }
            else if (zoneManager != null)
            {
                zoneManager.StartTutorial();
            }
            else
            {
                Debug.LogError("No tutorial system found to start!");
            }
        }
        
        [ContextMenu("Reset Tutorial System")]
        public void ResetTutorialSystem()
        {
            Debug.Log("[TutorialDiagnostics] Resetting tutorial system...");
            
            if (zoneManager != null)
            {
                zoneManager.RestartTutorial();
            }
            
            if (sceneSetup != null)
            {
                sceneSetup.RestartTutorial();
            }
        }
        
        private void OnGUI()
        {
            if (!diagnosticsComplete) return;
            
            // Show diagnostic controls
            GUILayout.BeginArea(new Rect(10, Screen.height - 120, 300, 110));
            GUILayout.Box("Tutorial Diagnostics Controls");
            
            if (GUILayout.Button($"Run Diagnostics ({diagnosticsKey})"))
            {
                StartCoroutine(RunCompleteDiagnostics());
            }
            
            if (GUILayout.Button($"Start Tutorial ({startTutorialKey})"))
            {
                StartTutorialManually();
            }
            
            if (GUILayout.Button($"Reset Tutorial ({resetTutorialKey})"))
            {
                ResetTutorialSystem();
            }
            
            GUILayout.EndArea();
        }
    }
}

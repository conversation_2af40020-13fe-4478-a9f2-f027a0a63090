# Unity Project Settings for Ashes of the Grove

## Recommended Settings

### Player Settings
- **Company Name**: Your Studio Name
- **Product Name**: Ashes of the Grove
- **Default Icon**: Set custom icon
- **Resolution and Presentation**:
  - Default Screen Width: 1920
  - Default Screen Height: 1080
  - Fullscreen Mode: Fullscreen Window

### Input Manager
- Set up custom input axes for isometric movement
- Add stealth controls (crouch, run, interact)

### Tags and Layers
**Tags:**
- Player
- Enemy
- Interactable
- HidingSpot
- NoiseSource

**Layers:**
- Default (0)
- Player (8)
- Enemy (9)
- Environment (10)
- Interactable (11)
- UI (12)
- Obstacle (13)

### Physics Settings
- Configure layer collision matrix
- Disable unnecessary layer interactions for performance

### Quality Settings
- Set up quality levels for different platforms
- Configure shadows, anti-aliasing, and post-processing

### Audio Settings
- Set up audio mixer groups:
  - Master
  - Music
  - SFX
  - Voice
  - UI

## Required Packages
Install these packages via Window > Package Manager:

1. **NavMesh Components** (for AI pathfinding)
2. **Timeline** (for cutscenes)
3. **Cinemachine** (for camera control)
4. **Input System** (modern input handling)
5. **Universal Render Pipeline** (optional, for better graphics)

## Build Settings
- Add all scenes to build settings in correct order:
  1. MainMenu
  2. Mission_01_Wiretap
  3. Mission_02_Checkpoint

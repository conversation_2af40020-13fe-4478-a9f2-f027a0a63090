using UnityEngine;

namespace AshesOfTheGrove.GameSystems
{
    /// <summary>
    /// Modular movement controller that handles character movement mechanics
    /// Can be used by player, NPCs, or any moving entity
    /// </summary>
    public class MovementController : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float walkSpeed = 15f;
        [SerializeField] private float crouchSpeed = 8f;
        [SerializeField] private float runSpeed = 25f;
        
        [Header("Movement Behavior")]
        [SerializeField] private bool useRigidbody = true;
        [SerializeField] private bool faceMovementDirection = true;
        [SerializeField] private float rotationSpeed = 10f;
        
        [Header("State")]
        [SerializeField] private bool isCrouching = false;
        [SerializeField] private bool isRunning = false;
        [SerializeField] private bool canMove = true;
        
        // Components
        private Rigidbody rb;
        private InputManager inputManager;
        private Animator animator;
        
        // Movement state
        private Vector3 currentMovement;
        private Vector3 lastMovementDirection;
        private float currentSpeed;
        
        // Events
        public System.Action<Vector3> OnMovementChanged;
        public System.Action<float> OnSpeedChanged;
        public System.Action<bool> OnCrouchStateChanged;
        public System.Action<bool> OnRunStateChanged;
        
        // Properties
        public bool IsCrouching => isCrouching;
        public bool IsRunning => isRunning;
        public bool IsMoving => currentMovement.magnitude > 0.1f;
        public float CurrentSpeed => currentSpeed;
        public Vector3 MovementDirection => currentMovement.normalized;
        
        private void Awake()
        {
            // Get components
            rb = GetComponent<Rigidbody>();
            animator = GetComponent<Animator>();
            
            if (useRigidbody && rb == null)
            {
                Debug.LogWarning("[MovementController] Rigidbody required but not found. Adding one.");
                rb = gameObject.AddComponent<Rigidbody>();
                rb.freezeRotation = true;
            }
        }
        
        private void Start()
        {
            InitializeMovement();
        }
        
        private void Update()
        {
            if (!canMove) return;
            
            HandleMovementInput();
            HandleMovementStates();
        }
        
        private void FixedUpdate()
        {
            if (!canMove) return;
            
            ApplyMovement();
        }
        
        private void InitializeMovement()
        {
            // Find input manager
            inputManager = FindObjectOfType<InputManager>();
            if (inputManager == null)
            {
                Debug.LogWarning("[MovementController] No InputManager found. Movement input will not work.");
                return;
            }
            
            // Subscribe to input events
            inputManager.OnMovementInput += HandleMovementInput;
            inputManager.OnCrouchPressed += ToggleCrouch;
            inputManager.OnRunPressed += ToggleRun;
            
            Debug.Log("[MovementController] Movement system initialized");
        }
        
        private void HandleMovementInput()
        {
            if (inputManager == null) return;
            
            // Get movement input
            Vector2 input = inputManager.MovementInput;
            
            // Convert to 3D movement (isometric)
            currentMovement = new Vector3(input.x, 0, input.y);
            
            // Update running state based on input
            if (inputManager.RunHeld && !isCrouching)
            {
                isRunning = true;
            }
            else
            {
                isRunning = false;
            }
            
            OnMovementChanged?.Invoke(currentMovement);
        }
        
        private void HandleMovementInput(Vector2 input)
        {
            // This is called by the input manager event
            currentMovement = new Vector3(input.x, 0, input.y);
        }
        
        private void HandleMovementStates()
        {
            // Calculate current speed
            float targetSpeed = GetTargetSpeed();
            if (targetSpeed != currentSpeed)
            {
                currentSpeed = targetSpeed;
                OnSpeedChanged?.Invoke(currentSpeed);
            }
            
            // Update animator if available
            UpdateAnimator();
            
            // Handle rotation
            if (faceMovementDirection && IsMoving)
            {
                RotateTowardsMovement();
            }
        }
        
        private float GetTargetSpeed()
        {
            if (!IsMoving) return 0f;
            
            if (isCrouching) return crouchSpeed;
            if (isRunning) return runSpeed;
            return walkSpeed;
        }
        
        private void ApplyMovement()
        {
            if (!IsMoving) return;
            
            Vector3 moveDirection = currentMovement.normalized * currentSpeed * Time.fixedDeltaTime;
            
            if (useRigidbody && rb != null)
            {
                // Use Rigidbody for physics-based movement
                rb.MovePosition(transform.position + moveDirection);
            }
            else
            {
                // Direct transform movement
                transform.position += moveDirection;
            }
            
            lastMovementDirection = currentMovement.normalized;
        }
        
        private void RotateTowardsMovement()
        {
            if (currentMovement.magnitude < 0.1f) return;
            
            Quaternion targetRotation = Quaternion.LookRotation(currentMovement.normalized);
            transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
        
        private void UpdateAnimator()
        {
            if (animator == null) return;
            
            // Update animator parameters
            animator.SetFloat("Speed", currentMovement.magnitude);
            animator.SetBool("IsCrouching", isCrouching);
            animator.SetBool("IsRunning", isRunning);
        }
        
        private void ToggleCrouch()
        {
            SetCrouchState(!isCrouching);
        }
        
        private void ToggleRun()
        {
            // Run is handled by input hold, but this can be used for toggle mode
            if (!isCrouching)
            {
                isRunning = !isRunning;
                OnRunStateChanged?.Invoke(isRunning);
            }
        }
        
        // Public control methods
        public void SetCrouchState(bool crouch)
        {
            if (isCrouching != crouch)
            {
                isCrouching = crouch;
                
                // Visual feedback
                UpdateCrouchVisuals();
                
                OnCrouchStateChanged?.Invoke(isCrouching);
                Debug.Log($"[MovementController] Crouch state: {isCrouching}");
            }
        }
        
        public void SetRunState(bool run)
        {
            if (!isCrouching && isRunning != run)
            {
                isRunning = run;
                OnRunStateChanged?.Invoke(isRunning);
                Debug.Log($"[MovementController] Run state: {isRunning}");
            }
        }
        
        public void SetMovementEnabled(bool enabled)
        {
            canMove = enabled;
            if (!enabled)
            {
                currentMovement = Vector3.zero;
                currentSpeed = 0f;
            }
            Debug.Log($"[MovementController] Movement {(enabled ? "enabled" : "disabled")}");
        }
        
        public void SetSpeeds(float walk, float crouch, float run)
        {
            walkSpeed = walk;
            crouchSpeed = crouch;
            runSpeed = run;
            Debug.Log($"[MovementController] Speeds updated - Walk: {walk}, Crouch: {crouch}, Run: {run}");
        }
        
        private void UpdateCrouchVisuals()
        {
            // Scale player down when crouching
            float targetScale = isCrouching ? 0.7f : 1f;
            transform.localScale = new Vector3(1f, targetScale, 1f);
        }
        
        // Utility methods
        public Vector3 GetVelocity()
        {
            if (useRigidbody && rb != null)
            {
                return rb.velocity;
            }
            return currentMovement * currentSpeed;
        }
        
        public void StopMovement()
        {
            currentMovement = Vector3.zero;
            currentSpeed = 0f;
            
            if (useRigidbody && rb != null)
            {
                rb.velocity = Vector3.zero;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (inputManager != null)
            {
                inputManager.OnMovementInput -= HandleMovementInput;
                inputManager.OnCrouchPressed -= ToggleCrouch;
                inputManager.OnRunPressed -= ToggleRun;
            }
        }
    }
}

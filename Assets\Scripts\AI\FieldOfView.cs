using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.AI
{
    /// <summary>
    /// Field of View system for enemy AI detection
    /// Handles line of sight, view angles, and stealth detection
    /// </summary>
    public class FieldOfView : MonoBehaviour
    {
        [Header("Field of View Settings")]
        [SerializeField] private float viewRadius = 10f;
        [SerializeField] private float viewAngle = 90f;
        [SerializeField] private LayerMask playerMask;
        [SerializeField] private LayerMask obstacleMask;
        
        [Header("Detection Settings")]
        [SerializeField] private float detectionTime = 2f;
        [SerializeField] private float stealthDetectionMultiplier = 0.5f;

        [Header("Tutorial Visualization")]
        [SerializeField] private bool showFieldOfView = true;
        [SerializeField] private bool showDetectionProgress = true;
        [SerializeField] private Color normalViewColor = Color.red;
        [SerializeField] private Color detectionColor = Color.yellow;
        [SerializeField] private bool useRuntimeVisualization = true;

        private Transform player;
        private PlayerControllerModular playerController;
        private float currentDetectionTime = 0f;
        private bool playerInSight = false;

        // Runtime visualization components
        private LineRenderer fovLineRenderer;
        private LineRenderer detectionLineRenderer;
        
        private void Start()
        {
            player = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (player != null)
            {
                playerController = player.GetComponent<PlayerControllerModular>();
            }

            if (useRuntimeVisualization)
            {
                SetupRuntimeVisualization();
            }
        }
        
        private void Update()
        {
            FindPlayer();

            if (useRuntimeVisualization)
            {
                UpdateRuntimeVisualization();
            }
        }
        
        private void FindPlayer()
        {
            // Re-find player if it's null (might have been destroyed and recreated)
            if (player == null)
            {
                player = GameObject.FindGameObjectWithTag("Player")?.transform;
                if (player != null)
                {
                    playerController = player.GetComponent<PlayerControllerModular>();
                }
            }

            if (player == null) return;
            
            Vector3 dirToPlayer = (player.position - transform.position).normalized;
            
            if (Vector3.Angle(transform.forward, dirToPlayer) < viewAngle / 2)
            {
                float distToPlayer = Vector3.Distance(transform.position, player.position);
                
                if (distToPlayer <= viewRadius)
                {
                    if (Physics.Raycast(transform.position, dirToPlayer, distToPlayer, obstacleMask))
                    {
                        // Player is behind obstacle
                        playerInSight = false;
                        currentDetectionTime = 0f;
                    }
                    else
                    {
                        // Player is visible
                        HandlePlayerDetection();
                    }
                }
                else
                {
                    playerInSight = false;
                    currentDetectionTime = 0f;
                }
            }
            else
            {
                playerInSight = false;
                currentDetectionTime = 0f;
            }
        }
        
        private void HandlePlayerDetection()
        {
            if (playerController != null)
            {
                float detectionMultiplier = 1f;
                
                // Reduce detection if player is crouching
                if (playerController.IsCrouching())
                {
                    detectionMultiplier *= stealthDetectionMultiplier;
                }
                
                // Reduce detection if player is hiding
                if (playerController.IsHiding())
                {
                    detectionMultiplier *= 0.1f;
                }
                
                // Increase detection based on noise level
                detectionMultiplier *= playerController.GetNoiseLevel();
                
                currentDetectionTime += Time.deltaTime * detectionMultiplier;
                
                if (currentDetectionTime >= detectionTime)
                {
                    playerInSight = true;
                }
            }
        }
        
        public bool CanSeePlayer()
        {
            return playerInSight;
        }
        
        public float GetDetectionProgress()
        {
            return currentDetectionTime / detectionTime;
        }
        
        public Vector3 DirectionToPlayer()
        {
            if (player != null)
            {
                return (player.position - transform.position).normalized;
            }
            return Vector3.zero;
        }
        
        public Vector3 DirectionFromAngle(float angleInDegrees, bool angleIsGlobal)
        {
            if (!angleIsGlobal)
            {
                angleInDegrees += transform.eulerAngles.y;
            }
            return new Vector3(Mathf.Sin(angleInDegrees * Mathf.Deg2Rad), 0, Mathf.Cos(angleInDegrees * Mathf.Deg2Rad));
        }
        
        private void OnDrawGizmos()
        {
            if (!showFieldOfView) return;

            // Draw field of view cone
            Color viewColor = playerInSight ? detectionColor : normalViewColor;
            viewColor.a = 0.3f;
            Gizmos.color = viewColor;

            Vector3 viewAngleA = DirectionFromAngle(-viewAngle / 2, false);
            Vector3 viewAngleB = DirectionFromAngle(viewAngle / 2, false);

            // Draw view cone
            Gizmos.DrawLine(transform.position, transform.position + viewAngleA * viewRadius);
            Gizmos.DrawLine(transform.position, transform.position + viewAngleB * viewRadius);

            // Draw arc
            int rayCount = 20;
            for (int i = 0; i <= rayCount; i++)
            {
                float angle = Mathf.Lerp(-viewAngle / 2, viewAngle / 2, (float)i / rayCount);
                Vector3 direction = DirectionFromAngle(angle, false);
                Vector3 endPoint = transform.position + direction * viewRadius;

                if (i > 0)
                {
                    float prevAngle = Mathf.Lerp(-viewAngle / 2, viewAngle / 2, (float)(i - 1) / rayCount);
                    Vector3 prevDirection = DirectionFromAngle(prevAngle, false);
                    Vector3 prevEndPoint = transform.position + prevDirection * viewRadius;
                    Gizmos.DrawLine(prevEndPoint, endPoint);
                }
            }

            // Draw detection line
            if (playerInSight && player != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, player.position);
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Draw detailed view information when selected
            Gizmos.color = Color.white;
            Gizmos.DrawWireSphere(transform.position, viewRadius);

            Vector3 viewAngleA = DirectionFromAngle(-viewAngle / 2, false);
            Vector3 viewAngleB = DirectionFromAngle(viewAngle / 2, false);

            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, transform.position + viewAngleA * viewRadius);
            Gizmos.DrawLine(transform.position, transform.position + viewAngleB * viewRadius);

            if (playerInSight && player != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, player.position);
            }
        }

        // Tutorial support methods
        public void SetTutorialVisualization(bool enabled)
        {
            showFieldOfView = enabled;
            showDetectionProgress = enabled;
            useRuntimeVisualization = enabled;

            if (enabled && fovLineRenderer == null)
            {
                SetupRuntimeVisualization();
            }
            else if (!enabled && fovLineRenderer != null)
            {
                if (fovLineRenderer != null) fovLineRenderer.enabled = false;
                if (detectionLineRenderer != null) detectionLineRenderer.enabled = false;
            }
        }

        private void SetupRuntimeVisualization()
        {
            // Create FOV cone LineRenderer
            GameObject fovObject = new GameObject("FOV_Visualization");
            fovObject.transform.SetParent(transform);
            fovObject.transform.localPosition = Vector3.zero;

            fovLineRenderer = fovObject.AddComponent<LineRenderer>();
            fovLineRenderer.material = new Material(Shader.Find("Sprites/Default"));
            fovLineRenderer.material.color = normalViewColor;
            fovLineRenderer.startWidth = 0.05f;
            fovLineRenderer.endWidth = 0.05f;
            fovLineRenderer.useWorldSpace = true;
            fovLineRenderer.sortingOrder = 1;

            // Create detection line LineRenderer
            GameObject detectionObject = new GameObject("Detection_Line");
            detectionObject.transform.SetParent(transform);
            detectionObject.transform.localPosition = Vector3.zero;

            detectionLineRenderer = detectionObject.AddComponent<LineRenderer>();
            detectionLineRenderer.material = new Material(Shader.Find("Sprites/Default"));
            detectionLineRenderer.material.color = Color.red;
            detectionLineRenderer.startWidth = 0.1f;
            detectionLineRenderer.endWidth = 0.1f;
            detectionLineRenderer.useWorldSpace = true;
            detectionLineRenderer.sortingOrder = 2;
            detectionLineRenderer.positionCount = 2;
            detectionLineRenderer.enabled = false;
        }

        private void UpdateRuntimeVisualization()
        {
            if (fovLineRenderer == null) return;

            // Update FOV cone color based on detection state
            Color currentColor = playerInSight ? detectionColor : normalViewColor;
            currentColor.a = 0.6f;
            fovLineRenderer.material.color = currentColor;

            // Draw FOV cone
            int rayCount = 20;
            fovLineRenderer.positionCount = rayCount + 3; // +3 for cone shape (center to edges and back)

            Vector3[] positions = new Vector3[rayCount + 3];
            positions[0] = transform.position;

            for (int i = 0; i <= rayCount; i++)
            {
                float angle = Mathf.Lerp(-viewAngle / 2, viewAngle / 2, (float)i / rayCount);
                Vector3 direction = DirectionFromAngle(angle, false);
                positions[i + 1] = transform.position + direction * viewRadius;
            }

            positions[rayCount + 2] = transform.position; // Close the cone
            fovLineRenderer.SetPositions(positions);

            // Update detection line
            if (playerInSight && player != null && detectionLineRenderer != null)
            {
                detectionLineRenderer.enabled = true;
                detectionLineRenderer.SetPosition(0, transform.position);
                detectionLineRenderer.SetPosition(1, player.position);
            }
            else if (detectionLineRenderer != null)
            {
                detectionLineRenderer.enabled = false;
            }
        }

        public bool IsPlayerInSight()
        {
            return playerInSight;
        }

        public float GetViewRadius()
        {
            return viewRadius;
        }

        public float GetViewAngle()
        {
            return viewAngle;
        }
    }
}

using UnityEngine;

namespace AshesOfTheGrove.AI
{
    /// <summary>
    /// Field of View system for enemy AI detection
    /// <PERSON>les line of sight, view angles, and stealth detection
    /// </summary>
    public class FieldOfView : MonoBehaviour
    {
        [Header("Field of View Settings")]
        [SerializeField] private float viewRadius = 10f;
        [SerializeField] private float viewAngle = 90f;
        [SerializeField] private LayerMask playerMask;
        [SerializeField] private LayerMask obstacleMask;
        
        [Header("Detection Settings")]
        [SerializeField] private float detectionTime = 2f;
        [SerializeField] private float stealthDetectionMultiplier = 0.5f;
        
        private Transform player;
        private PlayerController playerController;
        private float currentDetectionTime = 0f;
        private bool playerInSight = false;
        
        private void Start()
        {
            player = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (player != null)
            {
                playerController = player.GetComponent<PlayerController>();
            }
        }
        
        private void Update()
        {
            FindPlayer();
        }
        
        private void FindPlayer()
        {
            if (player == null) return;
            
            Vector3 dirToPlayer = (player.position - transform.position).normalized;
            
            if (Vector3.Angle(transform.forward, dirToPlayer) < viewAngle / 2)
            {
                float distToPlayer = Vector3.Distance(transform.position, player.position);
                
                if (distToPlayer <= viewRadius)
                {
                    if (Physics.Raycast(transform.position, dirToPlayer, distToPlayer, obstacleMask))
                    {
                        // Player is behind obstacle
                        playerInSight = false;
                        currentDetectionTime = 0f;
                    }
                    else
                    {
                        // Player is visible
                        HandlePlayerDetection();
                    }
                }
                else
                {
                    playerInSight = false;
                    currentDetectionTime = 0f;
                }
            }
            else
            {
                playerInSight = false;
                currentDetectionTime = 0f;
            }
        }
        
        private void HandlePlayerDetection()
        {
            if (playerController != null)
            {
                float detectionMultiplier = 1f;
                
                // Reduce detection if player is crouching
                if (playerController.IsCrouching())
                {
                    detectionMultiplier *= stealthDetectionMultiplier;
                }
                
                // Reduce detection if player is hiding
                if (playerController.IsHiding())
                {
                    detectionMultiplier *= 0.1f;
                }
                
                // Increase detection based on noise level
                detectionMultiplier *= playerController.GetNoiseLevel();
                
                currentDetectionTime += Time.deltaTime * detectionMultiplier;
                
                if (currentDetectionTime >= detectionTime)
                {
                    playerInSight = true;
                }
            }
        }
        
        public bool CanSeePlayer()
        {
            return playerInSight;
        }
        
        public float GetDetectionProgress()
        {
            return currentDetectionTime / detectionTime;
        }
        
        public Vector3 DirectionToPlayer()
        {
            if (player != null)
            {
                return (player.position - transform.position).normalized;
            }
            return Vector3.zero;
        }
        
        public Vector3 DirectionFromAngle(float angleInDegrees, bool angleIsGlobal)
        {
            if (!angleIsGlobal)
            {
                angleInDegrees += transform.eulerAngles.y;
            }
            return new Vector3(Mathf.Sin(angleInDegrees * Mathf.Deg2Rad), 0, Mathf.Cos(angleInDegrees * Mathf.Deg2Rad));
        }
        
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.white;
            Gizmos.DrawWireSphere(transform.position, viewRadius);
            
            Vector3 viewAngleA = DirectionFromAngle(-viewAngle / 2, false);
            Vector3 viewAngleB = DirectionFromAngle(viewAngle / 2, false);
            
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, transform.position + viewAngleA * viewRadius);
            Gizmos.DrawLine(transform.position, transform.position + viewAngleB * viewRadius);
            
            if (playerInSight && player != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, player.position);
            }
        }
    }
}

using UnityEngine;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing
{
    /// <summary>
    /// Test script to verify PlayerController input functionality
    /// Displays input values and player state in real-time
    /// </summary>
    public class InputTester : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private PlayerController playerController;
        
        [Header("Display Settings")]
        [SerializeField] private bool showDebugInfo = true;
        [SerializeField] private KeyCode toggleDebugKey = KeyCode.F1;
        
        // Input tracking
        private float horizontalInput;
        private float verticalInput;
        private bool crouchInput;
        private bool runInput;
        private Vector3 lastPosition;
        private float currentSpeed;
        
        // GUI Style
        private GUIStyle debugStyle;
        
        private void Start()
        {
            // Find PlayerController if not assigned
            if (playerController == null)
            {
                playerController = FindObjectOfType<PlayerController>();
            }
            
            if (playerController == null)
            {
                Debug.LogError("InputTester: No PlayerController found in scene!");
            }
            
            lastPosition = transform.position;
            
            // Setup GUI style
            debugStyle = new GUIStyle();
            debugStyle.fontSize = 16;
            debugStyle.normal.textColor = Color.white;
            debugStyle.alignment = TextAnchor.UpperLeft;
        }
        
        private void Update()
        {
            // Toggle debug display
            if (Input.GetKeyDown(toggleDebugKey))
            {
                showDebugInfo = !showDebugInfo;
            }
            
            // Track input values
            TrackInputs();
            
            // Calculate current speed
            CalculateSpeed();
            
            // Log input changes
            LogInputChanges();
        }
        
        private void TrackInputs()
        {
            horizontalInput = Input.GetAxis("Horizontal");
            verticalInput = Input.GetAxis("Vertical");
            crouchInput = Input.GetKey(KeyCode.LeftControl);
            runInput = Input.GetKey(KeyCode.LeftShift);
        }
        
        private void CalculateSpeed()
        {
            Vector3 currentPosition = transform.position;
            currentSpeed = Vector3.Distance(currentPosition, lastPosition) / Time.deltaTime;
            lastPosition = currentPosition;
        }
        
        private void LogInputChanges()
        {
            // Log crouch toggle
            if (Input.GetKeyDown(KeyCode.LeftControl))
            {
                Debug.Log($"[InputTester] Crouch pressed - Player crouching: {(playerController != null ? playerController.IsCrouching() : "N/A")}");
            }
            
            // Log run input
            if (Input.GetKeyDown(KeyCode.LeftShift))
            {
                Debug.Log("[InputTester] Run key pressed");
            }
            
            if (Input.GetKeyUp(KeyCode.LeftShift))
            {
                Debug.Log("[InputTester] Run key released");
            }
            
            // Log movement input (only when significant)
            if (Mathf.Abs(horizontalInput) > 0.1f || Mathf.Abs(verticalInput) > 0.1f)
            {
                if (Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
                {
                    Debug.Log($"[InputTester] Movement - H: {horizontalInput:F2}, V: {verticalInput:F2}, Speed: {currentSpeed:F2}");
                }
            }
        }
        
        private void OnGUI()
        {
            if (!showDebugInfo) return;
            
            // Create debug info panel
            GUI.Box(new Rect(10, 10, 300, 250), "");
            
            float yPos = 20;
            float lineHeight = 20;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), "=== INPUT TESTER ===", debugStyle);
            yPos += lineHeight * 1.5f;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Press {toggleDebugKey} to toggle this panel", debugStyle);
            yPos += lineHeight * 1.5f;
            
            // Input values
            GUI.Label(new Rect(20, yPos, 280, lineHeight), "INPUT VALUES:", debugStyle);
            yPos += lineHeight;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Horizontal: {horizontalInput:F2}", debugStyle);
            yPos += lineHeight;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Vertical: {verticalInput:F2}", debugStyle);
            yPos += lineHeight;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Crouch (L-Ctrl): {crouchInput}", debugStyle);
            yPos += lineHeight;
            
            GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Run (L-Shift): {runInput}", debugStyle);
            yPos += lineHeight * 1.5f;
            
            // Player state (if PlayerController is available)
            if (playerController != null)
            {
                GUI.Label(new Rect(20, yPos, 280, lineHeight), "PLAYER STATE:", debugStyle);
                yPos += lineHeight;
                
                GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Is Crouching: {playerController.IsCrouching()}", debugStyle);
                yPos += lineHeight;
                
                GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Is Hiding: {playerController.IsHiding()}", debugStyle);
                yPos += lineHeight;
                
                GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Noise Level: {playerController.GetNoiseLevel():F2}", debugStyle);
                yPos += lineHeight;
                
                GUI.Label(new Rect(20, yPos, 280, lineHeight), $"Current Speed: {currentSpeed:F2}", debugStyle);
            }
            else
            {
                GUI.Label(new Rect(20, yPos, 280, lineHeight), "PlayerController: NOT FOUND", debugStyle);
            }
        }
        
        // Test methods that can be called from buttons or other scripts
        public void TestMovementInput()
        {
            Debug.Log("=== TESTING MOVEMENT INPUT ===");
            Debug.Log("Use WASD or Arrow Keys to move");
            Debug.Log("Expected: Player should move in isometric directions");
        }
        
        public void TestStealthInput()
        {
            Debug.Log("=== TESTING STEALTH INPUT ===");
            Debug.Log("Press Left Control to toggle crouch");
            Debug.Log("Hold Left Shift to run");
            Debug.Log("Expected: Different movement speeds and noise levels");
        }
        
        public void TestAllInputs()
        {
            Debug.Log("=== COMPREHENSIVE INPUT TEST ===");
            Debug.Log("1. Move with WASD/Arrow Keys");
            Debug.Log("2. Toggle crouch with Left Control");
            Debug.Log("3. Hold Left Shift to run");
            Debug.Log("4. Try combinations (crouch + move, run + move)");
            Debug.Log("5. Watch the debug panel for real-time feedback");
        }
    }
}

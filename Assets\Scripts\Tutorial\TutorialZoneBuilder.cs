using UnityEngine;
using System.Collections.Generic;
using AshesOfTheGrove.AI;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Builds tutorial zones from configurations
    /// Creates all zone elements including enemies, obstacles, and objectives
    /// </summary>
    public class TutorialZoneBuilder : MonoBehaviour
    {
        [Header("Prefab References")]
        [SerializeField] private GameObject enemyPrefab;
        [SerializeField] private GameObject obstaclePrefab;
        [SerializeField] private GameObject hidingSpotPrefab;
        [SerializeField] private GameObject objectivePrefab;
        [SerializeField] private GameObject zoneBoundaryPrefab;
        
        [Header("Default Materials")]
        [SerializeField] private Material enemyMaterial;
        [SerializeField] private Material obstacleMaterial;
        [SerializeField] private Material hidingSpotMaterial;
        [SerializeField] private Material objectiveMaterial;
        
        [Header("Build Settings")]
        [SerializeField] private bool createNavMesh = true;
        [SerializeField] private bool showZoneBoundaries = true;
        
        // Built zones tracking
        private List<GameObject> builtZoneObjects = new List<GameObject>();
        
        public void BuildAllZones(TutorialZoneConfigurations configurations)
        {
            if (configurations == null)
            {
                Debug.LogError("[TutorialZoneBuilder] No configurations provided");
                return;
            }
            
            ClearExistingZones();
            
            foreach (var config in configurations.GetAllConfigs())
            {
                BuildZone(config);
            }
            
            if (createNavMesh)
            {
                BuildNavMesh();
            }
            
            Debug.Log($"[TutorialZoneBuilder] Built {configurations.GetAllConfigs().Count} tutorial zones");
        }
        
        public GameObject BuildZone(TutorialZoneConfig config)
        {
            if (config == null)
            {
                Debug.LogError("[TutorialZoneBuilder] No zone configuration provided");
                return null;
            }
            
            // Create zone parent object
            GameObject zoneParent = new GameObject($"TutorialZone_{config.zoneIndex}_{config.zoneName}");
            zoneParent.transform.position = config.zoneCenter;
            builtZoneObjects.Add(zoneParent);
            
            // Add TutorialZone component
            TutorialZone zoneComponent = zoneParent.AddComponent<TutorialZone>();
            ConfigureZoneComponent(zoneComponent, config);
            
            // Create zone elements
            CreateZoneEnemies(zoneParent, config);
            CreateZoneObstacles(zoneParent, config);
            CreateZoneHidingSpots(zoneParent, config);
            CreateZoneObjective(zoneParent, config);
            
            if (showZoneBoundaries)
            {
                CreateZoneBoundary(zoneParent, config);
            }
            
            Debug.Log($"[TutorialZoneBuilder] Built zone: {config.zoneName}");
            return zoneParent;
        }
        
        private void ConfigureZoneComponent(TutorialZone zoneComponent, TutorialZoneConfig config)
        {
            // Configure the zone component using the new configuration method
            zoneComponent.ConfigureFromConfig(config);

            Debug.Log($"[TutorialZoneBuilder] Configured zone component for: {config.zoneName}");
        }
        
        private void CreateZoneEnemies(GameObject parent, TutorialZoneConfig config)
        {
            GameObject enemiesParent = new GameObject("Enemies");
            enemiesParent.transform.SetParent(parent.transform, false);
            
            for (int i = 0; i < config.enemyPositions.Count; i++)
            {
                // Calculate world position relative to zone transform
                Vector3 worldPos = parent.transform.position + config.zoneCenter + config.enemyPositions[i];
                GameObject enemy = CreateEnemy(worldPos, i);
                enemy.transform.SetParent(enemiesParent.transform, false);
            }
            
            Debug.Log($"[TutorialZoneBuilder] Created {config.enemyPositions.Count} enemies for zone: {config.zoneName}");
        }
        
        private GameObject CreateEnemy(Vector3 position, int index)
        {
            GameObject enemy;
            
            if (enemyPrefab != null)
            {
                enemy = Instantiate(enemyPrefab, position, Quaternion.identity);
            }
            else
            {
                // Create basic enemy
                enemy = CreateBasicEnemy(position);
            }
            
            enemy.name = $"TutorialEnemy_{index}";
            enemy.tag = "Enemy";
            
            return enemy;
        }
        
        private GameObject CreateBasicEnemy(Vector3 position)
        {
            // Create basic enemy capsule
            GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            enemy.transform.position = position;
            
            // Add required AI components
            if (enemy.GetComponent<NavMeshAgent>() == null)
            {
                enemy.AddComponent<NavMeshAgent>();
            }

            if (enemy.GetComponent<FieldOfView>() == null)
            {
                enemy.AddComponent<FieldOfView>();
            }

            if (enemy.GetComponent<EnemyAIController>() == null)
            {
                enemy.AddComponent<EnemyAIController>();
            }
            
            // Set material
            if (enemyMaterial != null)
            {
                enemy.GetComponent<Renderer>().material = enemyMaterial;
            }
            else
            {
                enemy.GetComponent<Renderer>().material.color = Color.red;
            }
            
            return enemy;
        }
        
        private void CreateZoneObstacles(GameObject parent, TutorialZoneConfig config)
        {
            GameObject obstaclesParent = new GameObject("Obstacles");
            obstaclesParent.transform.SetParent(parent.transform, false);
            
            for (int i = 0; i < config.obstaclePositions.Count; i++)
            {
                // Calculate world position relative to zone transform
                Vector3 worldPos = parent.transform.position + config.zoneCenter + config.obstaclePositions[i];
                GameObject obstacle = CreateObstacle(worldPos, i);
                obstacle.transform.SetParent(obstaclesParent.transform, false);
            }
            
            Debug.Log($"[TutorialZoneBuilder] Created {config.obstaclePositions.Count} obstacles for zone: {config.zoneName}");
        }
        
        private GameObject CreateObstacle(Vector3 position, int index)
        {
            GameObject obstacle;
            
            if (obstaclePrefab != null)
            {
                obstacle = Instantiate(obstaclePrefab, position, Quaternion.identity);
            }
            else
            {
                // Create basic obstacle
                obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
                obstacle.transform.position = position;
                obstacle.transform.localScale = new Vector3(2f, 1f, 2f);
                
                // Set material
                if (obstacleMaterial != null)
                {
                    obstacle.GetComponent<Renderer>().material = obstacleMaterial;
                }
                else
                {
                    obstacle.GetComponent<Renderer>().material.color = Color.gray;
                }
            }
            
            obstacle.name = $"TutorialObstacle_{index}";
            obstacle.tag = "Obstacle";
            
            // Make it a navigation obstacle
            obstacle.isStatic = true;
            
            return obstacle;
        }
        
        private void CreateZoneHidingSpots(GameObject parent, TutorialZoneConfig config)
        {
            GameObject hidingSpotsParent = new GameObject("HidingSpots");
            hidingSpotsParent.transform.SetParent(parent.transform, false);
            
            for (int i = 0; i < config.hidingSpotPositions.Count; i++)
            {
                // Calculate world position relative to zone transform
                Vector3 worldPos = parent.transform.position + config.zoneCenter + config.hidingSpotPositions[i];
                GameObject hidingSpot = CreateHidingSpot(worldPos, i);
                hidingSpot.transform.SetParent(hidingSpotsParent.transform, false);
            }
            
            Debug.Log($"[TutorialZoneBuilder] Created {config.hidingSpotPositions.Count} hiding spots for zone: {config.zoneName}");
        }
        
        private GameObject CreateHidingSpot(Vector3 position, int index)
        {
            GameObject hidingSpot;
            
            if (hidingSpotPrefab != null)
            {
                hidingSpot = Instantiate(hidingSpotPrefab, position, Quaternion.identity);
            }
            else
            {
                // Create basic hiding spot
                hidingSpot = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                hidingSpot.transform.position = position;
                hidingSpot.transform.localScale = new Vector3(2f, 0.1f, 2f);
                
                // Remove collider so player can walk through
                Collider collider = hidingSpot.GetComponent<Collider>();
                if (collider != null)
                {
                    collider.isTrigger = true;
                }
                
                // Set material
                if (hidingSpotMaterial != null)
                {
                    hidingSpot.GetComponent<Renderer>().material = hidingSpotMaterial;
                }
                else
                {
                    hidingSpot.GetComponent<Renderer>().material.color = Color.green;
                }
            }
            
            hidingSpot.name = $"TutorialHidingSpot_{index}";
            hidingSpot.tag = "HidingSpot";
            
            // Add HidingSpot component if it doesn't exist
            if (hidingSpot.GetComponent<HidingSpot>() == null)
            {
                hidingSpot.AddComponent<HidingSpot>();
            }
            
            return hidingSpot;
        }
        
        private void CreateZoneObjective(GameObject parent, TutorialZoneConfig config)
        {
            // Calculate world position relative to zone transform
            Vector3 worldPos = parent.transform.position + config.zoneCenter + config.completionPosition;
            GameObject objective = CreateObjective(worldPos);
            objective.transform.SetParent(parent.transform, false);

            Debug.Log($"[TutorialZoneBuilder] Created objective for zone: {config.zoneName}");
        }
        
        private GameObject CreateObjective(Vector3 position)
        {
            GameObject objective;
            
            if (objectivePrefab != null)
            {
                objective = Instantiate(objectivePrefab, position, Quaternion.identity);
            }
            else
            {
                // Create basic objective
                objective = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                objective.transform.position = position;
                objective.transform.localScale = Vector3.one * 1.5f;
                
                // Remove collider or make it trigger
                Collider collider = objective.GetComponent<Collider>();
                if (collider != null)
                {
                    collider.isTrigger = true;
                }
                
                // Set material
                if (objectiveMaterial != null)
                {
                    objective.GetComponent<Renderer>().material = objectiveMaterial;
                }
                else
                {
                    objective.GetComponent<Renderer>().material.color = Color.cyan;
                }
            }
            
            objective.name = "TutorialObjective";
            objective.tag = "Objective";
            
            return objective;
        }
        
        private void CreateZoneBoundary(GameObject parent, TutorialZoneConfig config)
        {
            GameObject boundary;
            
            if (zoneBoundaryPrefab != null)
            {
                // Calculate world position relative to zone transform
                Vector3 worldPos = parent.transform.position + config.zoneCenter;
                boundary = Instantiate(zoneBoundaryPrefab, worldPos, Quaternion.identity);
            }
            else
            {
                // Create basic boundary visualization
                boundary = GameObject.CreatePrimitive(PrimitiveType.Cube);
                // Calculate world position relative to zone transform
                Vector3 worldPos = parent.transform.position + config.zoneCenter;
                boundary.transform.position = worldPos;
                boundary.transform.localScale = config.zoneSize;
                
                // Make it wireframe only
                Renderer renderer = boundary.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material boundaryMat = new Material(Shader.Find("Standard"));
                    boundaryMat.color = new Color(1f, 1f, 0f, 0.2f);
                    boundaryMat.SetFloat("_Mode", 3); // Transparent mode
                    renderer.material = boundaryMat;
                }
                
                // Remove collider
                Collider collider = boundary.GetComponent<Collider>();
                if (collider != null)
                {
                    DestroyImmediate(collider);
                }
            }
            
            boundary.name = "ZoneBoundary";
            boundary.transform.SetParent(parent.transform, false);
        }
        
        private void BuildNavMesh()
        {
#if UNITY_EDITOR
            // Mark all static objects for navigation
            GameObject[] staticObjects = GameObject.FindGameObjectsWithTag("Obstacle");
            foreach (var obj in staticObjects)
            {
                UnityEditor.GameObjectUtility.SetStaticEditorFlags(obj, UnityEditor.StaticEditorFlags.NavigationStatic);
            }
            
            // Bake NavMesh
            UnityEditor.AI.NavMeshBuilder.BuildNavMesh();
            Debug.Log("[TutorialZoneBuilder] NavMesh built successfully");
#endif
        }
        
        public void ClearExistingZones()
        {
            foreach (var zoneObj in builtZoneObjects)
            {
                if (zoneObj != null)
                {
                    DestroyImmediate(zoneObj);
                }
            }
            builtZoneObjects.Clear();
            
            Debug.Log("[TutorialZoneBuilder] Cleared existing zones");
        }
        
        // Utility methods
        public void SetPrefabs(GameObject enemy, GameObject obstacle, GameObject hidingSpot, GameObject objective)
        {
            enemyPrefab = enemy;
            obstaclePrefab = obstacle;
            hidingSpotPrefab = hidingSpot;
            objectivePrefab = objective;
        }
        
        public void SetMaterials(Material enemy, Material obstacle, Material hidingSpot, Material objective)
        {
            enemyMaterial = enemy;
            obstacleMaterial = obstacle;
            hidingSpotMaterial = hidingSpot;
            objectiveMaterial = objective;
        }
    }
}

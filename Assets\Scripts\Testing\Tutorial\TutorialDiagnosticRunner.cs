using UnityEngine;
using AshesOfTheGrove.Tutorial;

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Simple runner to set up and run tutorial diagnostics
    /// Use this to quickly test the tutorial system
    /// </summary>
    public class TutorialDiagnosticRunner : MonoBehaviour
    {
        [Header("Setup")]
        [SerializeField] private bool setupTutorialOnStart = true;
        [SerializeField] private bool runDiagnosticsOnStart = true;
        
        [Header("Manual Controls")]
        [SerializeField] private KeyCode setupKey = KeyCode.F9;
        [SerializeField] private KeyCode diagnosticsKey = KeyCode.F12;
        [SerializeField] private KeyCode startTutorialKey = KeyCode.F11;
        
        private ModularTutorialSceneSetup sceneSetup;
        private TutorialDiagnostics diagnostics;
        
        private void Start()
        {
            if (setupTutorialOnStart)
            {
                SetupTutorialSystem();
            }
            
            if (runDiagnosticsOnStart)
            {
                // Wait a frame for setup to complete
                Invoke(nameof(RunDiagnostics), 0.1f);
            }
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(setupKey))
            {
                SetupTutorialSystem();
            }
            
            if (Input.GetKeyDown(diagnosticsKey))
            {
                RunDiagnostics();
            }
            
            if (Input.GetKeyDown(startTutorialKey))
            {
                StartTutorial();
            }
        }
        
        [ContextMenu("Setup Tutorial System")]
        public void SetupTutorialSystem()
        {
            Debug.Log("[TutorialDiagnosticRunner] Setting up tutorial system...");
            
            // Find or create scene setup
            sceneSetup = FindObjectOfType<ModularTutorialSceneSetup>();
            if (sceneSetup == null)
            {
                GameObject setupObj = new GameObject("ModularTutorialSceneSetup");
                sceneSetup = setupObj.AddComponent<ModularTutorialSceneSetup>();
                Debug.Log("[TutorialDiagnosticRunner] Created ModularTutorialSceneSetup");
            }
            
            // Setup the tutorial scene
            sceneSetup.SetupTutorialScene();
            
            Debug.Log("[TutorialDiagnosticRunner] Tutorial system setup complete");
        }
        
        [ContextMenu("Run Diagnostics")]
        public void RunDiagnostics()
        {
            Debug.Log("[TutorialDiagnosticRunner] Running diagnostics...");
            
            // Find or create diagnostics
            diagnostics = FindObjectOfType<TutorialDiagnostics>();
            if (diagnostics == null)
            {
                GameObject diagObj = new GameObject("TutorialDiagnostics");
                diagnostics = diagObj.AddComponent<TutorialDiagnostics>();
                Debug.Log("[TutorialDiagnosticRunner] Created TutorialDiagnostics");
            }
            
            // Force run diagnostics
            diagnostics.StartCoroutine("RunCompleteDiagnostics");
        }
        
        [ContextMenu("Start Tutorial")]
        public void StartTutorial()
        {
            Debug.Log("[TutorialDiagnosticRunner] Starting tutorial...");
            
            TutorialZoneManager zoneManager = FindObjectOfType<TutorialZoneManager>();
            if (zoneManager != null)
            {
                zoneManager.StartTutorial();
            }
            else
            {
                Debug.LogError("[TutorialDiagnosticRunner] No TutorialZoneManager found!");
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            GUILayout.Box("Tutorial Diagnostic Runner");
            
            if (GUILayout.Button($"Setup Tutorial System ({setupKey})"))
            {
                SetupTutorialSystem();
            }
            
            if (GUILayout.Button($"Run Diagnostics ({diagnosticsKey})"))
            {
                RunDiagnostics();
            }
            
            if (GUILayout.Button($"Start Tutorial ({startTutorialKey})"))
            {
                StartTutorial();
            }
            
            GUILayout.Space(10);
            GUILayout.Label("Instructions:");
            GUILayout.Label("1. Click 'Setup Tutorial System' first");
            GUILayout.Label("2. Click 'Run Diagnostics' to check status");
            GUILayout.Label("3. Click 'Start Tutorial' to begin");
            
            GUILayout.EndArea();
        }
    }
}

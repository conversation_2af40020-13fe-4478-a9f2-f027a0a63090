%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1441be28f8690244b9d4b93594f8197f, type: 3}
  m_Name: TutorialZoneConfigurations
  m_EditorClassIdentifier: 
  zoneConfigs:
  - zoneName: Basic Movement
    zoneDescription: Learn to move your character using WASD keys
    zoneIndex: 0
    zoneCenter: {x: 0, y: 0, z: 0}
    zoneSize: {x: 15, y: 5, z: 15}
    playerStartPosition: {x: -5, y: 0, z: -5}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Use WASD keys to move around
    - Walk to the green objective marker
    instructions:
    - Welcome to Ashes of the Grove!
    - Use WASD keys to move your character
    - Walk to the green marker to complete this zone
    completionType: 0
    completionPosition: {x: 5, y: 0, z: 5}
    completionRadius: 2
    completionTime: 0
    enemyPositions: []
    obstaclePositions:
    - {x: 0, y: 0, z: 0}
    - {x: -2, y: 0, z: 2}
    - {x: 2, y: 0, z: -2}
    hidingSpotPositions: []
  - zoneName: Stealth Basics - Crouching
    zoneDescription: Learn to crouch for stealth movement
    zoneIndex: 1
    zoneCenter: {x: 25, y: 0, z: 0}
    zoneSize: {x: 15, y: 5, z: 15}
    playerStartPosition: {x: -5, y: 0, z: -5}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Press Left Ctrl to crouch
    - Move while crouching to the objective
    instructions:
    - Crouching makes you harder to detect
    - Hold Left Ctrl to crouch
    - Crouch and move to the objective
    completionType: 0
    completionPosition: {x: 5, y: 0, z: 5}
    completionRadius: 2
    completionTime: 0
    enemyPositions: []
    obstaclePositions:
    - {x: 0, y: 0, z: 0}
    - {x: -3, y: 0, z: 3}
    - {x: 3, y: 0, z: -3}
    hidingSpotPositions:
    - {x: -2, y: 0, z: 0}
    - {x: 2, y: 0, z: 0}
  - zoneName: Movement Speed - Running
    zoneDescription: Learn to run for faster movement
    zoneIndex: 2
    zoneCenter: {x: 50, y: 0, z: 0}
    zoneSize: {x: 20, y: 5, z: 15}
    playerStartPosition: {x: -8, y: 0, z: 0}
    playerStartRotation: {x: 0, y: 90, z: 0}
    objectives:
    - Hold Left Shift to run
    - Run to the objective quickly
    instructions:
    - Running allows faster movement
    - Hold Left Shift while moving to run
    - But be careful - running makes more noise!
    completionType: 0
    completionPosition: {x: 8, y: 0, z: 0}
    completionRadius: 2
    completionTime: 0
    enemyPositions: []
    obstaclePositions:
    - {x: -2, y: 0, z: -3}
    - {x: 2, y: 0, z: 3}
    - {x: 0, y: 0, z: -5}
    hidingSpotPositions: []
  - zoneName: Enemy Awareness
    zoneDescription: Learn about enemy field of view and detection
    zoneIndex: 3
    zoneCenter: {x: 0, y: 0, z: 25}
    zoneSize: {x: 20, y: 5, z: 20}
    playerStartPosition: {x: -8, y: 0, z: -8}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Observe the enemy's field of view cone
    - Avoid the red detection area
    - Reach the objective without being seen
    instructions:
    - Enemies have a field of view shown as a cone
    - Stay out of the red area to avoid detection
    - Sneak around to reach the objective
    completionType: 4
    completionPosition: {x: 8, y: 0, z: 8}
    completionRadius: 2
    completionTime: 0
    enemyPositions:
    - {x: 0, y: 0, z: 0}
    obstaclePositions:
    - {x: -4, y: 0, z: 2}
    - {x: 4, y: 0, z: -2}
    - {x: -2, y: 0, z: -4}
    - {x: 2, y: 0, z: 4}
    hidingSpotPositions:
    - {x: -6, y: 0, z: -2}
    - {x: 6, y: 0, z: 2}
    - {x: -2, y: 0, z: -6}
    - {x: 2, y: 0, z: 6}
  - zoneName: Using Cover and Hiding
    zoneDescription: Learn to use hiding spots and cover
    zoneIndex: 4
    zoneCenter: {x: 25, y: 0, z: 25}
    zoneSize: {x: 20, y: 5, z: 20}
    playerStartPosition: {x: -8, y: 0, z: -8}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Use hiding spots to avoid detection
    - Move from cover to cover
    - Reach the objective safely
    instructions:
    - Green areas are hiding spots
    - Stay in hiding spots when enemies are near
    - Move between hiding spots to stay hidden
    completionType: 4
    completionPosition: {x: 8, y: 0, z: 8}
    completionRadius: 2
    completionTime: 0
    enemyPositions:
    - {x: -2, y: 0, z: 2}
    - {x: 2, y: 0, z: -2}
    obstaclePositions:
    - {x: 0, y: 0, z: 0}
    - {x: -5, y: 0, z: 0}
    - {x: 5, y: 0, z: 0}
    - {x: 0, y: 0, z: -5}
    - {x: 0, y: 0, z: 5}
    hidingSpotPositions:
    - {x: -6, y: 0, z: -6}
    - {x: -3, y: 0, z: 0}
    - {x: 0, y: 0, z: -3}
    - {x: 3, y: 0, z: 3}
    - {x: 6, y: 0, z: 6}
  - zoneName: Enemy Patrol Patterns
    zoneDescription: Learn to predict and avoid enemy patrols
    zoneIndex: 5
    zoneCenter: {x: 50, y: 0, z: 25}
    zoneSize: {x: 25, y: 5, z: 20}
    playerStartPosition: {x: -10, y: 0, z: -8}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Observe enemy patrol routes
    - Time your movement between patrols
    - Reach the objective undetected
    instructions:
    - Enemies follow patrol routes
    - Watch their patterns and timing
    - Move when they're looking away
    completionType: 4
    completionPosition: {x: 10, y: 0, z: 8}
    completionRadius: 2
    completionTime: 0
    enemyPositions:
    - {x: -5, y: 0, z: 0}
    - {x: 5, y: 0, z: 0}
    - {x: 0, y: 0, z: -5}
    obstaclePositions:
    - {x: -8, y: 0, z: -3}
    - {x: 8, y: 0, z: 3}
    - {x: -3, y: 0, z: 8}
    - {x: 3, y: 0, z: -8}
    - {x: 0, y: 0, z: 0}
    hidingSpotPositions:
    - {x: -7, y: 0, z: -7}
    - {x: -7, y: 0, z: 7}
    - {x: 7, y: 0, z: -7}
    - {x: 7, y: 0, z: 7}
    - {x: 0, y: 0, z: -8}
    - {x: 0, y: 0, z: 8}
  - zoneName: Advanced Stealth Challenge
    zoneDescription: Put all your skills together in a final challenge
    zoneIndex: 6
    zoneCenter: {x: 0, y: 0, z: 50}
    zoneSize: {x: 30, y: 5, z: 25}
    playerStartPosition: {x: -12, y: 0, z: -10}
    playerStartRotation: {x: 0, y: 45, z: 0}
    objectives:
    - Use all stealth techniques learned
    - Navigate through multiple enemies
    - Reach the final objective
    instructions:
    - This is your final test!
    - Use crouching, hiding, and timing
    - Avoid all enemies to complete the tutorial
    completionType: 4
    completionPosition: {x: 12, y: 0, z: 10}
    completionRadius: 2
    completionTime: 0
    enemyPositions:
    - {x: -8, y: 0, z: -5}
    - {x: -4, y: 0, z: 0}
    - {x: 0, y: 0, z: -8}
    - {x: 4, y: 0, z: 0}
    - {x: 8, y: 0, z: 5}
    obstaclePositions:
    - {x: -10, y: 0, z: -8}
    - {x: -6, y: 0, z: -4}
    - {x: -2, y: 0, z: 0}
    - {x: 2, y: 0, z: 0}
    - {x: 6, y: 0, z: 4}
    - {x: 10, y: 0, z: 8}
    - {x: 0, y: 0, z: -10}
    - {x: 0, y: 0, z: 10}
    hidingSpotPositions:
    - {x: -12, y: 0, z: -8}
    - {x: -8, y: 0, z: -12}
    - {x: -4, y: 0, z: -8}
    - {x: 0, y: 0, z: -4}
    - {x: 4, y: 0, z: 8}
    - {x: 8, y: 0, z: 12}
    - {x: 12, y: 0, z: 8}
    - {x: -6, y: 0, z: 6}
    - {x: 6, y: 0, z: -6}

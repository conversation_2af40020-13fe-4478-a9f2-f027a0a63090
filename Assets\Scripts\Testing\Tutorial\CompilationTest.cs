using UnityEngine;
using AshesOfTheGrove.Tutorial;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Simple compilation test to verify all tutorial system components compile correctly
    /// </summary>
    public class CompilationTest : MonoBehaviour
    {
        [Header("Compilation Test")]
        [SerializeField] private bool runTestOnStart = true;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                RunCompilationTest();
            }
        }
        
        [ContextMenu("Run Compilation Test")]
        public void RunCompilationTest()
        {
            Debug.Log("=== COMPILATION TEST STARTING ===");
            
            try
            {
                // Test TutorialZoneConfigurations
                var configs = ScriptableObject.CreateInstance<TutorialZoneConfigurations>();
                Debug.Log($"✓ TutorialZoneConfigurations created successfully");
                
                // Test TutorialZoneConfig access
                var allConfigs = configs.GetAllConfigs();
                Debug.Log($"✓ TutorialZoneConfig access successful - found {allConfigs.Count} configs");
                
                // Test property access
                foreach (var config in allConfigs)
                {
                    Debug.Log($"✓ Zone: {config.zoneName}, Enemies: {config.enemyPositions.Count}, Objectives: {config.objectives.Count}, Hiding Spots: {config.hidingSpotPositions.Count}");
                }
                
                // Test TutorialZoneBuilder
                var builder = gameObject.AddComponent<TutorialZoneBuilder>();
                Debug.Log($"✓ TutorialZoneBuilder component added successfully");
                
                // Test TutorialZoneManager
                var manager = gameObject.AddComponent<TutorialZoneManager>();
                Debug.Log($"✓ TutorialZoneManager component added successfully");
                
                // Test TutorialDiagnostics
                var diagnostics = gameObject.AddComponent<TutorialDiagnostics>();
                Debug.Log($"✓ TutorialDiagnostics component added successfully");
                
                // Test PlayerControllerModular reference
                var player = FindObjectOfType<PlayerControllerModular>();
                if (player != null)
                {
                    Debug.Log($"✓ PlayerControllerModular found: {player.name}");
                    Debug.Log($"✓ Player properties accessible - IsCrouching: {player.IsCrouching}, IsRunning: {player.IsRunning}");
                }
                else
                {
                    Debug.Log("⚠ PlayerControllerModular not found in scene");
                }
                
                Debug.Log("=== COMPILATION TEST COMPLETED SUCCESSFULLY ===");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"✗ COMPILATION TEST FAILED: {e.Message}");
                Debug.LogError($"Stack trace: {e.StackTrace}");
            }
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F6))
            {
                RunCompilationTest();
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 200, 300, 100));
            GUILayout.Box("Compilation Test");
            
            if (GUILayout.Button("Run Compilation Test (F6)"))
            {
                RunCompilationTest();
            }
            
            GUILayout.Label("This test verifies all tutorial components compile correctly");
            GUILayout.EndArea();
        }
    }
}

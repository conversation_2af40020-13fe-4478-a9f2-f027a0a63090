using UnityEngine;
using UnityEngine.InputSystem;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Simple test script to verify Input System integration works correctly
    /// </summary>
    public class TutorialInputTest : MonoBehaviour
    {
        [Header("Input Test Settings")]
        [SerializeField] private bool logInputEvents = true;
        
        void Update()
        {
            if (Keyboard.current == null) return;
            
            // Test basic input functionality
            if (Keyboard.current.spaceKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] Space key pressed - Input System working correctly!");
            }
            
            if (Keyboard.current.enterKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] Enter key pressed - Input System working correctly!");
            }
            
            // Test WASD movement keys
            if (Keyboard.current.wKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] W key pressed - Movement input working!");
            }
            
            if (Keyboard.current.sKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] S key pressed - Movement input working!");
            }
            
            if (Keyboard.current.aKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] A key pressed - Movement input working!");
            }
            
            if (Keyboard.current.dKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] D key pressed - Movement input working!");
            }
            
            // Test tutorial control keys
            if (Keyboard.current.rKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] R key pressed - Restart functionality working!");
            }
            
            if (Keyboard.current.hKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] H key pressed - Help functionality working!");
            }
            
            if (Keyboard.current.f4Key.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] F4 key pressed - Debug toggle working!");
            }
            
            if (Keyboard.current.vKey.wasPressedThisFrame && logInputEvents)
            {
                Debug.Log("[TutorialInputTest] V key pressed - Validation functionality working!");
            }
        }
        
        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, Screen.height - 150, 400, 140));
            GUILayout.Label("Tutorial Input System Test", GUI.skin.box);
            GUILayout.Label("Press keys to test Input System integration:");
            GUILayout.Label("• Space/Enter - Basic input test");
            GUILayout.Label("• WASD - Movement input test");
            GUILayout.Label("• R - Restart functionality test");
            GUILayout.Label("• H - Help functionality test");
            GUILayout.Label("• F4 - Debug toggle test");
            GUILayout.Label("• V - Validation test");
            GUILayout.EndArea();
        }
    }
}

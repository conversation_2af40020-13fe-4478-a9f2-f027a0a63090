using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using AshesOfTheGrove.GameSystems;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Manages multiple tutorial zones and handles progression between them
    /// Coordinates zone activation, player transitions, and overall tutorial flow
    /// </summary>
    public class TutorialZoneManager : MonoBehaviour
    {
        [Header("Zone Management")]
        [SerializeField] private List<TutorialZone> tutorialZones = new List<TutorialZone>();
        [SerializeField] private bool autoStartTutorial = true;
        [SerializeField] private bool autoProgressZones = true;
        
        [Header("Transition Settings")]
        [SerializeField] private float transitionDelay = 2f;
        [SerializeField] private bool smoothCameraTransitions = true;
        [SerializeField] private float cameraTransitionSpeed = 2f;
        
        [Header("UI References")]
        [SerializeField] private TutorialUI tutorialUI;
        
        // State
        private int currentZoneIndex = -1;
        private TutorialZone currentZone;
        private bool tutorialActive = false;
        private bool isTransitioning = false;
        
        // System References
        private CameraController cameraController;
        private InputManager inputManager;
        private GameObject player;
        
        // Events
        public System.Action<int> OnZoneChanged;
        public System.Action<TutorialZone> OnZoneStarted;
        public System.Action<TutorialZone> OnZoneFinished;
        public System.Action OnTutorialStarted;
        public System.Action OnTutorialCompleted;
        
        // Properties
        public int CurrentZoneIndex => currentZoneIndex;
        public TutorialZone CurrentZone => currentZone;
        public bool IsTutorialActive => tutorialActive;
        public int TotalZones => tutorialZones.Count;
        
        private void Awake()
        {
            InitializeZoneManager();
        }
        
        private void Start()
        {
            if (autoStartTutorial)
            {
                StartTutorial();
            }
        }
        
        private void InitializeZoneManager()
        {
            // Find system references
            cameraController = FindObjectOfType<CameraController>();
            inputManager = FindObjectOfType<InputManager>();
            player = GameObject.FindGameObjectWithTag("Player");
            
            // Find tutorial UI if not assigned
            if (tutorialUI == null)
            {
                tutorialUI = FindObjectOfType<TutorialUI>();
            }
            
            // Auto-find tutorial zones if not manually assigned
            if (tutorialZones.Count == 0)
            {
                TutorialZone[] foundZones = FindObjectsOfType<TutorialZone>();
                tutorialZones.AddRange(foundZones);
                
                // Sort zones by index
                tutorialZones.Sort((a, b) => a.ZoneIndex.CompareTo(b.ZoneIndex));
            }
            
            // Subscribe to zone events
            foreach (var zone in tutorialZones)
            {
                zone.OnZoneCompleted += OnZoneCompleted;
                zone.OnZoneEntered += OnZoneEntered;
                zone.OnZoneExited += OnZoneExited;
            }
            
            Debug.Log($"[TutorialZoneManager] Initialized with {tutorialZones.Count} zones");
        }
        
        public void StartTutorial()
        {
            if (tutorialActive) return;
            
            tutorialActive = true;
            currentZoneIndex = -1;
            
            Debug.Log("[TutorialZoneManager] Starting tutorial...");
            
            // Show welcome message
            if (tutorialUI != null)
            {
                tutorialUI.ShowWelcomeMessage();
            }
            
            OnTutorialStarted?.Invoke();
            
            // Start first zone after a brief delay
            StartCoroutine(StartFirstZone());
        }
        
        private IEnumerator StartFirstZone()
        {
            yield return new WaitForSeconds(1f);
            ActivateZone(0);
        }
        
        public void ActivateZone(int zoneIndex)
        {
            if (zoneIndex < 0 || zoneIndex >= tutorialZones.Count)
            {
                Debug.LogWarning($"[TutorialZoneManager] Invalid zone index: {zoneIndex}");
                return;
            }
            
            if (isTransitioning)
            {
                Debug.LogWarning("[TutorialZoneManager] Cannot activate zone while transitioning");
                return;
            }
            
            StartCoroutine(TransitionToZone(zoneIndex));
        }
        
        private IEnumerator TransitionToZone(int zoneIndex)
        {
            isTransitioning = true;
            
            // Deactivate current zone
            if (currentZone != null)
            {
                currentZone.SetZoneActive(false);
                OnZoneFinished?.Invoke(currentZone);
            }
            
            // Update current zone
            currentZoneIndex = zoneIndex;
            currentZone = tutorialZones[zoneIndex];
            
            Debug.Log($"[TutorialZoneManager] Transitioning to zone {zoneIndex}: {currentZone.ZoneName}");
            
            // Move player to zone start position
            if (player != null)
            {
                currentZone.MovePlayerToStart();
            }
            
            // Handle camera transition
            if (smoothCameraTransitions && cameraController != null)
            {
                yield return StartCoroutine(TransitionCamera());
            }
            
            // Activate new zone
            currentZone.SetZoneActive(true);
            
            // Update UI
            if (tutorialUI != null)
            {
                tutorialUI.UpdateZone(currentZone);
                StartCoroutine(ShowZoneInstructions());
            }
            
            // Fire events
            OnZoneChanged?.Invoke(currentZoneIndex);
            OnZoneStarted?.Invoke(currentZone);
            
            isTransitioning = false;
            
            Debug.Log($"[TutorialZoneManager] Zone {zoneIndex} activated: {currentZone.ZoneName}");
        }
        
        private IEnumerator TransitionCamera()
        {
            // Smooth camera transition logic
            // For now, just a simple delay
            yield return new WaitForSeconds(0.5f);
        }
        
        private IEnumerator ShowZoneInstructions()
        {
            if (currentZone == null || tutorialUI == null) yield break;
            
            foreach (string instruction in currentZone.Instructions)
            {
                tutorialUI.ShowInstruction(instruction);
                yield return new WaitForSeconds(3f);
            }
        }
        
        public void OnZoneCompleted(TutorialZone completedZone)
        {
            Debug.Log($"[TutorialZoneManager] Zone completed: {completedZone.ZoneName}");
            
            if (tutorialUI != null)
            {
                tutorialUI.ShowZoneComplete(completedZone.ZoneName);
            }
            
            if (autoProgressZones)
            {
                StartCoroutine(ProgressToNextZone());
            }
        }
        
        private IEnumerator ProgressToNextZone()
        {
            yield return new WaitForSeconds(transitionDelay);
            
            int nextZoneIndex = currentZoneIndex + 1;
            if (nextZoneIndex < tutorialZones.Count)
            {
                ActivateZone(nextZoneIndex);
            }
            else
            {
                CompleteTutorial();
            }
        }
        
        private void CompleteTutorial()
        {
            tutorialActive = false;
            
            Debug.Log("[TutorialZoneManager] Tutorial completed!");
            
            if (tutorialUI != null)
            {
                tutorialUI.ShowTutorialComplete();
            }
            
            OnTutorialCompleted?.Invoke();
        }
        
        // Zone event handlers
        private void OnZoneEntered(TutorialZone zone)
        {
            Debug.Log($"[TutorialZoneManager] Player entered zone: {zone.ZoneName}");
        }
        
        private void OnZoneExited(TutorialZone zone)
        {
            Debug.Log($"[TutorialZoneManager] Player exited zone: {zone.ZoneName}");
        }
        
        // Public control methods
        public void SkipToZone(int zoneIndex)
        {
            if (!tutorialActive) return;
            
            ActivateZone(zoneIndex);
        }
        
        public void RestartCurrentZone()
        {
            if (currentZone != null)
            {
                currentZone.ResetZone();
                ActivateZone(currentZoneIndex);
            }
        }
        
        public void RestartTutorial()
        {
            // Reset all zones
            foreach (var zone in tutorialZones)
            {
                zone.ResetZone();
            }
            
            tutorialActive = false;
            currentZoneIndex = -1;
            currentZone = null;
            
            StartTutorial();
        }
        
        public void PauseTutorial()
        {
            if (inputManager != null)
            {
                inputManager.EnableInput(false);
            }
            
            Time.timeScale = 0f;
            Debug.Log("[TutorialZoneManager] Tutorial paused");
        }
        
        public void ResumeTutorial()
        {
            if (inputManager != null)
            {
                inputManager.EnableInput(true);
            }
            
            Time.timeScale = 1f;
            Debug.Log("[TutorialZoneManager] Tutorial resumed");
        }
        
        // Utility methods
        public TutorialZone GetZone(int index)
        {
            if (index >= 0 && index < tutorialZones.Count)
            {
                return tutorialZones[index];
            }
            return null;
        }
        
        public List<TutorialZone> GetAllZones()
        {
            return new List<TutorialZone>(tutorialZones);
        }
        
        public float GetTutorialProgress()
        {
            if (tutorialZones.Count == 0) return 0f;
            
            int completedZones = 0;
            foreach (var zone in tutorialZones)
            {
                if (zone.IsCompleted) completedZones++;
            }
            
            return (float)completedZones / tutorialZones.Count;
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from zone events
            foreach (var zone in tutorialZones)
            {
                if (zone != null)
                {
                    zone.OnZoneCompleted -= OnZoneCompleted;
                    zone.OnZoneEntered -= OnZoneEntered;
                    zone.OnZoneExited -= OnZoneExited;
                }
            }
        }
    }
}

using UnityEngine;

namespace AshesOfTheGrove.Data
{
    /// <summary>
    /// ScriptableObject for defining mission parameters and objectives
    /// </summary>
    [CreateAssetMenu(fileName = "New Mission", menuName = "Ashes of the Grove/Mission Data")]
    public class MissionData : ScriptableObject
    {
        [Header("Mission Info")]
        public string missionName;
        public string missionDescription;
        public Sprite missionIcon;
        
        [Head<PERSON>("Objectives")]
        public MissionObjective[] objectives;
        
        [<PERSON><PERSON>("Settings")]
        public float timeLimit = 0f; // 0 = no time limit
        public bool allowAlarms = true;
        public int maxAlarmCount = 3;
        
        [<PERSON><PERSON>("Rewards")]
        public int scoreReward = 1000;
        public string[] unlockedItems;
    }
    
    [System.Serializable]
    public class MissionObjective
    {
        public string objectiveText;
        public ObjectiveType type;
        public bool isOptional = false;
        public bool isCompleted = false;
        
        public enum ObjectiveType
        {
            Reach,
            Collect,
            Avoid,
            Eliminate,
            Interact
        }
    }
}

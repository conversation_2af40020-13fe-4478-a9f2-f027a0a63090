# Player Input Testing Guide

## Overview
This guide will help you test the PlayerController input system for "Ashes of the Grove". The testing system includes automated setup, real-time debugging, and comprehensive test scenarios.

## Quick Start

### Option 1: Automatic Setup (Recommended)
1. Open the `TestScene` in Unity
2. Press Play - everything will be set up automatically
3. Use the controls listed below to test input

### Option 2: Manual Setup
1. Create an empty scene
2. Add a GameObject and attach the `TestSceneManager` script
3. Press Play or use the context menu "Setup Test Scene"

## Controls

### Basic Movement
- **WASD** or **Arrow Keys**: Move player
- **Left Ctrl**: Toggle crouch (press to toggle on/off)
- **Left Shift**: Hold to run (hold down for faster movement)

### Debug Controls
- **F1**: Toggle debug information panel
- **F2**: Toggle test instructions panel

## Test Scenarios

### 1. Basic Movement Test
**Objective**: Verify basic movement functionality
**Steps**:
1. Use WASD or arrow keys to move in all directions
2. Observe player movement and rotation
3. Check that movement is smooth and responsive

**Expected Results**:
- ✅ Player moves smoothly in all directions
- ✅ Player rotates to face movement direction
- ✅ Movement speed is consistent
- ✅ No jittery or erratic behavior

### 2. Stealth Mechanics Test
**Objective**: Test crouch and run functionality
**Steps**:
1. Press Left Ctrl to toggle crouch
2. Move while crouching
3. Hold Left Shift and move to run
4. Try crouching while running

**Expected Results**:
- ✅ Crouch toggles on/off with Left Ctrl
- ✅ Movement speed decreases when crouching
- ✅ Movement speed increases when running
- ✅ Cannot run while crouching

### 3. Noise Level Test
**Objective**: Verify noise level changes with movement type
**Steps**:
1. Enable debug panel (F1)
2. Stand still and observe noise level
3. Walk normally and observe noise level
4. Crouch and move, observe noise level
5. Run and observe noise level

**Expected Results**:
- ✅ Standing still: Low noise level
- ✅ Walking: Medium noise level
- ✅ Crouching: Lower noise level than walking
- ✅ Running: Higher noise level than walking

### 4. Combined Actions Test
**Objective**: Test multiple inputs simultaneously
**Steps**:
1. Move while holding crouch
2. Try to run while crouching (should not work)
3. Quick direction changes while moving
4. Rapid crouch toggle while moving

**Expected Results**:
- ✅ Crouch + movement works smoothly
- ✅ Cannot run while crouching
- ✅ Direction changes are responsive
- ✅ Rapid inputs don't break the system

### 5. Edge Cases Test
**Objective**: Test unusual input patterns
**Steps**:
1. Press multiple movement keys simultaneously
2. Rapidly press and release movement keys
3. Hold all movement keys at once
4. Press crouch and run simultaneously

**Expected Results**:
- ✅ Multiple movement keys result in diagonal movement
- ✅ Rapid inputs don't cause errors
- ✅ Conflicting inputs are handled gracefully
- ✅ No exceptions or errors in console

## Debug Information

### Debug Panel (F1)
The debug panel shows real-time information:
- **Input Values**: Current horizontal/vertical input
- **Control States**: Crouch and run button states
- **Player State**: Current crouching and hiding status
- **Noise Level**: Current noise level value
- **Speed**: Current movement speed

### Console Logging
Watch the Unity Console for:
- Input change notifications
- State transitions
- Error messages (there should be none!)

## Troubleshooting

### Common Issues

**Player doesn't move**:
- Check that the player has a Rigidbody component
- Verify the PlayerController script is attached
- Make sure the player isn't stuck in geometry

**Input not responding**:
- Check Unity's Input Manager settings
- Verify WASD and arrow keys are mapped correctly
- Try different keys to isolate the issue

**Jerky movement**:
- Check that Time.deltaTime is being used correctly
- Verify Rigidbody drag settings
- Look for conflicting physics interactions

**Debug panel not showing**:
- Press F1 to toggle the debug panel
- Check that InputTester script is attached to player
- Verify the script is enabled

### Performance Notes
- The test system logs input changes to avoid console spam
- Debug information updates every frame for real-time feedback
- Test obstacles are provided for collision testing

## Test Results Checklist

Use this checklist to verify all functionality:

**Basic Movement**:
- [ ] WASD movement works
- [ ] Arrow key movement works
- [ ] Player rotates to face movement direction
- [ ] Movement is smooth and responsive

**Stealth Mechanics**:
- [ ] Left Ctrl toggles crouch
- [ ] Crouch reduces movement speed
- [ ] Left Shift increases movement speed (run)
- [ ] Cannot run while crouching

**Audio/Noise System**:
- [ ] Noise level changes with movement type
- [ ] Standing still has lowest noise
- [ ] Running has highest noise
- [ ] Crouching reduces noise level

**Input Responsiveness**:
- [ ] No input lag or delay
- [ ] Rapid inputs handled correctly
- [ ] Multiple simultaneous inputs work
- [ ] No console errors during testing

**Visual Feedback**:
- [ ] Player visual representation moves correctly
- [ ] Direction indicator shows facing direction
- [ ] Debug information updates in real-time
- [ ] Test environment renders properly

## Next Steps

After completing input testing:
1. Test integration with enemy AI detection
2. Verify stealth mechanics work with FieldOfView system
3. Test input with different camera angles
4. Performance testing with multiple enemies
5. Integration testing with full game systems

## Files Created for Testing

- `InputTester.cs`: Real-time input monitoring and debugging
- `TestSceneManager.cs`: Automated test scene setup
- `TestInstructions.cs`: In-game instruction display
- `PlayerSetupHelper.cs`: Manual player setup utilities
- `TestScene.unity`: Pre-configured test scene

All testing scripts are in the `AshesOfTheGrove.Testing` namespace and can be safely removed from production builds.

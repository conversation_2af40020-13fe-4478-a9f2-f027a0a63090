# Ashes of the Grove
*An isometric stealth game inspired by Metal Gear Solid 2*

## Project Structure

This Unity project follows a modular structure designed for stealth gameplay mechanics:

### 📁 Assets Organization

```
/Assets
├── /Art                    # Visual assets
│   ├── Characters         # Player & enemy models, textures
│   ├── Environments       # Level geometry, props, lighting
│   └── UI                 # HUD elements, menus, icons
├── /Audio                 # Sound assets
│   ├── Music             # Background music, ambient tracks
│   └── SFX               # Sound effects, footsteps, UI sounds
├── /Prefabs              # Reusable game objects
│   ├── Enemies           # Guard types, patrol units
│   ├── Player            # Player character variants
│   └── Interactables     # Doors, alarms, cameras, items
├── /Scenes               # Unity scene files
│   ├── Mission_01_Wiretap.unity
│   ├── Mission_02_Checkpoint.unity
│   └── MainMenu.unity
├── /Scripts              # C# code organization
│   ├── Player/           # Player controller & mechanics
│   ├── AI/               # Enemy AI & field of view
│   ├── GameSystems/      # Alarm system, game management
│   ├── UI/               # User interface controllers
│   └── ScriptableObjects/ # Data definitions
├── /Animations           # Animation controllers & clips
├── /Materials            # Material assets
├── /Shaders              # Custom shaders (optional)
├── /Timeline             # Cinematics & cutscenes
└── /ScriptableObjects    # Data assets (missions, enemies)
```

## 🎮 Core Systems

### Player Controller
- **Isometric movement** with WASD controls
- **Stealth mechanics**: Crouching, hiding, noise levels
- **Speed variants**: Walk, crouch, run with different noise levels

### AI System
- **State-based AI**: Patrol, Alert, Search, Chase
- **Field of View**: Realistic vision cones with obstruction detection
- **Detection system**: Progressive detection with stealth modifiers

### Alarm System
- **Alert levels**: Clear, Caution, Alert, Evasion
- **Reinforcement spawning** during high alert
- **Automatic cooldown** system

## 🛠️ Getting Started

1. **Unity Version**: Recommended Unity 2022.3 LTS or newer
2. **Required Packages**:
   - NavMesh Components
   - Timeline
   - Cinemachine (for camera control)

3. **Setup**:
   - Import the project into Unity
   - Set up NavMesh for AI pathfinding
   - Configure input system for player controls

## 📋 Development Roadmap

### Phase 1: Core Mechanics
- [x] Basic project structure
- [x] Player movement system
- [x] Enemy AI with field of view
- [x] Alarm system foundation
- [ ] Basic UI implementation

### Phase 2: Game Systems
- [ ] Mission system integration
- [ ] Save/load functionality
- [ ] Audio system implementation
- [ ] Animation integration

### Phase 3: Content Creation
- [ ] Level design tools
- [ ] Mission scripting
- [ ] Cutscene system
- [ ] Polish and optimization

## 🎯 Design Philosophy

**Stealth-First Gameplay**: Every system is designed around avoiding detection rather than combat.

**Emergent Gameplay**: AI behaviors and systems interact to create unpredictable situations.

**Player Agency**: Multiple approaches to objectives with meaningful consequences for different playstyles.

## 📝 Notes

- All scripts use proper namespacing (`AshesOfTheGrove.*`)
- ScriptableObjects are used for data-driven design
- Modular architecture allows for easy expansion
- Gizmos are implemented for visual debugging

---

*Developed with Unity Engine*

# Shader Graph samples

## Description

The Shader Graph package offers sample Assets, which you can download through **Package Manager**. When you import these samples, <PERSON> places the files in your Project's Asset folder. The files contain examples that demonstrate how to use Shader Graph features.

## Add samples

To add samples to your Project, go to **Window** > **Package Manager**. Locate **Shader Graph** in the list of available packages, and select it. Under the package description, there is list of available samples. Click the **Import into Project** button next to the sample you wish to add.

![](images/PatternSamples_01.png)

Unity places imported samples in your Project's Asset folder under **Assets** > **Samples** > **Shader Graph** > **[version number]** > **[sample name]**. The example below shows the samples for **Procedural Patterns**.

![](images/PatternSamples_02.png)

## Available samples

The following samples are currently available for Shader Graph.

| Procedural Patterns |
|:--------------------|
|![](images/Patterns_Page.png) |
| This collection of Assets showcases various procedural techniques possible with Shader Graph. Use them directly in your Project, or edit them to create other procedural patterns. The patterns in this collection are: Bacteria, Brick, Dots, Grid, <PERSON>ingbone, Hex <PERSON>ttice, Houndstooth, Smooth Wave, Spiral, Stripes, Truchet, Whirl, Zig Zag. |


| Node Reference |
|:--------------------|
|![](images/NodeReferenceSamples.png) |
| This set of Shader Graph assets provides reference material for the nodes available in the Shader Graph node library. Each graph contains a description for a specific node, examples of how it can be used, and useful tips. Some example assets also show a break-down of the math that the node is doing. You can use these samples along with the documentation to learn more about the behavior of individual nodes. |

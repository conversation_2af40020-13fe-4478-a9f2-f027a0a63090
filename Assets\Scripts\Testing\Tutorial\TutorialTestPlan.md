# Tutorial System Test Plan

## Overview
This document outlines the comprehensive testing plan for the "Ashes of the Grove" tutorial system.

## Test Environment Setup

### Prerequisites
1. Unity 2022.3 LTS or later
2. All required packages installed (NavMesh, Cinemachine, Input System, Timeline, URP)
3. All tutorial scripts compiled without errors
4. TutorialScene.unity loaded

### Quick Start Testing
1. Open `Assets/Scenes/TutorialScene.unity`
2. Press Play in Unity Editor
3. The tutorial should automatically start with the TutorialDemo component

## Test Categories

### 1. System Integration Tests

#### 1.1 Component Discovery
- **Test**: All tutorial components are found and initialized
- **Expected**: TutorialManager, TutorialUI, TutorialSceneBuilder, etc. are all present
- **Validation**: Use TutorialSystemValidator (Press V in play mode)

#### 1.2 Scene Generation
- **Test**: Automatic scene building creates all required elements
- **Expected**: Player, enemies, walls, hiding spots, objectives are created
- **Visual Check**: Scene should show a structured tutorial environment

### 2. Tutorial Flow Tests

#### 2.1 Area Progression
- **Test**: Tutorial progresses through all 7 areas sequentially
- **Areas to Test**:
  1. Basic Movement
  2. Stealth Mechanics  
  3. Enemy Detection
  4. Using Cover
  5. Patrol Patterns
  6. Alert System
  7. Final Challenge

#### 2.2 Objective Completion
- **Test**: Each area's objectives can be completed
- **Expected**: Green objective markers turn blue when completed
- **Interaction**: Walk to objectives to trigger completion

### 3. Player Interaction Tests

#### 3.1 Movement Controls
- **Test**: WASD movement works correctly
- **Expected**: Player moves smoothly in all directions
- **Visual**: Blue player capsule responds to input

#### 3.2 Stealth Mechanics
- **Test**: Ctrl key toggles crouch/stealth mode
- **Expected**: Player moves slower when crouching
- **Visual**: Player capsule should change appearance or scale

### 4. AI Behavior Tests

#### 4.1 State Visualization
- **Test**: Enemy AI states are visually represented
- **Expected Colors**:
  - 🟢 Green: Patrol state
  - 🟡 Yellow: Alert state
  - 🟠 Orange: Search state
  - 🔴 Red: Chase state

#### 4.2 Detection System
- **Test**: Field of view detection works correctly
- **Expected**: Enemies detect player when in sight cone
- **Visual**: Detection cones should be visible (if debug mode enabled)

#### 4.3 Patrol Behavior
- **Test**: Enemies follow predefined patrol routes
- **Expected**: Red enemy capsules move between waypoints
- **Timing**: Should take reasonable time to complete patrol loops

### 5. Interactive Elements Tests

#### 5.1 Hiding Spots
- **Test**: Player can hide behind cover objects
- **Expected**: Green cube hiding spots provide concealment
- **Interaction**: Walk near hiding spots to trigger hiding state

#### 5.2 Objective Markers
- **Test**: Objective markers respond to player proximity
- **Expected**: Pulsing green markers with completion animations
- **Feedback**: UI should update when objectives are reached

### 6. UI System Tests

#### 6.1 Instruction Display
- **Test**: Tutorial instructions appear on screen
- **Expected**: Clear, readable text with current objectives
- **Timing**: Instructions should update as tutorial progresses

#### 6.2 Debug Information
- **Test**: F4 toggles debug information display
- **Expected**: Additional technical information when enabled
- **Content**: AI states, detection progress, area information

### 7. Control System Tests

#### 7.1 Tutorial Controls
- **Test**: All tutorial-specific controls work
- **Controls to Test**:
  - R: Restart tutorial
  - H: Toggle help instructions
  - F4: Toggle debug mode
  - Esc: Quit demo (in standalone build)

#### 7.2 Responsiveness
- **Test**: Controls respond immediately to input
- **Expected**: No input lag or delayed responses
- **Smoothness**: Transitions should be smooth and natural

## Performance Tests

### 8.1 Frame Rate
- **Test**: Tutorial maintains stable frame rate
- **Target**: 60+ FPS in Unity Editor
- **Monitor**: Unity Profiler for performance metrics

### 8.2 Memory Usage
- **Test**: No memory leaks during tutorial progression
- **Monitor**: Memory usage should remain stable
- **Duration**: Run tutorial multiple times to check for leaks

## Error Handling Tests

### 9.1 Component Failures
- **Test**: System handles missing components gracefully
- **Method**: Temporarily disable components and observe behavior
- **Expected**: Appropriate error messages, no crashes

### 9.2 Invalid States
- **Test**: System recovers from unexpected states
- **Method**: Force invalid AI states or player positions
- **Expected**: System resets or handles gracefully

## User Experience Tests

### 10.1 Learning Curve
- **Test**: Tutorial effectively teaches stealth mechanics
- **Method**: Have someone unfamiliar with the game try it
- **Expected**: Clear progression from basic to advanced concepts

### 10.2 Feedback Quality
- **Test**: UI feedback is helpful and informative
- **Expected**: Players understand what to do next
- **Clarity**: Instructions should be concise and actionable

## Regression Tests

### 11.1 Core Game Systems
- **Test**: Tutorial doesn't break existing game functionality
- **Check**: Player controller, AI systems work in non-tutorial contexts
- **Isolation**: Tutorial-specific code doesn't affect main game

### 11.2 Scene Transitions
- **Test**: Can transition from tutorial to main game
- **Expected**: Clean state management between scenes
- **Memory**: No lingering tutorial objects or states

## Test Execution Checklist

### Before Testing
- [ ] All scripts compile without errors
- [ ] TutorialScene.unity is properly configured
- [ ] Unity console is clear of warnings/errors

### During Testing
- [ ] Document any unexpected behavior
- [ ] Take screenshots of visual issues
- [ ] Note performance problems
- [ ] Test all control combinations

### After Testing
- [ ] Review Unity console for errors
- [ ] Check memory usage in Profiler
- [ ] Validate all test categories passed
- [ ] Document any issues found

## Success Criteria

The tutorial system passes testing if:
1. ✅ All components initialize correctly
2. ✅ Tutorial progresses through all 7 areas
3. ✅ Player and AI interactions work as designed
4. ✅ UI provides clear guidance throughout
5. ✅ Performance remains stable (60+ FPS)
6. ✅ No critical errors or crashes occur
7. ✅ Controls are responsive and intuitive
8. ✅ Visual feedback is clear and helpful

## Issue Reporting

When issues are found:
1. **Severity**: Critical, High, Medium, Low
2. **Category**: Which test category the issue falls under
3. **Steps to Reproduce**: Exact steps to recreate the issue
4. **Expected vs Actual**: What should happen vs what actually happens
5. **Screenshots**: Visual evidence when applicable
6. **Console Output**: Any relevant error messages

## Next Steps After Testing

1. **Fix Critical Issues**: Address any game-breaking problems
2. **Polish Experience**: Improve user experience based on feedback
3. **Performance Optimization**: Address any performance concerns
4. **Documentation Updates**: Update documentation based on test results
5. **Integration Testing**: Test tutorial integration with main game systems

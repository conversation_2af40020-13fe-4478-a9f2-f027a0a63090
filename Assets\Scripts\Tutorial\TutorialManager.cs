using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using AshesOfTheGrove.Player;
using AshesOfTheGrove.AI;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Main tutorial manager that coordinates the entire tutorial experience
    /// Handles progression, UI updates, and interaction between player and AI systems
    /// </summary>
    public class TutorialManager : MonoBehaviour
    {
        [Header("Tutorial Configuration")]
        [SerializeField] private bool autoStartTutorial = true;
        [SerializeField] private float instructionDisplayTime = 3f;
        [SerializeField] private float transitionDelay = 1f;
        
        [Header("Scene References")]
        [SerializeField] private TutorialSceneLayout sceneLayout;
        [SerializeField] private PlayerController player;
        [SerializeField] private Camera tutorialCamera;
        [SerializeField] private TutorialUI tutorialUI;
        
        [Header("Tutorial Objects")]
        [SerializeField] private GameObject[] enemies;
        [SerializeField] private GameObject[] objectives;
        [SerializeField] private GameObject[] hidingSpots;
        
        // Tutorial state
        private int currentAreaIndex = 0;
        private bool tutorialActive = false;
        private bool areaInProgress = false;
        private TutorialArea currentArea;
        
        // Events
        public System.Action<int> OnAreaChanged;
        public System.Action<string> OnObjectiveUpdated;
        public System.Action OnTutorialCompleted;
        
        private void Start()
        {
            InitializeTutorial();
            
            if (autoStartTutorial)
            {
                StartTutorial();
            }
        }
        
        private void InitializeTutorial()
        {
            // Find components if not assigned
            if (sceneLayout == null)
                sceneLayout = FindObjectOfType<TutorialSceneLayout>();
            
            if (player == null)
                player = FindObjectOfType<PlayerController>();
            
            if (tutorialCamera == null)
                tutorialCamera = Camera.main;
            
            if (tutorialUI == null)
                tutorialUI = FindObjectOfType<TutorialUI>();
            
            // Initialize all tutorial objects as inactive
            SetAllObjectsActive(false);
            
            Debug.Log("[TutorialManager] Tutorial initialized successfully");
        }
        
        public void StartTutorial()
        {
            if (tutorialActive) return;
            
            tutorialActive = true;
            currentAreaIndex = 0;
            
            Debug.Log("[TutorialManager] Starting tutorial...");
            
            if (tutorialUI != null)
            {
                tutorialUI.ShowWelcomeMessage();
            }
            
            StartCoroutine(BeginTutorialSequence());
        }
        
        private IEnumerator BeginTutorialSequence()
        {
            yield return new WaitForSeconds(2f);
            
            StartArea(0);
        }
        
        public void StartArea(int areaIndex)
        {
            if (areaIndex < 0 || areaIndex >= sceneLayout.GetAreaCount())
            {
                CompleteTutorial();
                return;
            }
            
            currentAreaIndex = areaIndex;
            currentArea = sceneLayout.GetArea(areaIndex);
            areaInProgress = true;
            
            Debug.Log($"[TutorialManager] Starting area: {currentArea.areaName}");
            
            // Setup area
            SetupArea(currentArea);
            
            // Update UI
            if (tutorialUI != null)
            {
                tutorialUI.UpdateArea(currentArea);
            }
            
            // Notify listeners
            OnAreaChanged?.Invoke(areaIndex);
            OnObjectiveUpdated?.Invoke(currentArea.objective);
        }
        
        private void SetupArea(TutorialArea area)
        {
            // Position player
            if (player != null)
            {
                player.transform.position = area.playerStartPosition;
                player.transform.rotation = Quaternion.identity;
            }
            
            // Position camera
            if (tutorialCamera != null)
            {
                tutorialCamera.transform.position = area.cameraPosition;
                tutorialCamera.transform.LookAt(area.playerStartPosition);
            }
            
            // Setup area-specific objects
            SetupAreaObjects(currentAreaIndex);
            
            // Start area-specific logic
            StartCoroutine(RunAreaLogic(area));
        }
        
        private void SetupAreaObjects(int areaIndex)
        {
            // Deactivate all objects first
            SetAllObjectsActive(false);
            
            switch (areaIndex)
            {
                case 0: // Basic Movement
                    SetupBasicMovementArea();
                    break;
                case 1: // Stealth Mechanics
                    SetupStealthArea();
                    break;
                case 2: // Enemy Detection
                    SetupDetectionArea();
                    break;
                case 3: // Using Cover
                    SetupCoverArea();
                    break;
                case 4: // Patrol Patterns
                    SetupPatrolArea();
                    break;
                case 5: // Alert States
                    SetupAlertArea();
                    break;
                case 6: // Final Challenge
                    SetupFinalArea();
                    break;
            }
        }
        
        private void SetupBasicMovementArea()
        {
            // Create movement target
            CreateObjectiveMarker(new Vector3(0, 0.1f, -5), "Move here to continue");
        }
        
        private void SetupStealthArea()
        {
            // Create stealth practice area
            CreateObjectiveMarker(new Vector3(5, 0.1f, -5), "Practice stealth movement");
        }
        
        private void SetupDetectionArea()
        {
            // Activate first enemy
            if (enemies.Length > 0 && enemies[0] != null)
            {
                enemies[0].SetActive(true);
                SetupEnemyPatrol(enemies[0], new Vector3[] { 
                    new Vector3(-5, 0, 0), 
                    new Vector3(-2, 0, 0) 
                });
            }
            CreateObjectiveMarker(new Vector3(-5, 0.1f, 3), "Avoid the guard");
        }
        
        private void SetupCoverArea()
        {
            // Activate enemy and hiding spots
            if (enemies.Length > 1 && enemies[1] != null)
            {
                enemies[1].SetActive(true);
                SetupEnemyPatrol(enemies[1], new Vector3[] { 
                    new Vector3(-8, 0, 2), 
                    new Vector3(-8, 0, 8) 
                });
            }
            
            if (hidingSpots.Length > 0)
            {
                hidingSpots[0].SetActive(true);
            }
            
            CreateObjectiveMarker(new Vector3(-8, 0.1f, 8), "Use cover to reach here");
        }
        
        private void SetupPatrolArea()
        {
            // Activate multiple enemies with complex patrols
            for (int i = 2; i < Mathf.Min(4, enemies.Length); i++)
            {
                if (enemies[i] != null)
                {
                    enemies[i].SetActive(true);
                }
            }
            
            CreateObjectiveMarker(new Vector3(5, 0.1f, 5), "Navigate past the guards");
        }
        
        private void SetupAlertArea()
        {
            // Setup alert demonstration
            if (enemies.Length > 4 && enemies[4] != null)
            {
                enemies[4].SetActive(true);
            }
            
            CreateObjectiveMarker(new Vector3(8, 0.1f, 3), "Observe alert states");
        }
        
        private void SetupFinalArea()
        {
            // Activate all remaining elements for final challenge
            for (int i = 0; i < enemies.Length; i++)
            {
                if (enemies[i] != null)
                {
                    enemies[i].SetActive(true);
                }
            }
            
            for (int i = 0; i < hidingSpots.Length; i++)
            {
                if (hidingSpots[i] != null)
                {
                    hidingSpots[i].SetActive(true);
                }
            }
            
            CreateObjectiveMarker(new Vector3(10, 0.1f, -5), "Final Objective");
        }
        
        private void SetupEnemyPatrol(GameObject enemy, Vector3[] patrolPoints)
        {
            EnemyAIController aiController = enemy.GetComponent<EnemyAIController>();
            if (aiController != null)
            {
                // Set patrol points (this would need to be implemented in EnemyAIController)
                Debug.Log($"[TutorialManager] Setting up patrol for {enemy.name}");
            }
        }
        
        private void CreateObjectiveMarker(Vector3 position, string description)
        {
            // Create a simple objective marker
            GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            marker.name = "ObjectiveMarker";
            marker.transform.position = position;
            marker.transform.localScale = new Vector3(1, 0.1f, 1);
            
            // Make it green and glowing
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = Color.green;
            mat.EnableKeyword("_EMISSION");
            mat.SetColor("_EmissionColor", Color.green * 0.5f);
            marker.GetComponent<Renderer>().material = mat;
            
            // Add to objectives list
            System.Array.Resize(ref objectives, objectives.Length + 1);
            objectives[objectives.Length - 1] = marker;
        }
        
        private IEnumerator RunAreaLogic(TutorialArea area)
        {
            // Display instructions
            if (tutorialUI != null)
            {
                foreach (string instruction in area.instructions)
                {
                    tutorialUI.ShowInstruction(instruction);
                    yield return new WaitForSeconds(instructionDisplayTime);
                }
            }
            
            // Wait for area completion
            yield return StartCoroutine(WaitForAreaCompletion());
            
            // Complete area
            CompleteCurrentArea();
        }
        
        private IEnumerator WaitForAreaCompletion()
        {
            // Area-specific completion logic
            switch (currentAreaIndex)
            {
                case 0: // Basic Movement
                    yield return WaitForPlayerReachPosition(new Vector3(0, 1, -5), 2f);
                    break;
                case 1: // Stealth Mechanics
                    yield return WaitForStealthPractice();
                    break;
                default:
                    yield return WaitForPlayerReachObjective();
                    break;
            }
        }
        
        private IEnumerator WaitForPlayerReachPosition(Vector3 targetPosition, float threshold)
        {
            while (Vector3.Distance(player.transform.position, targetPosition) > threshold)
            {
                yield return null;
            }
        }
        
        private IEnumerator WaitForStealthPractice()
        {
            bool crouchTested = false;
            bool runTested = false;
            float startTime = Time.time;

            while ((!crouchTested || !runTested) && Time.time - startTime < 30f)
            {
                if (player.IsCrouching())
                    crouchTested = true;

                // Check if player is running by monitoring input
                if (Input.GetKey(KeyCode.LeftShift))
                    runTested = true;

                yield return null;
            }
        }
        
        private IEnumerator WaitForPlayerReachObjective()
        {
            // Wait for player to reach the current objective marker
            if (objectives.Length > 0)
            {
                GameObject currentObjective = objectives[objectives.Length - 1];
                if (currentObjective != null)
                {
                    while (Vector3.Distance(player.transform.position, currentObjective.transform.position) > 2f)
                    {
                        yield return null;
                    }
                }
            }
            else
            {
                yield return new WaitForSeconds(5f); // Default wait time
            }
        }
        
        private void CompleteCurrentArea()
        {
            if (currentArea != null)
            {
                sceneLayout.CompleteArea(currentAreaIndex);
                Debug.Log($"[TutorialManager] Completed area: {currentArea.areaName}");
                
                if (tutorialUI != null)
                {
                    tutorialUI.ShowAreaComplete(currentArea.areaName);
                }
            }
            
            areaInProgress = false;
            
            // Move to next area after delay
            StartCoroutine(TransitionToNextArea());
        }
        
        private IEnumerator TransitionToNextArea()
        {
            yield return new WaitForSeconds(transitionDelay);
            
            int nextAreaIndex = currentAreaIndex + 1;
            if (nextAreaIndex < sceneLayout.GetAreaCount())
            {
                StartArea(nextAreaIndex);
            }
            else
            {
                CompleteTutorial();
            }
        }
        
        private void CompleteTutorial()
        {
            tutorialActive = false;
            Debug.Log("[TutorialManager] Tutorial completed!");
            
            if (tutorialUI != null)
            {
                tutorialUI.ShowTutorialComplete();
            }
            
            OnTutorialCompleted?.Invoke();
        }
        
        private void SetAllObjectsActive(bool active)
        {
            foreach (var enemy in enemies)
            {
                if (enemy != null)
                    enemy.SetActive(active);
            }
            
            foreach (var objective in objectives)
            {
                if (objective != null)
                    objective.SetActive(active);
            }
            
            foreach (var spot in hidingSpots)
            {
                if (spot != null)
                    spot.SetActive(active);
            }
        }
        
        // Public methods for external control
        public void SkipToArea(int areaIndex)
        {
            if (tutorialActive)
            {
                StartArea(areaIndex);
            }
        }
        
        public void RestartTutorial()
        {
            tutorialActive = false;
            StartTutorial();
        }
        
        public bool IsTutorialActive()
        {
            return tutorialActive;
        }
        
        public int GetCurrentAreaIndex()
        {
            return currentAreaIndex;
        }
    }
}

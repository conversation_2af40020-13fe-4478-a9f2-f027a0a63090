using UnityEngine;
using UnityEngine.SceneManagement;

namespace AshesOfTheGrove.Tutorial
{
    /// <summary>
    /// Complete tutorial scene setup that integrates all tutorial systems
    /// Creates a fully functional tutorial environment with all components
    /// </summary>
    public class TutorialSceneSetup : MonoBehaviour
    {
        [Header("Scene Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool useSceneBuilder = true;
        
        [Header("Tutorial Components")]
        [SerializeField] private TutorialManager tutorialManager;
        [SerializeField] private TutorialUI tutorialUI;
        [SerializeField] private TutorialSceneBuilder sceneBuilder;
        [SerializeField] private TutorialSceneLayout sceneLayout;
        
        [Header("Integration Settings")]
        [SerializeField] private bool connectObjectives = true;
        [SerializeField] private bool setupEventHandlers = true;
        [SerializeField] private bool enableDebugMode = true;
        
        private ObjectiveMarker[] objectiveMarkers;
        private HidingSpot[] hidingSpots;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupTutorialScene();
            }
        }
        
        [ContextMenu("Setup Complete Tutorial Scene")]
        public void SetupTutorialScene()
        {
            Debug.Log("[TutorialSceneSetup] Setting up complete tutorial scene...");
            
            // Step 1: Build the scene if needed
            if (useSceneBuilder && sceneBuilder != null)
            {
                sceneBuilder.BuildTutorialScene();
            }
            
            // Step 2: Setup tutorial components
            SetupTutorialComponents();
            
            // Step 3: Connect interactive elements
            ConnectInteractiveElements();
            
            // Step 4: Setup event handlers
            if (setupEventHandlers)
            {
                SetupEventHandlers();
            }
            
            // Step 5: Initialize tutorial system
            InitializeTutorialSystem();
            
            Debug.Log("[TutorialSceneSetup] Tutorial scene setup complete!");
        }
        
        private void SetupTutorialComponents()
        {
            // Create tutorial manager if not assigned
            if (tutorialManager == null)
            {
                GameObject managerObj = new GameObject("TutorialManager");
                tutorialManager = managerObj.AddComponent<TutorialManager>();
            }
            
            // Create tutorial UI if not assigned
            if (tutorialUI == null)
            {
                GameObject uiObj = new GameObject("TutorialUI");
                tutorialUI = uiObj.AddComponent<TutorialUI>();
            }
            
            // Create scene layout if not assigned
            if (sceneLayout == null)
            {
                GameObject layoutObj = new GameObject("TutorialSceneLayout");
                sceneLayout = layoutObj.AddComponent<TutorialSceneLayout>();
            }
            
            // Create scene builder if not assigned and needed
            if (useSceneBuilder && sceneBuilder == null)
            {
                GameObject builderObj = new GameObject("TutorialSceneBuilder");
                sceneBuilder = builderObj.AddComponent<TutorialSceneBuilder>();
            }
            
            Debug.Log("[TutorialSceneSetup] Tutorial components setup complete");
        }
        
        private void ConnectInteractiveElements()
        {
            // Find all interactive elements in the scene
            objectiveMarkers = FindObjectsOfType<ObjectiveMarker>();
            hidingSpots = FindObjectsOfType<HidingSpot>();
            
            Debug.Log($"[TutorialSceneSetup] Found {objectiveMarkers.Length} objective markers and {hidingSpots.Length} hiding spots");
            
            if (connectObjectives)
            {
                ConnectObjectiveMarkers();
            }
            
            SetupHidingSpots();
        }
        
        private void ConnectObjectiveMarkers()
        {
            for (int i = 0; i < objectiveMarkers.Length; i++)
            {
                ObjectiveMarker marker = objectiveMarkers[i];
                
                // Connect to tutorial manager
                marker.OnObjectiveCompleted += OnObjectiveCompleted;
                marker.OnPlayerEntered += OnPlayerEnteredObjective;
                marker.OnPlayerExited += OnPlayerExitedObjective;
                
                Debug.Log($"[TutorialSceneSetup] Connected objective marker: {marker.GetObjectiveName()}");
            }
        }
        
        private void SetupHidingSpots()
        {
            foreach (HidingSpot spot in hidingSpots)
            {
                spot.SetTutorialMode(enableDebugMode);
                Debug.Log($"[TutorialSceneSetup] Setup hiding spot: {spot.name}");
            }
        }
        
        private void SetupEventHandlers()
        {
            // Connect tutorial manager events
            if (tutorialManager != null)
            {
                // These would need to be implemented in TutorialManager
                // tutorialManager.OnAreaStarted += OnTutorialAreaStarted;
                // tutorialManager.OnAreaCompleted += OnTutorialAreaCompleted;
                // tutorialManager.OnTutorialCompleted += OnTutorialCompleted;
            }
            
            Debug.Log("[TutorialSceneSetup] Event handlers setup complete");
        }
        
        private void InitializeTutorialSystem()
        {
            // Start the tutorial
            if (tutorialManager != null)
            {
                tutorialManager.StartTutorial();
            }
            
            // Show welcome message
            if (tutorialUI != null)
            {
                tutorialUI.ShowWelcomeMessage("Welcome to Ashes of the Grove Tutorial");
            }
            
            Debug.Log("[TutorialSceneSetup] Tutorial system initialized");
        }
        
        // Event handlers for objective markers
        private void OnObjectiveCompleted(ObjectiveMarker marker)
        {
            Debug.Log($"[TutorialSceneSetup] Objective completed: {marker.GetObjectiveName()}");
            
            if (tutorialUI != null)
            {
                tutorialUI.ShowInstruction($"Objective completed: {marker.GetObjectiveName()}");
            }
            
            // Notify tutorial manager
            if (tutorialManager != null)
            {
                // This would need to be implemented in TutorialManager
                // tutorialManager.OnObjectiveCompleted(marker.GetObjectiveName());
            }
        }
        
        private void OnPlayerEnteredObjective(ObjectiveMarker marker)
        {
            Debug.Log($"[TutorialSceneSetup] Player entered objective: {marker.GetObjectiveName()}");
            
            if (tutorialUI != null)
            {
                tutorialUI.SetObjective(marker.GetObjectiveDescription());
            }
        }
        
        private void OnPlayerExitedObjective(ObjectiveMarker marker)
        {
            Debug.Log($"[TutorialSceneSetup] Player exited objective: {marker.GetObjectiveName()}");
        }
        
        // Utility methods
        public void RestartTutorial()
        {
            Debug.Log("[TutorialSceneSetup] Restarting tutorial...");
            
            // Reset all objective markers
            foreach (ObjectiveMarker marker in objectiveMarkers)
            {
                marker.ResetObjective();
            }
            
            // Reset tutorial manager
            if (tutorialManager != null)
            {
                tutorialManager.RestartTutorial();
            }
            
            // Clear UI
            if (tutorialUI != null)
            {
                tutorialUI.ClearInstruction();
                tutorialUI.ClearObjective();
            }
        }
        
        public void SkipToArea(int areaIndex)
        {
            if (tutorialManager != null)
            {
                // This would need to be implemented in TutorialManager
                // tutorialManager.SkipToArea(areaIndex);
            }
        }
        
        public void EnableDebugMode(bool enabled)
        {
            enableDebugMode = enabled;
            
            if (tutorialUI != null)
            {
                tutorialUI.SetShowDebugInfo(enabled);
            }
            
            // Update all hiding spots
            foreach (HidingSpot spot in hidingSpots)
            {
                spot.SetTutorialMode(enabled);
            }
        }
        
        public void ReloadScene()
        {
            SceneManager.LoadScene(SceneManager.GetActiveScene().name);
        }
        
        // Debug methods
        [ContextMenu("Test Objective Completion")]
        public void TestObjectiveCompletion()
        {
            if (objectiveMarkers.Length > 0)
            {
                objectiveMarkers[0].CompleteObjective();
            }
        }
        
        [ContextMenu("Show Debug Info")]
        public void ShowDebugInfo()
        {
            Debug.Log($"[TutorialSceneSetup] Debug Info:");
            Debug.Log($"- Tutorial Manager: {(tutorialManager != null ? "Found" : "Missing")}");
            Debug.Log($"- Tutorial UI: {(tutorialUI != null ? "Found" : "Missing")}");
            Debug.Log($"- Scene Layout: {(sceneLayout != null ? "Found" : "Missing")}");
            Debug.Log($"- Scene Builder: {(sceneBuilder != null ? "Found" : "Missing")}");
            Debug.Log($"- Objective Markers: {objectiveMarkers.Length}");
            Debug.Log($"- Hiding Spots: {hidingSpots.Length}");
        }
        
        private void OnValidate()
        {
            // Auto-find components if not assigned
            if (tutorialManager == null)
                tutorialManager = FindObjectOfType<TutorialManager>();
            
            if (tutorialUI == null)
                tutorialUI = FindObjectOfType<TutorialUI>();
            
            if (sceneLayout == null)
                sceneLayout = FindObjectOfType<TutorialSceneLayout>();
            
            if (sceneBuilder == null)
                sceneBuilder = FindObjectOfType<TutorialSceneBuilder>();
        }
    }
}

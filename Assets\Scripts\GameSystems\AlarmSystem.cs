using UnityEngine;
using System.Collections;

namespace AshesOfTheGrove.GameSystems
{
    /// <summary>
    /// Manages the alarm system for the stealth game
    /// Handles alert levels, reinforcements, and game state changes
    /// </summary>
    public class AlarmSystem : MonoBehaviour
    {
        [Header("Alarm Settings")]
        [SerializeField] private float alertCooldownTime = 30f;
        [SerializeField] private float reinforcementDelay = 5f;
        
        [Header("Alert Levels")]
        [SerializeField] private AlertLevel currentAlertLevel = AlertLevel.Clear;
        
        [Header("Audio")]
        [SerializeField] private AudioSource alarmAudioSource;
        [SerializeField] private AudioClip alarmSound;
        
        [Header("Reinforcements")]
        [SerializeField] private GameObject[] reinforcementPrefabs;
        [SerializeField] private Transform[] reinforcementSpawnPoints;
        
        public static AlarmSystem Instance { get; private set; }
        
        public enum AlertLevel
        {
            Clear,      // No alert
            Caution,    // Suspicious activity detected
            Alert,      // Player spotted
            Evasion     // Player lost but still searching
        }
        
        // Events
        public System.Action<AlertLevel> OnAlertLevelChanged;
        
        private Coroutine alertCooldownCoroutine;
        private Coroutine reinforcementCoroutine;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (alarmAudioSource == null)
            {
                alarmAudioSource = GetComponent<AudioSource>();
            }
        }
        
        public void TriggerAlert(AlertLevel level)
        {
            if (level > currentAlertLevel)
            {
                SetAlertLevel(level);
            }
        }
        
        public void SetAlertLevel(AlertLevel newLevel)
        {
            if (currentAlertLevel == newLevel) return;
            
            AlertLevel previousLevel = currentAlertLevel;
            currentAlertLevel = newLevel;
            
            HandleAlertLevelChange(previousLevel, newLevel);
            OnAlertLevelChanged?.Invoke(newLevel);
            
            // Start cooldown for automatic alert reduction
            if (alertCooldownCoroutine != null)
            {
                StopCoroutine(alertCooldownCoroutine);
            }
            
            if (newLevel != AlertLevel.Clear)
            {
                alertCooldownCoroutine = StartCoroutine(AlertCooldown());
            }
        }
        
        private void HandleAlertLevelChange(AlertLevel from, AlertLevel to)
        {
            switch (to)
            {
                case AlertLevel.Clear:
                    HandleClearAlert();
                    break;
                    
                case AlertLevel.Caution:
                    HandleCautionAlert();
                    break;
                    
                case AlertLevel.Alert:
                    HandleFullAlert();
                    break;
                    
                case AlertLevel.Evasion:
                    HandleEvasionAlert();
                    break;
            }
        }
        
        private void HandleClearAlert()
        {
            StopAlarmSound();
            CancelReinforcements();
            
            // Notify all enemies to return to patrol
            var enemies = FindObjectsOfType<AI.EnemyAIController>();
            foreach (var enemy in enemies)
            {
                // Reset enemy states if needed
            }
        }
        
        private void HandleCautionAlert()
        {
            // Increase enemy awareness but don't trigger full alarm
        }
        
        private void HandleFullAlert()
        {
            PlayAlarmSound();
            CallReinforcements();
            
            // Set all enemies to alert state
            var enemies = FindObjectsOfType<AI.EnemyAIController>();
            foreach (var enemy in enemies)
            {
                // Force enemies into alert/search mode
            }
        }
        
        private void HandleEvasionAlert()
        {
            StopAlarmSound();
            // Keep enemies in search mode but stop reinforcements
        }
        
        private void PlayAlarmSound()
        {
            if (alarmAudioSource != null && alarmSound != null)
            {
                alarmAudioSource.clip = alarmSound;
                alarmAudioSource.loop = true;
                alarmAudioSource.Play();
            }
        }
        
        private void StopAlarmSound()
        {
            if (alarmAudioSource != null)
            {
                alarmAudioSource.Stop();
            }
        }
        
        private void CallReinforcements()
        {
            if (reinforcementCoroutine != null)
            {
                StopCoroutine(reinforcementCoroutine);
            }
            
            reinforcementCoroutine = StartCoroutine(SpawnReinforcements());
        }
        
        private void CancelReinforcements()
        {
            if (reinforcementCoroutine != null)
            {
                StopCoroutine(reinforcementCoroutine);
                reinforcementCoroutine = null;
            }
        }
        
        private IEnumerator SpawnReinforcements()
        {
            yield return new WaitForSeconds(reinforcementDelay);
            
            if (currentAlertLevel == AlertLevel.Alert)
            {
                foreach (var spawnPoint in reinforcementSpawnPoints)
                {
                    if (reinforcementPrefabs.Length > 0)
                    {
                        int randomIndex = Random.Range(0, reinforcementPrefabs.Length);
                        Instantiate(reinforcementPrefabs[randomIndex], spawnPoint.position, spawnPoint.rotation);
                    }
                }
            }
        }
        
        private IEnumerator AlertCooldown()
        {
            yield return new WaitForSeconds(alertCooldownTime);
            
            // Gradually reduce alert level
            switch (currentAlertLevel)
            {
                case AlertLevel.Alert:
                    SetAlertLevel(AlertLevel.Evasion);
                    break;
                case AlertLevel.Evasion:
                    SetAlertLevel(AlertLevel.Caution);
                    break;
                case AlertLevel.Caution:
                    SetAlertLevel(AlertLevel.Clear);
                    break;
            }
        }
        
        public AlertLevel GetCurrentAlertLevel()
        {
            return currentAlertLevel;
        }
        
        public bool IsAlarmActive()
        {
            return currentAlertLevel == AlertLevel.Alert;
        }
        
        public string GetAlertLevelString()
        {
            switch (currentAlertLevel)
            {
                case AlertLevel.Clear: return "CLEAR";
                case AlertLevel.Caution: return "CAUTION";
                case AlertLevel.Alert: return "ALERT";
                case AlertLevel.Evasion: return "EVASION";
                default: return "UNKNOWN";
            }
        }
    }
}

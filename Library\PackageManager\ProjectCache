m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638864793956948109
    m_Hash: 2171451764
  m_LockFileStatus:
    m_FilePath: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638864803503819901
    m_Hash: 766559675
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: C:/Users/<USER>/Documents/augment-projects/AshesOfTheGrove/Packages
m_EditorVersion: 2022.3.17f1 (4fc78088f837)
m_ResolvedPackages:
- packageId: com.unity.ai.navigation@1.1.5
  testable: 0
  isDirectDependency: 1
  version: 1.1.5
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ai.navigation@1.1.5
  assetPath: Packages/com.unity.ai.navigation
  name: com.unity.ai.navigation
  displayName: AI Navigation
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: High-level NavMesh components for building and using NavMeshes at
    runtime and at edit time.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0-exp.2
    - 1.0.0-exp.3
    - 1.0.0-exp.4
    - 1.1.0-pre.1
    - 1.1.0-pre.2
    - 1.1.1
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 2.0.0-pre.3
    - 2.0.0-pre.4
    - 2.0.0
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.7
    - 2.0.8
    compatible:
    - 1.1.5
    - 1.1.6
    recommended: 1.1.6
    deprecated: []
  dependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638319373850000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.navigation@1.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ai.navigation.git
    revision: a905e1ab41c8afae1a75688ceb3b4dfa1686ea75
    path: 
  unityLifecycle:
    version: 1.1.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.cinemachine@2.9.7
  testable: 0
  isDirectDependency: 1
  version: 2.9.7
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.cinemachine@2.9.7
  assetPath: Packages/com.unity.cinemachine
  name: com.unity.cinemachine
  displayName: Cinemachine
  author:
    name: 
    email: 
    url: 
  category: cinematography
  type: asset
  description: "Smart camera tools for passionate creators. \n\nNew starting from
    2.7.1: Are you looking for the Cinemachine menu? It has moved to the GameObject
    menu.\n\nIMPORTANT NOTE: If you are upgrading from the legacy Asset Store version
    of Cinemachine, delete the Cinemachine asset from your project BEFORE installing
    this version from the Package Manager."
  status: 0
  errors: []
  versions:
    all:
    - 2.1.11-beta.1
    - 2.1.12
    - 2.1.13
    - 2.2.0
    - 2.2.7
    - 2.2.8
    - 2.2.9
    - 2.2.10-preview.3
    - 2.2.10-preview.4
    - 2.3.1
    - 2.3.3
    - 2.3.4
    - 2.3.5-preview.3
    - 2.4.0-preview.3
    - 2.4.0-preview.4
    - 2.4.0-preview.6
    - 2.4.0-preview.7
    - 2.4.0-preview.8
    - 2.4.0-preview.9
    - 2.4.0-preview.10
    - 2.4.0
    - 2.5.0
    - 2.6.0-preview.2
    - 2.6.0-preview.3
    - 2.6.0-preview.5
    - 2.6.0-preview.8
    - 2.6.0
    - 2.6.1-preview.6
    - 2.6.1
    - 2.6.2-preview.1
    - 2.6.2
    - 2.6.3-preview.2
    - 2.6.3
    - 2.6.4
    - 2.6.5
    - 2.6.9
    - 2.6.10
    - 2.6.11
    - 2.6.14
    - 2.6.15
    - 2.6.17
    - 2.7.1
    - 2.7.2
    - 2.7.3
    - 2.7.4
    - 2.7.5
    - 2.7.8
    - 2.7.9
    - 2.8.0-exp.1
    - 2.8.0-exp.2
    - 2.8.0-pre.1
    - 2.8.0
    - 2.8.1
    - 2.8.2
    - 2.8.3
    - 2.8.4
    - 2.8.6
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    compatible:
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    recommended: 2.9.7
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - camera
  - follow
  - rig
  - fps
  - cinematography
  - aim
  - orbit
  - cutscene
  - cinematic
  - collision
  - freelook
  - cinemachine
  - compose
  - composition
  - dolly
  - track
  - clearshot
  - noise
  - framing
  - handheld
  - lens
  - impulse
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638191355130000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.cinemachine@2.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
    revision: 46485ca80113434766f2b256525bb98def548fb9
    path: 
  unityLifecycle:
    version: 2.9.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.collab-proxy@2.2.0
  testable: 0
  isDirectDependency: 1
  version: 2.2.0
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.collab-proxy@2.2.0
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: assets
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  status: 0
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    compatible:
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    recommended: 2.2.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638326189440000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: 33c8fba1793946be3c73f738edf70db0b5546afd
    path: 
  unityLifecycle:
    version: 2.2.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.feature.development@1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.feature.development@1.0.1
  assetPath: Packages/com.unity.feature.development
  name: com.unity.feature.development
  displayName: Engineering
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: "Optimize your development experience in Unity with the Dev Tools
    feature set. Enable support for multiple integrated development environments
    (IDE) for editing your Unity code. Get access to development tools to help you
    test and analyze your project\u2019s performance."
  status: 0
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.ide.visualstudio
    version: default
  - name: com.unity.ide.rider
    version: default
  - name: com.unity.ide.vscode
    version: default
  - name: com.unity.editorcoroutines
    version: default
  - name: com.unity.performance.profile-analyzer
    version: default
  - name: com.unity.test-framework
    version: default
  - name: com.unity.testtools.codecoverage
    version: default
  resolvedDependencies:
  - name: com.unity.ide.visualstudio
    version: 2.0.22
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ide.rider
    version: 3.0.27
  - name: com.unity.ide.vscode
    version: 1.2.5
  - name: com.unity.editorcoroutines
    version: 1.0.0
  - name: com.unity.performance.profile-analyzer
    version: 1.2.2
  - name: com.unity.testtools.codecoverage
    version: 1.2.4
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.inputsystem@1.7.0
  testable: 0
  isDirectDependency: 1
  version: 1.7.0
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.inputsystem@1.7.0
  assetPath: Packages/com.unity.inputsystem
  name: com.unity.inputsystem
  displayName: Input System
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: A new input system which can be used as a more extensible and customizable
    alternative to Unity's classic input system in UnityEngine.Input.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.6-preview
    - 0.2.8-preview
    - 0.2.10-preview
    - 0.9.0-preview
    - 0.9.1-preview
    - 0.9.2-preview
    - 0.9.3-preview
    - 0.9.4-preview
    - 0.9.5-preview
    - 0.9.6-preview
    - 1.0.0-preview
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.6
    - 1.0.0-preview.7
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.1.0-pre.5
    - 1.1.0-pre.6
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.1
    - 1.2.0
    - 1.3.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.5.0
    - 1.5.1
    - 1.6.1
    - 1.6.3
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    compatible:
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    recommended: 1.7.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - input
  - events
  - keyboard
  - mouse
  - gamepad
  - touch
  - vr
  - xr
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638277060810000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.7/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/InputSystem.git
    revision: e29989176f3a8473ba30854e4bfae953083eba28
    path: 
  unityLifecycle:
    version: 1.7.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.render-pipelines.universal@14.0.9
  testable: 0
  isDirectDependency: 1
  version: 14.0.9
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.render-pipelines.universal@14.0.9
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal RP
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  status: 0
  errors: []
  versions:
    all:
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.35
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.26
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 14.0.9
    compatible:
    - 14.0.9
    recommended: 14.0.9
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.burst
    version: 1.8.9
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  - name: com.unity.shadergraph
    version: 14.0.9
  - name: com.unity.render-pipelines.universal-config
    version: 14.0.9
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.burst
    version: 1.8.11
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.shadergraph
    version: 14.0.9
  - name: com.unity.searcher
    version: 4.9.2
  - name: com.unity.render-pipelines.universal-config
    version: 14.0.9
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 14.0.9
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.timeline@1.7.6
  testable: 0
  isDirectDependency: 1
  version: 1.7.6
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.timeline@1.7.6
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  status: 0
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    compatible:
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    recommended: 1.7.7
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638326112840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.7/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: 02f49ada64a9f88044207cf42ba407dc634ef8e6
    path: 
  unityLifecycle:
    version: 1.7.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.ugui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ugui@1.0.0
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.visualscripting@1.9.1
  testable: 0
  isDirectDependency: 1
  version: 1.9.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.visualscripting@1.9.1
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  status: 0
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638284821510000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 5e78e9c354ed1567dbcea08e2bd9992e4a473cfb
    path: 
  unityLifecycle:
    version: 1.9.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.ai@1.0.0
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.androidjni@1.0.0
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.animation@1.0.0
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.assetbundle@1.0.0
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.audio@1.0.0
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.cloth@1.0.0
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.director@1.0.0
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.imageconversion@1.0.0
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods to convert images from and to PNG, JPEG or EXR formats.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.imgui@1.0.0
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.jsonserialize@1.0.0
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.particlesystem@1.0.0
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.physics@1.0.0
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.physics2d@1.0.0
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.screencapture@1.0.0
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.terrain@1.0.0
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.terrainphysics@1.0.0
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.tilemap@1.0.0
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.ui@1.0.0
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.uielements@1.0.0
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.umbra@1.0.0
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unityanalytics@1.0.0
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityAnalytics module implements APIs required to use Unity Analytics.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityAnalyticsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.vehicles@1.0.0
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.video@1.0.0
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.vr@1.0.0
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.wind@1.0.0
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.xr@1.0.0
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.modules.subsystems@1.0.0
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.mathematics@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.mathematics@1.2.6
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  status: 0
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.2.6
    - 1.3.1
    - 1.3.2
    recommended: 1.2.6
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637841876140000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: f110c8c230d253654afed153569030a587cc7557
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.burst@1.8.11
  testable: 0
  isDirectDependency: 0
  version: 1.8.11
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.burst@1.8.11
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  status: 0
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    compatible:
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    recommended: 1.8.23
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.2.6
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638360965820000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: ***********************************:unity/burst
    revision: 1d80ef69a5caadb47bb90e69e6fc822e3a334bd3
    path: 
  unityLifecycle:
    version: 1.8.11
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.render-pipelines.core@14.0.9
  testable: 0
  isDirectDependency: 0
  version: 14.0.9
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.render-pipelines.core@14.0.9
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Core RP Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.21
    - 0.1.27
    - 0.1.28
    - 1.0.0-beta
    - 1.0.1-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.8-preview
    - 1.1.10-preview
    - 1.1.11-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.35
    - 9.0.0-preview.38
    - 9.0.0-preview.60
    - 9.0.0-preview.77
    - 10.0.0-preview.30
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 14.0.9
    compatible:
    - 14.0.9
    recommended: 14.0.9
    deprecated: []
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 14.0.9
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.shadergraph@14.0.9
  testable: 0
  isDirectDependency: 0
  version: 14.0.9
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.shadergraph@14.0.9
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.8
    - 0.1.9
    - 0.1.17
    - 1.0.0-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.6-preview
    - 1.1.8-preview
    - 1.1.9-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.34
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 14.0.9
    compatible:
    - 14.0.9
    recommended: 14.0.9
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  - name: com.unity.searcher
    version: 4.9.2
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.searcher
    version: 4.9.2
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 14.0.9
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.render-pipelines.universal-config@14.0.9
  testable: 0
  isDirectDependency: 0
  version: 14.0.9
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.render-pipelines.universal-config@14.0.9
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal RP Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  status: 0
  errors: []
  versions:
    all:
    - 14.0.9
    compatible:
    - 14.0.9
    recommended: 14.0.9
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 14.0.9
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.ide.visualstudio@2.0.22
  testable: 0
  isDirectDependency: 0
  version: 2.0.22
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ide.visualstudio@2.0.22
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.22
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638326061910000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 700b44077345e97d37d464ff25507638983aed64
    path: 
  unityLifecycle:
    version: 2.0.22
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.ide.rider@3.0.27
  testable: 0
  isDirectDependency: 0
  version: 3.0.27
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ide.rider@3.0.27
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638373878680000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: 1d471c19a7f21834ae5b8d0358fd6f9b242c1ee8
    path: 
  unityLifecycle:
    version: 3.0.27
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.ide.vscode@1.2.5
  testable: 0
  isDirectDependency: 0
  version: 1.2.5
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ide.vscode@1.2.5
  assetPath: Packages/com.unity.ide.vscode
  name: com.unity.ide.vscode
  displayName: Visual Studio Code Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: Code editor integration for supporting Visual Studio Code as code
    editor for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.7
    - 1.1.0
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    compatible:
    - 1.2.5
    recommended: 1.2.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637800255360000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.ide.vscode.git
    revision: b0740c80bfc2440527c317109f7c3d9100132722
    path: 
  unityLifecycle:
    version: 1.2.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.editorcoroutines@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.editorcoroutines@1.0.0
  assetPath: Packages/com.unity.editorcoroutines
  name: com.unity.editorcoroutines
  displayName: Editor Coroutines
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'The editor coroutines package allows developers to start constructs
    similar to Unity''s monobehaviour based coroutines within the editor using abitrary
    objects. '
  status: 0
  errors: []
  versions:
    all:
    - 0.0.1-preview.3
    - 0.0.1-preview.4
    - 0.0.1-preview.5
    - 0.0.2-preview.1
    - 0.1.0-preview.1
    - 0.1.0-preview.2
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - coroutine
  - coroutines
  - editor
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637232611380000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.editorcoroutines.git
    revision: f67fc9992bbc7a553b17375de53a8b2db136528e
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.performance.profile-analyzer@1.2.2
  testable: 0
  isDirectDependency: 0
  version: 1.2.2
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.performance.profile-analyzer@1.2.2
  assetPath: Packages/com.unity.performance.profile-analyzer
  name: com.unity.performance.profile-analyzer
  displayName: Profile Analyzer
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: "The Profile Analyzer tool supports the standard Unity Profiler. You
    can use it to analyze multiple frames and multiple data sets of the CPU data
    in the Profiler.\n\nMain features: \n\u25AA Multi-frame analysis of a single
    set of Profiler CPU data \n\u25AA Comparison of two multi-frame profile scans
    \n\n"
  status: 0
  errors: []
  versions:
    all:
    - 0.4.0-preview.3
    - 0.4.0-preview.5
    - 0.4.0-preview.6
    - 0.5.0-preview.1
    - 0.6.0-preview.1
    - 0.7.0-preview.4
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.1.0-pre.2
    - 1.1.0
    - 1.1.1
    - 1.2.2
    - 1.2.3
    compatible:
    - 1.2.2
    - 1.2.3
    recommended: 1.2.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638108624620000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git
    revision: 2c63f931588caa60a1fef33b1b250b20d952dc58
    path: 
  unityLifecycle:
    version: 1.2.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.test-framework@1.1.33
  testable: 0
  isDirectDependency: 0
  version: 1.1.33
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.test-framework@1.1.33
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: asset
  description: Test framework for running Edit mode and Play mode tests in Unity.
  status: 0
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.1.33
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637938212700000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.test-framework.git
    revision: 34a4d423d64926635eb36d3bb86276179cc186e1
    path: 
  unityLifecycle:
    version: 1.1.33
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.testtools.codecoverage@1.2.4
  testable: 0
  isDirectDependency: 0
  version: 1.2.4
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.testtools.codecoverage@1.2.4
  assetPath: Packages/com.unity.testtools.codecoverage
  name: com.unity.testtools.codecoverage
  displayName: Code Coverage
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: Use this package to export code coverage data and reports from your
    automated tests. Additionally, the Code Coverage package offers a Coverage Recording
    feature which allows capturing coverage data on demand, for manual testing or
    when there are no automated tests in the project.
  status: 0
  errors: []
  versions:
    all:
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.4.2-preview
    - 0.4.3-preview
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.2.0-exp.1
    - 1.2.0-exp.2
    - 1.2.0-exp.3
    - 1.2.0-exp.4
    - 1.2.0-exp.5
    - 1.2.0-exp.6
    - 1.2.0-exp.7
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    compatible:
    - 1.2.4
    - 1.2.5
    - 1.2.6
    recommended: 1.2.6
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.0.16
  - name: com.unity.settings-manager
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords:
  - test
  - coverage
  - testing
  - opencover
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638212235100000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.testtools.codecoverage.git
    revision: a7dd0751db55cd55e3b336723fb51433c1de7244
    path: 
  unityLifecycle:
    version: 1.2.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.searcher@4.9.2
  testable: 0
  isDirectDependency: 0
  version: 4.9.2
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.searcher@4.9.2
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  status: 0
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    compatible:
    - 4.9.2
    recommended: 4.9.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637831252240000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 7f9f75b04e97cf09c8ddc3968d059e74d6a2460a
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.settings-manager@2.0.1
  testable: 0
  isDirectDependency: 0
  version: 2.0.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.settings-manager@2.0.1
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.0.1
    - 2.1.0
    recommended: 2.0.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637744099390000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 9b7c12d04ef880168151c127fd6b37b5931197dc
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
- packageId: com.unity.ext.nunit@1.0.6
  testable: 0
  isDirectDependency: 0
  version: 1.0.6
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\augment-projects\AshesOfTheGrove\Library\PackageCache\com.unity.ext.nunit@1.0.6
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: asset
  description: Custom version of the nunit package build to work with Unity. Used
    by the Unity Test Framework.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    recommended: 1.0.6
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637429759280000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
    revision: 29ea4d6504a5f58fb3a6934db839aa80ae6d9d88
    path: 
  unityLifecycle:
    version: 1.0.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 

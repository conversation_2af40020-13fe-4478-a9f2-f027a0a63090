using UnityEngine;
using AshesOfTheGrove.Tutorial;
using AshesOfTheGrove.Player;

namespace AshesOfTheGrove.Testing.Tutorial
{
    /// <summary>
    /// Quick test script to set up and test the tutorial system
    /// Add this to an empty GameObject in a scene to quickly test tutorial functionality
    /// </summary>
    public class TutorialQuickTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool autoRunDiagnostics = true;
        [SerializeField] private bool autoStartTutorial = false;
        
        [Header("Manual Controls")]
        [SerializeField] private KeyCode setupKey = KeyCode.F8;
        [SerializeField] private KeyCode diagnosticsKey = KeyCode.F9;
        [SerializeField] private KeyCode startTutorialKey = KeyCode.F10;
        [SerializeField] private KeyCode resetKey = KeyCode.F7;
        
        private ModularTutorialSceneSetup sceneSetup;
        private TutorialDiagnostics diagnostics;
        private TutorialZoneManager zoneManager;
        
        private void Start()
        {
            Debug.Log("[TutorialQuickTest] Tutorial Quick Test initialized");
            Debug.Log("[TutorialQuickTest] Controls: F8=Setup, F9=Diagnostics, F10=Start Tutorial, F7=Reset");
            
            if (autoSetupOnStart)
            {
                Invoke(nameof(SetupTutorialSystem), 0.1f);
            }
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(setupKey))
            {
                SetupTutorialSystem();
            }
            
            if (Input.GetKeyDown(diagnosticsKey))
            {
                RunDiagnostics();
            }
            
            if (Input.GetKeyDown(startTutorialKey))
            {
                StartTutorial();
            }
            
            if (Input.GetKeyDown(resetKey))
            {
                ResetTutorial();
            }
        }
        
        [ContextMenu("Setup Tutorial System")]
        public void SetupTutorialSystem()
        {
            Debug.Log("[TutorialQuickTest] === SETTING UP TUTORIAL SYSTEM ===");
            
            // Find or create scene setup
            sceneSetup = FindObjectOfType<ModularTutorialSceneSetup>();
            if (sceneSetup == null)
            {
                GameObject setupObj = new GameObject("ModularTutorialSceneSetup");
                sceneSetup = setupObj.AddComponent<ModularTutorialSceneSetup>();
                Debug.Log("[TutorialQuickTest] Created ModularTutorialSceneSetup");
            }
            
            // Setup the tutorial scene
            sceneSetup.SetupTutorialScene();
            
            // Find zone manager
            zoneManager = FindObjectOfType<TutorialZoneManager>();
            
            Debug.Log("[TutorialQuickTest] Tutorial system setup complete!");
            
            if (autoRunDiagnostics)
            {
                Invoke(nameof(RunDiagnostics), 1f);
            }
            
            if (autoStartTutorial)
            {
                Invoke(nameof(StartTutorial), 2f);
            }
        }
        
        [ContextMenu("Run Diagnostics")]
        public void RunDiagnostics()
        {
            Debug.Log("[TutorialQuickTest] === RUNNING DIAGNOSTICS ===");
            
            // Find or create diagnostics
            diagnostics = FindObjectOfType<TutorialDiagnostics>();
            if (diagnostics == null)
            {
                GameObject diagObj = new GameObject("TutorialDiagnostics");
                diagnostics = diagObj.AddComponent<TutorialDiagnostics>();
                Debug.Log("[TutorialQuickTest] Created TutorialDiagnostics");
            }
            
            // Run diagnostics
            StartCoroutine(diagnostics.RunCompleteDiagnostics());
        }
        
        [ContextMenu("Start Tutorial")]
        public void StartTutorial()
        {
            Debug.Log("[TutorialQuickTest] === STARTING TUTORIAL ===");
            
            if (zoneManager == null)
            {
                zoneManager = FindObjectOfType<TutorialZoneManager>();
            }
            
            if (zoneManager != null)
            {
                zoneManager.StartTutorial();
                Debug.Log("[TutorialQuickTest] Tutorial started!");
            }
            else
            {
                Debug.LogError("[TutorialQuickTest] No TutorialZoneManager found! Run setup first.");
            }
        }
        
        [ContextMenu("Reset Tutorial")]
        public void ResetTutorial()
        {
            Debug.Log("[TutorialQuickTest] === RESETTING TUTORIAL ===");
            
            if (zoneManager != null)
            {
                zoneManager.RestartTutorial();
                Debug.Log("[TutorialQuickTest] Tutorial reset!");
            }
            else
            {
                Debug.LogError("[TutorialQuickTest] No TutorialZoneManager found!");
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 200));
            
            GUILayout.Box("Tutorial Quick Test", GUILayout.Width(390));
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"Setup ({setupKey})", GUILayout.Width(90)))
            {
                SetupTutorialSystem();
            }
            if (GUILayout.Button($"Diagnostics ({diagnosticsKey})", GUILayout.Width(120)))
            {
                RunDiagnostics();
            }
            if (GUILayout.Button($"Start ({startTutorialKey})", GUILayout.Width(90)))
            {
                StartTutorial();
            }
            if (GUILayout.Button($"Reset ({resetKey})", GUILayout.Width(80)))
            {
                ResetTutorial();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Status display
            GUILayout.Label("Status:");
            GUILayout.Label($"Scene Setup: {(sceneSetup != null ? "✓" : "✗")}");
            GUILayout.Label($"Zone Manager: {(zoneManager != null ? "✓" : "✗")}");
            GUILayout.Label($"Diagnostics: {(diagnostics != null ? "✓" : "✗")}");
            
            if (zoneManager != null)
            {
                GUILayout.Label($"Tutorial Active: {zoneManager.IsTutorialActive}");
                GUILayout.Label($"Current Zone: {zoneManager.GetCurrentZoneIndex() + 1}/{zoneManager.GetZoneCount()}");
            }
            
            GUILayout.Space(10);
            GUILayout.Label("Instructions:");
            GUILayout.Label("1. Click 'Setup' to initialize tutorial system");
            GUILayout.Label("2. Click 'Diagnostics' to check for issues");
            GUILayout.Label("3. Click 'Start' to begin tutorial");
            GUILayout.Label("4. Use WASD to move, Q/E to rotate camera");
            
            GUILayout.EndArea();
        }
    }
}

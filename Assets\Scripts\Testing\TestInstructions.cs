using UnityEngine;

namespace AshesOfTheGrove.Testing
{
    /// <summary>
    /// Displays test instructions and manages the testing process
    /// </summary>
    public class TestInstructions : MonoBehaviour
    {
        [Header("Display Settings")]
        [SerializeField] private bool showInstructions = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.F2;
        
        private GUIStyle titleStyle;
        private GUIStyle instructionStyle;
        private bool stylesInitialized = false;
        
        private void Update()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                showInstructions = !showInstructions;
            }
        }
        
        private void InitializeStyles()
        {
            if (stylesInitialized) return;
            
            titleStyle = new GUIStyle();
            titleStyle.fontSize = 18;
            titleStyle.fontStyle = FontStyle.Bold;
            titleStyle.normal.textColor = Color.yellow;
            titleStyle.alignment = TextAnchor.UpperLeft;
            
            instructionStyle = new GUIStyle();
            instructionStyle.fontSize = 14;
            instructionStyle.normal.textColor = Color.white;
            instructionStyle.alignment = TextAnchor.UpperLeft;
            instructionStyle.wordWrap = true;
            
            stylesInitialized = true;
        }
        
        private void OnGUI()
        {
            if (!showInstructions) return;
            
            InitializeStyles();
            
            // Instructions panel
            float panelWidth = 400;
            float panelHeight = 500;
            float panelX = Screen.width - panelWidth - 10;
            float panelY = 10;
            
            GUI.Box(new Rect(panelX, panelY, panelWidth, panelHeight), "");
            
            float yPos = panelY + 10;
            float lineHeight = 20;
            float sectionSpacing = 10;
            
            // Title
            GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), "PLAYER INPUT TEST", titleStyle);
            yPos += lineHeight + sectionSpacing;
            
            GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), $"Press {toggleKey} to toggle this panel", instructionStyle);
            yPos += lineHeight + sectionSpacing;
            
            // Basic Controls
            GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), "BASIC CONTROLS:", titleStyle);
            yPos += lineHeight;
            
            string[] basicControls = {
                "• WASD or Arrow Keys: Move",
                "• Left Ctrl: Toggle Crouch",
                "• Left Shift: Hold to Run",
                "• F1: Toggle Debug Panel"
            };
            
            foreach (string control in basicControls)
            {
                GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), control, instructionStyle);
                yPos += lineHeight;
            }
            yPos += sectionSpacing;
            
            // Test Scenarios
            GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), "TEST SCENARIOS:", titleStyle);
            yPos += lineHeight;
            
            string[] testScenarios = {
                "1. Basic Movement:",
                "   - Move in all directions",
                "   - Check smooth movement",
                "   - Verify isometric rotation",
                "",
                "2. Stealth Mechanics:",
                "   - Toggle crouch (Ctrl)",
                "   - Hold run (Shift)",
                "   - Test speed differences",
                "",
                "3. Combined Actions:",
                "   - Crouch + Move",
                "   - Run + Move",
                "   - Quick direction changes",
                "",
                "4. Edge Cases:",
                "   - Rapid key presses",
                "   - Multiple keys at once",
                "   - Hold and release patterns"
            };
            
            foreach (string scenario in testScenarios)
            {
                if (scenario == "")
                {
                    yPos += lineHeight / 2;
                    continue;
                }
                
                GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), scenario, instructionStyle);
                yPos += lineHeight;
            }
            yPos += sectionSpacing;
            
            // Expected Results
            GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), "EXPECTED RESULTS:", titleStyle);
            yPos += lineHeight;
            
            string[] expectedResults = {
                "✓ Smooth movement in all directions",
                "✓ Player faces movement direction",
                "✓ Different speeds for walk/crouch/run",
                "✓ Noise levels change with movement",
                "✓ Responsive input handling",
                "✓ No jittery or erratic movement"
            };
            
            foreach (string result in expectedResults)
            {
                GUI.Label(new Rect(panelX + 10, yPos, panelWidth - 20, lineHeight), result, instructionStyle);
                yPos += lineHeight;
            }
        }
    }
}

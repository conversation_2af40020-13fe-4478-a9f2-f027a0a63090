{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 61532, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 61532, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 61532, "tid": 356, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 61532, "tid": 356, "ts": 1750946146526530, "dur": 7, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146526545, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 61532, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 61532, "tid": 1, "ts": 1750946146360899, "dur": 974, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 61532, "tid": 1, "ts": 1750946146361874, "dur": 14345, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 61532, "tid": 1, "ts": 1750946146376221, "dur": 75275, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146526548, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 61532, "tid": 163208757248, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146360876, "dur": 9401, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370278, "dur": 155830, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370286, "dur": 22, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370311, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370534, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370562, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146370567, "dur": 2288, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372859, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372893, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372895, "dur": 52, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372950, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372972, "dur": 15, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146372988, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373010, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373032, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373034, "dur": 18, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373054, "dur": 35, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373091, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373112, "dur": 16, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373130, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373173, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373202, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373204, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373229, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373231, "dur": 16, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373249, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373268, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373295, "dur": 36, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373335, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373359, "dur": 16, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373377, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373394, "dur": 16, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373412, "dur": 15, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373429, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373452, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373471, "dur": 13, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373486, "dur": 14, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373502, "dur": 17, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373520, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373537, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373565, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373582, "dur": 14, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373597, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373613, "dur": 16, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373632, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373655, "dur": 13, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373669, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373671, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373693, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373712, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373732, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373752, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373754, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373776, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373779, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373803, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373826, "dur": 12, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373839, "dur": 43, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373885, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373916, "dur": 15, "ph": "X", "name": "ReadAsync 1595", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373933, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373956, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146373979, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374001, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374018, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374036, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374037, "dur": 19, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374057, "dur": 15, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374074, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374090, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374105, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374107, "dur": 15, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374124, "dur": 15, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374141, "dur": 15, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374158, "dur": 13, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374174, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374194, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374216, "dur": 15, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374232, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374249, "dur": 17, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374267, "dur": 15, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374285, "dur": 17, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374303, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374320, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374338, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374356, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374372, "dur": 24, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374398, "dur": 20, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374420, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374438, "dur": 27, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374467, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374483, "dur": 41, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374526, "dur": 9, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374537, "dur": 7, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374546, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374572, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374600, "dur": 23, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374625, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374628, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374652, "dur": 21, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374676, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374700, "dur": 17, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374718, "dur": 15, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374735, "dur": 72, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374810, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374836, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374856, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374874, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374891, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374907, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374929, "dur": 28, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374961, "dur": 20, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374982, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374984, "dur": 13, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146374999, "dur": 15, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375016, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375032, "dur": 18, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375051, "dur": 23, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375076, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375098, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375118, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375136, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375139, "dur": 18, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375161, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375184, "dur": 18, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375205, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375226, "dur": 14, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375241, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375259, "dur": 15, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375276, "dur": 12, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375289, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375307, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375326, "dur": 10, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375338, "dur": 14, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375354, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375370, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375386, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375403, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375418, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375435, "dur": 14, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375451, "dur": 13, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375466, "dur": 38, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375505, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375522, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375538, "dur": 16, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375556, "dur": 13, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375571, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375592, "dur": 24, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375617, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375633, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375651, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375669, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375694, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375714, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375734, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375758, "dur": 17, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375778, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375796, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375814, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375831, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375832, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375849, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375867, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375885, "dur": 17, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375904, "dur": 16, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375921, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375923, "dur": 17, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375941, "dur": 17, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375959, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375961, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146375981, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376002, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376020, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376038, "dur": 15, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376055, "dur": 15, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376072, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376087, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376105, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376129, "dur": 16, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376146, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376169, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376186, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376204, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376221, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376238, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376255, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376274, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376289, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376308, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376326, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376342, "dur": 14, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376358, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376374, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376401, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376430, "dur": 15, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376447, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376462, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376478, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376495, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376521, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376540, "dur": 14, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376556, "dur": 13, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376571, "dur": 13, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376586, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376602, "dur": 12, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376615, "dur": 15, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376632, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376648, "dur": 15, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376664, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376681, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376705, "dur": 12, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376719, "dur": 16, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376736, "dur": 14, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376752, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376769, "dur": 12, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376783, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376797, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376812, "dur": 13, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376826, "dur": 13, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376840, "dur": 14, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376856, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376872, "dur": 14, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376888, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376904, "dur": 13, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376918, "dur": 10, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376929, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376945, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376963, "dur": 13, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376978, "dur": 14, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146376994, "dur": 13, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377008, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377023, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377045, "dur": 15, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377061, "dur": 13, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377076, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377093, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377108, "dur": 155, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377265, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377289, "dur": 12, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377303, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377320, "dur": 14, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377335, "dur": 14, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377351, "dur": 13, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377365, "dur": 16, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377383, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377398, "dur": 13, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377413, "dur": 13, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377428, "dur": 13, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377444, "dur": 18, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377463, "dur": 13, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377478, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377500, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377520, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377537, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377555, "dur": 14, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377571, "dur": 14, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377586, "dur": 43, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377631, "dur": 13, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377646, "dur": 14, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377661, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377662, "dur": 13, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377677, "dur": 10, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377688, "dur": 21, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377711, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377737, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377739, "dur": 25, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377766, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377791, "dur": 16, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377809, "dur": 29, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377841, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377865, "dur": 16, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377884, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377902, "dur": 14, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377917, "dur": 14, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377933, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377948, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377963, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146377977, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378000, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378017, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378038, "dur": 17, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378057, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378074, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378092, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378108, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378125, "dur": 18, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378145, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378168, "dur": 19, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378192, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378214, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378230, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378250, "dur": 14, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378265, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378284, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378301, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378318, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378334, "dur": 17, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378353, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378370, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378389, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378406, "dur": 153, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378563, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378606, "dur": 2, "ph": "X", "name": "ProcessMessages 4398", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378608, "dur": 17, "ph": "X", "name": "ReadAsync 4398", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378627, "dur": 18, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378646, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378666, "dur": 17, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378685, "dur": 14, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378701, "dur": 29, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378733, "dur": 26, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378761, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378783, "dur": 27, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378816, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378819, "dur": 50, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378871, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378872, "dur": 17, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378892, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378911, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378935, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378953, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378973, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146378994, "dur": 23, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379019, "dur": 16, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379036, "dur": 18, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379056, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379079, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379104, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379121, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379136, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379156, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379179, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379181, "dur": 21, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379205, "dur": 154, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379361, "dur": 35, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379397, "dur": 1, "ph": "X", "name": "ProcessMessages 3742", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379400, "dur": 17, "ph": "X", "name": "ReadAsync 3742", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379419, "dur": 14, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379435, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379454, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379474, "dur": 118, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379595, "dur": 26, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146379635, "dur": 25, "ph": "X", "name": "ReadAsync 1550", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146382750, "dur": 2, "ph": "X", "name": "ProcessMessages 2304", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146382754, "dur": 160, "ph": "X", "name": "ReadAsync 2304", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146382914, "dur": 10, "ph": "X", "name": "ProcessMessages 20537", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146382925, "dur": 23, "ph": "X", "name": "ReadAsync 20537", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146382950, "dur": 58, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383012, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383037, "dur": 29, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383069, "dur": 65, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383137, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383159, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383177, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383199, "dur": 54, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383255, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383278, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383295, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383311, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383390, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383418, "dur": 33, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383453, "dur": 65, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383521, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383543, "dur": 19, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383564, "dur": 16, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383582, "dur": 64, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383647, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383679, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383697, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383714, "dur": 60, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383776, "dur": 58, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383836, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383854, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383876, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383894, "dur": 17, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383913, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383962, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146383986, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384003, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384025, "dur": 58, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384086, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384108, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384125, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384141, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384142, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384162, "dur": 59, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384222, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384246, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384271, "dur": 32, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384306, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384307, "dur": 21, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384331, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384349, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384368, "dur": 14, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384383, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384400, "dur": 57, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384460, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384482, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384498, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384515, "dur": 49, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384565, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384615, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384632, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384653, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384671, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384690, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384709, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384727, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384744, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384803, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384825, "dur": 15, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384841, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384858, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384903, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384921, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384939, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146384956, "dur": 56, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385016, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385033, "dur": 15, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385054, "dur": 13, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385069, "dur": 41, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385117, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385135, "dur": 13, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385150, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385166, "dur": 55, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385223, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385249, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385272, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385293, "dur": 39, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385333, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385363, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385382, "dur": 74, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385457, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385487, "dur": 13, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385502, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385550, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385573, "dur": 48, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385623, "dur": 23, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385664, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385688, "dur": 37, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385727, "dur": 61, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385789, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385815, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385832, "dur": 45, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385879, "dur": 39, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385919, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385968, "dur": 15, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146385984, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386025, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386077, "dur": 15, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386093, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386154, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386181, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386203, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386225, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386243, "dur": 34, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386283, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386314, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386332, "dur": 55, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386389, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386434, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386476, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386477, "dur": 24, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386510, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386528, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386564, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386585, "dur": 14, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386629, "dur": 17, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386647, "dur": 52, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386700, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386729, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146386756, "dur": 279, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387037, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387070, "dur": 2, "ph": "X", "name": "ProcessMessages 3777", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387073, "dur": 37, "ph": "X", "name": "ReadAsync 3777", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387113, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387150, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387152, "dur": 18, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387171, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387206, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387293, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387296, "dur": 192, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387564, "dur": 22, "ph": "X", "name": "ProcessMessages 1686", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387586, "dur": 61, "ph": "X", "name": "ReadAsync 1686", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387649, "dur": 25, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387677, "dur": 43, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387749, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387751, "dur": 33, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387787, "dur": 50, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387839, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387841, "dur": 55, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387899, "dur": 17, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387949, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387951, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146387973, "dur": 151, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388129, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388215, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388218, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388271, "dur": 12, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388284, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388328, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388331, "dur": 50, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388383, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388386, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388440, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388442, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388499, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388501, "dur": 47, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388551, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388553, "dur": 68, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388623, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388626, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388683, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388685, "dur": 49, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388737, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388740, "dur": 64, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388806, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388808, "dur": 79, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388932, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388934, "dur": 58, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388995, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146388999, "dur": 49, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389050, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389052, "dur": 77, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389132, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389134, "dur": 49, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389209, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389212, "dur": 51, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389266, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389268, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389342, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389345, "dur": 50, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389397, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389399, "dur": 43, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389444, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389446, "dur": 68, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389532, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389535, "dur": 50, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389587, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389622, "dur": 58, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389681, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389683, "dur": 43, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389767, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389770, "dur": 89, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389861, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146389909, "dur": 54, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390012, "dur": 3, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390027, "dur": 92, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390121, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390124, "dur": 59, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390185, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146390187, "dur": 12868, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146403478, "dur": 126, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146403752, "dur": 418, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146404626, "dur": 7, "ph": "X", "name": "ProcessMessages 1236", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146404816, "dur": 872, "ph": "X", "name": "ReadAsync 1236", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146405691, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146405866, "dur": 414, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146406424, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146406607, "dur": 337, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146407211, "dur": 151, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146407363, "dur": 337, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146407840, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146407944, "dur": 553, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146408754, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146408889, "dur": 343, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146409373, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146409375, "dur": 42682, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146452063, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146452066, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146452100, "dur": 21, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146452123, "dur": 7474, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146459602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146459607, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146459650, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146459826, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146459853, "dur": 568, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460427, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460430, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460470, "dur": 223, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460697, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460734, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146460737, "dur": 436, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461178, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461213, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461244, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461247, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461290, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461333, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461400, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461438, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461477, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461526, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461558, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461611, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461665, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461668, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461706, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461708, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461748, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461750, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461795, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461797, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461832, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461833, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461870, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461900, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461960, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146461962, "dur": 61, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462025, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462028, "dur": 51, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462081, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462084, "dur": 43, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462128, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462130, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462186, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462226, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462228, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462267, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462269, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462314, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462318, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462360, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462394, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462425, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462576, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462627, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462662, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462664, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462707, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462741, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146462764, "dur": 435, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463204, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463237, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463277, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463312, "dur": 496, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463810, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463843, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463846, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146463880, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464023, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464055, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464103, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464140, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464142, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464172, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464376, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464406, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464432, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464454, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464485, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464487, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464516, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464646, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464673, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464707, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464742, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464769, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464799, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464803, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464838, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464871, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464895, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464898, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464925, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464947, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464968, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146464994, "dur": 48466, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146513470, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146513474, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146513524, "dur": 532, "ph": "X", "name": "ProcessMessages 3573", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146514057, "dur": 3884, "ph": "X", "name": "ReadAsync 3573", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146517945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146517948, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 61532, "tid": 163208757248, "ts": 1750946146517977, "dur": 8126, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146526555, "dur": 611, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 61532, "tid": 158913789952, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 61532, "tid": 158913789952, "ts": 1750946146360842, "dur": 90672, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 61532, "tid": 158913789952, "ts": 1750946146451515, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 61532, "tid": 158913789952, "ts": 1750946146451517, "dur": 45, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146527168, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 61532, "tid": 154618822656, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 61532, "tid": 154618822656, "ts": 1750946146358564, "dur": 167574, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 61532, "tid": 154618822656, "ts": 1750946146358645, "dur": 1922, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 61532, "tid": 154618822656, "ts": 1750946146526141, "dur": 32, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 61532, "tid": 154618822656, "ts": 1750946146526151, "dur": 13, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146527173, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750946146369896, "dur": 1263, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146371168, "dur": 541, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146371855, "dur": 588, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146380835, "dur": 1694, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750946146386965, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ****************, "dur": 15121, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146387588, "dur": 128504, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146516093, "dur": 104, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146516197, "dur": 300, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146516501, "dur": 124, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146517547, "dur": 1181, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750946146372144, "dur": 15453, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146387619, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146387724, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146387720, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750946146387793, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146387887, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146388078, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146388129, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_771434CFFF9D260C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750946146388555, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750946146388985, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750946146389479, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750946146389633, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146390944, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146392029, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146393083, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146394499, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146395607, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146396721, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146397826, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146398842, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146400093, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146400360, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146400856, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146401458, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146401860, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750946146402065, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750946146402703, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146402795, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146402917, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146403024, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146403463, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146403693, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146403911, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146404545, "dur": 51563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146459094, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146459212, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146459370, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146460000, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146460252, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146460726, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146460841, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146460933, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750946146456110, "dur": 5132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750946146461242, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146461384, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146461487, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146461547, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1750946146461720, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146462366, "dur": 2045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750946146464432, "dur": 51662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146372172, "dur": 15447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146387633, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146387717, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1750946146387625, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750946146388031, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FB86898111D557DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750946146388195, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146388319, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750946146388389, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750946146388457, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146388533, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750946146388916, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750946146389143, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750946146389388, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750946146389528, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146390674, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146391830, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146392985, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146395732, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Utilities\\ProgressUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750946146394654, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146396366, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146397372, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146398451, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146399524, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146400541, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146400869, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146401476, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146401886, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146402928, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146403025, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146403454, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146403674, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146403920, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146404534, "dur": 54574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146459180, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146460174, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146461048, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146461099, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146461243, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146461521, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146462314, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146462656, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750946146459109, "dur": 4494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750946146463603, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750946146463752, "dur": 52337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146372168, "dur": 15442, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146387619, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146387758, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1750946146387722, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750946146387938, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146388008, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388007, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_02D2E9C797962C02.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388121, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388120, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388175, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146388319, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146388435, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388817, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750946146388948, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750946146389181, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750946146389391, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750946146389599, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146390804, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146391818, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146392907, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146394251, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146395266, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146396703, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146397886, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146399095, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146400116, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146400301, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146400864, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146401468, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146401878, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146402916, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146403024, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146403081, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146403367, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146403659, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146403984, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146404520, "dur": 51606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146456594, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146459425, "dur": 495, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146460000, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146460316, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146460784, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146460842, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146460934, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146461049, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146461173, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146461243, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146461404, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146461861, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750946146456135, "dur": 6044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750946146462180, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146462354, "dur": 1978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750946146464332, "dur": 51800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146372201, "dur": 15427, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146387641, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146387705, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1750946146387633, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750946146387965, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146387964, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0767B7051AB61AA1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388103, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0CACEFF522BB87D4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388155, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146388321, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146388373, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388574, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388746, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388977, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389108, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389172, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389244, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389469, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389556, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389623, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389723, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389828, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146389886, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390079, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390148, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390259, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390376, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390437, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390533, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390641, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390719, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390877, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146390955, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391105, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391259, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391313, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391389, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391451, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391513, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391581, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391659, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391718, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391785, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391870, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146391976, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392076, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392222, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392681, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392734, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392790, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392848, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146392944, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393010, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393117, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393210, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393265, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393315, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393368, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393420, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393855, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146393974, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394230, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394342, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394442, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394595, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394697, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146394969, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146395073, "dur": 737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396008, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396094, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396148, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396215, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396266, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396338, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396445, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396502, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396608, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396659, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396761, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146396838, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397075, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397417, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397497, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397561, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397629, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397689, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397759, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397826, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146397964, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398062, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398133, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398250, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398311, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398367, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398518, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398626, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398736, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398806, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146398857, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399096, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399336, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399388, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399445, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399498, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399636, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399744, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399840, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399892, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146399957, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146400126, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750946146388875, "dur": 11611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750946146400487, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146400635, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146400875, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146401489, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146401893, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146402917, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146403020, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146403296, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146403640, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750946146403853, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750946146404200, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146404274, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146404536, "dur": 51637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146456516, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146457504, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146459094, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146460784, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146460842, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146460934, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146461097, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146461243, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750946146456175, "dur": 5138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750946146461314, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146461592, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146461694, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146461890, "dur": 2361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750946146464340, "dur": 51807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146372218, "dur": 15421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146387656, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146387708, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1750946146387648, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146387868, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146387925, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146388120, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_1E575EBD1ADFF155.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146388205, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146388313, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146388597, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750946146389102, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750946146389338, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750946146389402, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750946146389601, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146389684, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146391236, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146392303, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146393314, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146394713, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146396180, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146397206, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146398434, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146399573, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146400636, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146400874, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146401497, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146401849, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146402087, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146403012, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146403306, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146403638, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146403861, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146404377, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146404527, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146404648, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146404851, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146404946, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146405024, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146405496, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146405573, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750946146405646, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146406004, "dur": 50154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146458554, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Embedded.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146459092, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146459905, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146460000, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146460726, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750946146456159, "dur": 5541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750946146461701, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146461864, "dur": 2194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750946146464094, "dur": 52026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146372505, "dur": 15336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146387864, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146387853, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146387941, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146387998, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146388199, "dur": 535, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_772DB31288B13074.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146388745, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146388977, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389110, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389178, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389324, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389555, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389645, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389727, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389817, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146389881, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390073, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390205, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390285, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390380, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390443, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390543, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390653, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390755, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390851, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146390906, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391078, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391159, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391257, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391314, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391393, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391450, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391511, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391581, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391656, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391726, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391783, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391841, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146391995, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392103, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392198, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392274, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392340, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392391, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392494, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392546, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392649, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392706, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392762, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146392924, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146393023, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146393310, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146393364, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146393424, "dur": 428, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146393853, "dur": 923, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146394800, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146394942, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146394996, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146395055, "dur": 749, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396022, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396154, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396275, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396707, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396767, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396823, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146396944, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397002, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397054, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397122, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397178, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397252, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397312, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397413, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397480, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397543, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397602, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397715, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397779, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397836, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397889, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146397980, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398043, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398243, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398298, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398353, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398460, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398515, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398573, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398625, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398736, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398846, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146398902, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399096, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399339, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399389, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399451, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399541, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399597, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399660, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399761, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399816, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CoroutineTestWorkItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399881, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146399993, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400048, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400146, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400246, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400521, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750946146388879, "dur": 11859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400906, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146400969, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146401520, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146401844, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146402062, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146402888, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146403069, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146403190, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750946146403351, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146403713, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146403842, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1750946146404382, "dur": 59, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146404804, "dur": 46882, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1750946146456172, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146459907, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146460313, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146460621, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146460727, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146460784, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146461098, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146461242, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146461520, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146462312, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpsPolicy.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146462656, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.ObjectPool.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750946146460011, "dur": 4205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750946146464217, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750946146464349, "dur": 51809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146372266, "dur": 15397, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146387675, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750946146387670, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ****************, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146388029, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146388249, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750946146388247, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146388482, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146388560, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146388808, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750946146389416, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750946146389545, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146390819, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146392250, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146393268, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146394620, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146395734, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146397247, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146398405, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146399510, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146400498, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146400868, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146401465, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146401867, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146402053, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146402786, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146402961, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146403036, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146403392, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146403666, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146403950, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146404510, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146404808, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146404922, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146405882, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146405962, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146406062, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146406598, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146406685, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146407062, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750946146407139, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146407223, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146407506, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750946146407586, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750946146407874, "dur": 105210, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750946146372284, "dur": 15399, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146387700, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146387755, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1750946146387693, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146387972, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388183, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388181, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388357, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388356, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D3E2F14B3A6F429A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388546, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388722, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750946146388988, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750946146389390, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750946146389504, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750946146389629, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146391034, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146392075, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146393311, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146394741, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146395998, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146396888, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146397957, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146399121, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146400272, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146400842, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146401467, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146401876, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146402126, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750946146402709, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146402842, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146402916, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146403015, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146403196, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750946146403348, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750946146403691, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146403774, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146403872, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146404512, "dur": 46672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146451185, "dur": 2348, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146453533, "dur": 2601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146457307, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146459180, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146459957, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146460173, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146460840, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146461173, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146456152, "dur": 5129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750946146461281, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146461530, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146461603, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750946146461738, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146462815, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146462893, "dur": 1565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750946146464482, "dur": 51688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146372301, "dur": 15395, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146387710, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146387702, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146388029, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1F1578B895CD76F0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146388170, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146388285, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146388284, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146388532, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1750946146388684, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750946146389009, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750946146389385, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750946146389601, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146390817, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146391842, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146392927, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146394295, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146395293, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146396542, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146397590, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146398633, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146400091, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146400299, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146400840, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146401444, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146401891, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146402177, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750946146403023, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146403324, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146403448, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750946146403833, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146403931, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750946146404033, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750946146404414, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146404536, "dur": 51582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146457670, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146458399, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Google.Protobuf.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146459179, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146459370, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146459906, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.Local.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146460622, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146460841, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146461099, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146461173, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146461340, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146461447, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146461521, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750946146456131, "dur": 5882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750946146462017, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146462218, "dur": 2074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750946146464331, "dur": 51783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146372327, "dur": 15381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146387845, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD6D4C7F30E8FD4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750946146387998, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146387997, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_4E13DA5D4C1EBAA5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750946146388186, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750946146388412, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146388471, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146388559, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750946146388796, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750946146389051, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750946146389264, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750946146389411, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750946146389588, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146390838, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146391945, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146393099, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146394692, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SelectUnit_T.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750946146394540, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146396465, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146397492, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146398604, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146399813, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146400739, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146400844, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146401470, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146401873, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750946146402206, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146402261, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750946146402752, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146402923, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146403021, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146403325, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146403639, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750946146403996, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146403787, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750946146404427, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146404545, "dur": 51593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146459094, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\ucrtbase.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146460001, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146460315, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146460623, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146456140, "dur": 4788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750946146460929, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750946146461242, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146461364, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146461466, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146462210, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146462655, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750946146461080, "dur": 3279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750946146464427, "dur": 51666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146372637, "dur": 15288, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146387942, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146387934, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750946146388157, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146388156, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_32492E6E491A66EE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750946146388293, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146388383, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_32492E6E491A66EE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750946146388794, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750946146389164, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750946146389323, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750946146389466, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750946146389603, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146390807, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146391841, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146392910, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146394285, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146395722, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750946146395305, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146396855, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146398013, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146399152, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146400245, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146400335, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146400847, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146401450, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146401841, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750946146402291, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750946146403174, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146403466, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750946146403778, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146403590, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750946146404054, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146404136, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146404564, "dur": 51614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146459093, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146459905, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146460654, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146460882, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146456179, "dur": 4865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750946146461044, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750946146461175, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146461307, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146461445, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146462128, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750946146461116, "dur": 3299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750946146464496, "dur": 51610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146372658, "dur": 15280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146387946, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_68BCB468E6CA02D0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146388041, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_04DBE999BFBDB12D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146388123, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_04DBE999BFBDB12D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146388231, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146388342, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750946146388448, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146388681, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750946146389044, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146389315, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750946146389530, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146390707, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146391822, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146392848, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146394248, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146395724, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\ShortInspector.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750946146395400, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146397085, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146398445, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146399614, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146400614, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146400868, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146401472, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146401875, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146402141, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750946146402684, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146402838, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146402925, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146403016, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146403298, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146403641, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146403842, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146404512, "dur": 2410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146406923, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750946146407017, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750946146407283, "dur": 48871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146459906, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146460000, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146460251, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146456155, "dur": 4550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750946146460706, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146460933, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146461520, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146461640, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": ****************, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750946146460841, "dur": 3647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750946146464489, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750946146464573, "dur": 51545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146372401, "dur": 15374, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146387798, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146387789, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750946146388011, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146388009, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750946146388071, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146388126, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750946146388336, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146388464, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146388634, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750946146388966, "dur": 638, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750946146389605, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146390823, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146391790, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146392896, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146394318, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146395310, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146396712, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146397803, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146399034, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146400169, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146400222, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146400299, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146400839, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146401446, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146401856, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750946146402180, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750946146402749, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146402918, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146403026, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146403439, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146403677, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146403901, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146404531, "dur": 54567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146459179, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146460623, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146460726, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146460841, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146460971, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-2-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146461049, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146461242, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146461365, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146462656, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750946146459100, "dur": 4208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750946146463308, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146463444, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750946146464473, "dur": 51626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146372424, "dur": 15367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146387883, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146387881, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_DF935DB001343E8A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750946146388120, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146388118, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750946146388175, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146388340, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146388443, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750946146388814, "dur": 641, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750946146389457, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750946146389550, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146389633, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146390864, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146391940, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146393047, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146394439, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146395461, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146396900, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146398095, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146399228, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146400227, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146400358, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146400854, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146401492, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146401905, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146402920, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146403036, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146403383, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146403663, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146403957, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146404523, "dur": 51626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146456177, "dur": 3965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750946146460143, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146460726, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146460785, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146461242, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146461365, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146461862, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146462128, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-1.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146462655, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Memory.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750946146460303, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750946146463961, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750946146464086, "dur": 52015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146372751, "dur": 15242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146388000, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_62E3103B1EFE0DCE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750946146388058, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146388185, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E9D933C7AF1168F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750946146388266, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146388265, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750946146388476, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146388808, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750946146389175, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750946146389534, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146390709, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146391977, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146393020, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146394437, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146396013, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146396962, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146398107, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146399229, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146400065, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146400589, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146400873, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146401485, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146401885, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146402926, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146403018, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146403331, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146403649, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146403883, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146404521, "dur": 51643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146459095, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146459423, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146459957, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146460173, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146460251, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146460934, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750946146456166, "dur": 5067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750946146461233, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146461420, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146461514, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1750946146461676, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1750946146461784, "dur": 1948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750946146463733, "dur": 52390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146372770, "dur": 15234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146388012, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_046FB1380673C4F3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750946146388120, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_046FB1380673C4F3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750946146388330, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750946146388521, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750946146388687, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750946146388919, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750946146389084, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750946146389270, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750946146389428, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750946146389499, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750946146389602, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146389710, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146391102, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146392172, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146393285, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146394725, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146396151, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146397164, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146398178, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146399323, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146400369, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146400861, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146401462, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146401865, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750946146402074, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750946146403107, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146403334, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146403650, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146403881, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146404511, "dur": 2091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146406603, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750946146406713, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750946146407100, "dur": 49080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146459178, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146460000, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146460251, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146460622, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146460755, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146456181, "dur": 4718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750946146460900, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750946146461100, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146461341, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146461520, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146461641, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146462313, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146462656, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-locale-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750946146461004, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750946146464358, "dur": 51766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146372485, "dur": 15339, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146387878, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146387929, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146388440, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750946146388640, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1750946146389001, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750946146389277, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750946146389485, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750946146389592, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146391636, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer@1.2.2\\Editor\\MarkerData.cs"}}, {"pid": 12345, "tid": 17, "ts": 1750946146390781, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146392635, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146394095, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146395141, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146396510, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146397573, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146398797, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146400332, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146400850, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146401454, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146401843, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146402035, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146402758, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146403029, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146403424, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146403670, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146403944, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146404510, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146404642, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146405090, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146405179, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146406259, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146406338, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146406918, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750946146406998, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146407342, "dur": 48771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146459094, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146456115, "dur": 3133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146459249, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146459907, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146459959, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146460314, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146460933, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146461339, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146462124, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.Messages.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146462655, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750946146459433, "dur": 4103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750946146463537, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146463653, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750946146463765, "dur": 52325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146372238, "dur": 15414, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146387715, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1750946146387659, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750946146387888, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146388141, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2DD09EF85C7C380D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750946146388215, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146388544, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750946146388649, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750946146388819, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750946146389106, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750946146389325, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750946146389418, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750946146389600, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146389670, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750946146389722, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146390682, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146391830, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146392897, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146394213, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146395226, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146396333, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146397432, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146398883, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\UI\\UnityMenuItem.cs"}}, {"pid": 12345, "tid": 18, "ts": 1750946146398558, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146400298, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146400840, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146401443, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146401844, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750946146402088, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750946146403083, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146403339, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146403654, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146404012, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146404526, "dur": 51634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146457799, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146460172, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146456162, "dur": 4451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750946146460614, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146460787, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146461049, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146461242, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146461339, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146461640, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 18, "ts": ****************, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-time-l1-1-0.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750946146460786, "dur": 3684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750946146464471, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750946146464560, "dur": 51607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146372523, "dur": 15331, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146387860, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_262098ACA416DFC1.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750946146387960, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_262098ACA416DFC1.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750946146388258, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146388257, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750946146388406, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146388483, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146388563, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1750946146388813, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1750946146389001, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750946146389282, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750946146389515, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750946146389611, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146389718, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146391207, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146392386, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146393425, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146394867, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146396265, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146397297, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146398472, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146399585, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146400607, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146400870, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146401475, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146401876, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146402916, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146403056, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146403359, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146403662, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146403971, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146404514, "dur": 51597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146459093, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146459958, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146456112, "dur": 4011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750946146460123, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146460621, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146460882, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146461306, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146461520, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146461676, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146462656, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Localization.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750946146460322, "dur": 3882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750946146464258, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750946146464316, "dur": 51788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146372541, "dur": 15324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146387874, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F4C82E9DCF8C85FF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750946146387975, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F4C82E9DCF8C85FF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750946146388167, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146388166, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750946146388318, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146388382, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750946146388586, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750946146388807, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750946146389138, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1750946146389417, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750946146389538, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146390707, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146391798, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146392826, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146394207, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146395276, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146396670, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146397762, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146398815, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146399974, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146400665, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146400899, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146401495, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146401897, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146402946, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146403034, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146403409, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146403671, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146403937, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146404546, "dur": 51626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146459315, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146460314, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146460768, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146460933, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146456173, "dur": 4881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750946146461054, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146461495, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750946146461602, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1750946146461702, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146461921, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146461997, "dur": 2283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750946146464320, "dur": 51821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146372559, "dur": 15319, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146387892, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146387884, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750946146388017, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146388072, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750946146388189, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146388299, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8517F10BC8FA0755.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750946146388450, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146388602, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146388652, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1750946146388986, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750946146389377, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750946146389530, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146390742, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146391931, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146393037, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146394475, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146395726, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Description\\IGraphElementDescription.cs"}}, {"pid": 12345, "tid": 21, "ts": 1750946146395481, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146397074, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146398185, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146399317, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146400219, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146400367, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146400873, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146401481, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146401884, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146402963, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146403047, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146403350, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146403657, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146403996, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146404549, "dur": 51626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146459094, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146459425, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.StaticFiles.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146460000, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146460315, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.Messages.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146460841, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146461049, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146461339, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146461519, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750946146456183, "dur": 6380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750946146462563, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146462849, "dur": 1601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750946146464480, "dur": 51629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146372578, "dur": 15309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146387901, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146387894, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E79E12ACD5E69601.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146388020, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E79E12ACD5E69601.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146388240, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146388239, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146388347, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146388418, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146388814, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750946146389059, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750946146389198, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750946146389477, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750946146389610, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146389683, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146390972, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146392051, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146393210, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146394610, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146395647, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146396918, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146397998, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146399109, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146400131, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146400367, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146400857, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146401457, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146401862, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146402076, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750946146403341, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146403392, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750946146403476, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750946146403654, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750946146404021, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146404088, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146404532, "dur": 51634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146460000, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146460841, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146460933, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146460992, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146461242, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750946146456167, "dur": 5139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750946146461307, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146461543, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1750946146461721, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1750946146461787, "dur": 2197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750946146464031, "dur": 52055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146372598, "dur": 15301, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146387943, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146388236, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146388353, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146388462, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146388649, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750946146388921, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750946146389226, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750946146389570, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750946146389626, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146390845, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146391849, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146392947, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146394347, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146395357, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146396780, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146397839, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146398841, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146399984, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146400693, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146400907, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146401496, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146401855, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750946146402049, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750946146402753, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146402941, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750946146403073, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146403179, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750946146404004, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146404120, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146404535, "dur": 51636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146459094, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750946146456172, "dur": 3696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750946146459868, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146460934, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750946146462655, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750946146460027, "dur": 4189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750946146464217, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750946146464369, "dur": 51786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146372616, "dur": 15296, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146387931, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146387920, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750946146388020, "dur": 352, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750946146388463, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146388573, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750946146388684, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1750946146388768, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750946146388903, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1750946146389181, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750946146389558, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146389610, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750946146389732, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146390930, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146392127, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146393255, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146394626, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146396196, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146397228, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146398247, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146399373, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146400473, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146400865, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146401470, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146401872, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750946146402149, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146402808, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146402240, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750946146402985, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146403344, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146403653, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146404004, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146404532, "dur": 51612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146459093, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146459424, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146459999, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146460175, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146460314, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146460784, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146461049, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146461447, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146461518, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146461771, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750946146456146, "dur": 5702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750946146461848, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146461939, "dur": 2320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750946146464313, "dur": 51816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146372353, "dur": 15375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146387738, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_816435D8968F6544.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750946146387870, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": ****************, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8E4D74A20FBDC909.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750946146388200, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4137C56C25BAB6AB.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750946146388439, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750946146388587, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750946146388983, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1750946146389230, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750946146389502, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750946146389603, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146389693, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146390953, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146391993, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146393129, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146394542, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146395573, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146396821, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146397979, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146399100, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146400151, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146400324, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146400860, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146401462, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146401864, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750946146402078, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750946146402863, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146403026, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146403091, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146403329, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146403658, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146403990, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146404530, "dur": 51638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146457523, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146459094, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146459179, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146460174, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146460841, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146460934, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146461052, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750946146456169, "dur": 5113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750946146461282, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146461604, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 25, "ts": 1750946146461714, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146462232, "dur": 2070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750946146464302, "dur": 51825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146372381, "dur": 15367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146387755, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146387948, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146387947, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_99235D0DADAB3855.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146388205, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1750946146388157, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_AC5852D97D0872BC.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146388321, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_AC5852D97D0872BC.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146388784, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750946146389085, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1750946146389317, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1750946146389575, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750946146389637, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146390885, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146391964, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146393096, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146394498, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146395492, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146397000, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146398071, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146399179, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146400302, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146400854, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146401456, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146401858, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146402093, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750946146402856, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146403043, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750946146403127, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146403185, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750946146403655, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146403740, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146403882, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146404526, "dur": 51615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146459180, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146459908, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146460174, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146460623, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146460971, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750946146456143, "dur": 4933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750946146461076, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146461344, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146461463, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146461565, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146461664, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1750946146461761, "dur": 1659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146463457, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750946146464562, "dur": 51575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146372689, "dur": 15264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146387962, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9E74BE77200C0976.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750946146388179, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146388348, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146388433, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750946146388659, "dur": 289, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750946146389408, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146389546, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146390745, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146391935, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146393028, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146394372, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146395719, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Inspection\\MacroEditor.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750946146395413, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146397139, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146398356, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146399462, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146400486, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146400863, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146401463, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146401868, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750946146402275, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146402364, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750946146403157, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146403488, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146403678, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146403892, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146404508, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750946146404632, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750946146404832, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146404925, "dur": 51277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146457212, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146458269, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146458501, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.CoreLib.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146458755, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146459908, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146460001, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146460624, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146460882, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146456203, "dur": 4759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750946146460963, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750946146461520, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146461771, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 27, "ts": ****************, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750946146461125, "dur": 3282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750946146464494, "dur": 51618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146372712, "dur": 15255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146387975, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FA846FADE8279C51.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750946146388199, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146388311, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146388511, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750946146388638, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750946146388799, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750946146389259, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750946146389383, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750946146389623, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146390839, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146391951, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146393041, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146394428, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146395614, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146397048, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146398123, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146399324, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146400358, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146400853, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146401450, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146401840, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750946146402199, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146403509, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146403673, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750946146403869, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146404703, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146404804, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750946146404888, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146405715, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750946146405788, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146406107, "dur": 50036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146458312, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146458637, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146459095, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Channels.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146459906, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146460250, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146460726, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146456145, "dur": 4654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146460800, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750946146461340, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146461520, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146462656, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146463671, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750946146461121, "dur": 3295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750946146464490, "dur": 51646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146372731, "dur": 15253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146387992, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1FFDB2139C1829E2.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750946146388079, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1FFDB2139C1829E2.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750946146388189, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146388307, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146388477, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750946146388698, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750946146388800, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750946146389061, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 29, "ts": 1750946146389279, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 29, "ts": 1750946146389496, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750946146389606, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146390974, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146392042, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146393154, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146394541, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146395621, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146397045, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146398351, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146399781, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146400554, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146400884, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146401491, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146401893, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146402936, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146403026, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146403416, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146403673, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146403928, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146404514, "dur": 51588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146456515, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146457459, "dur": 727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146459092, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146459179, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146460315, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146456104, "dur": 4466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750946146460571, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750946146460841, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146460934, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146461049, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146461240, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146461488, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146461640, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146462630, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750946146460818, "dur": 3376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750946146464273, "dur": 51842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146372435, "dur": 15368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146387812, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8DB4C22B24A5A2EB.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750946146387869, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146388022, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9118DBDC897F9C38.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750946146388193, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146388442, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 30, "ts": 1750946146388919, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750946146388973, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146389187, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750946146389470, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750946146389616, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146390904, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146392043, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146393134, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146394571, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146395743, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146397120, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146398176, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146399351, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146400421, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146400871, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146401480, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146401883, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146402915, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146403017, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146403297, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146403642, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146403841, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146404513, "dur": 49025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146453538, "dur": 2591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146459369, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750946146459956, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750946146460251, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750946146456139, "dur": 4583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750946146460723, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750946146461100, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750946146461174, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 30, "ts": ****************, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll"}}, {"pid": 12345, "tid": 30, "ts": ****************, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.Abstractions.dll"}}, {"pid": 12345, "tid": 30, "ts": ****************, "dur": 3333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": ****************, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": ****************, "dur": 51879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": ****************, "dur": 15356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": ****************, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": ****************, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_E966188727BA7937.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750946146388162, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146388161, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB998ABA4D09E52A.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750946146388306, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146388463, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146388559, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_0CBB1EE9BC1FA429.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750946146388986, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750946146389173, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750946146389356, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750946146389446, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750946146389638, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146391143, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146392203, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146393288, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146394778, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146396543, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146397580, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146398897, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146400020, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146400687, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146400882, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146401490, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146401887, "dur": 1056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146402944, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146403031, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146403401, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146403664, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146403964, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146404517, "dur": 51586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146456105, "dur": 2945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750946146459051, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146459424, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146460174, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146460315, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146460622, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146460933, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146461099, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-process-l1-1-0.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146461243, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Client.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146461520, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HostFiltering.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146461641, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Abstractions.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146462655, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750946146459193, "dur": 4693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750946146463886, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750946146464005, "dur": 52168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146372786, "dur": 15231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146388031, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146388018, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5A63E73C35F67AF2.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750946146388106, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146388163, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5A63E73C35F67AF2.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750946146388434, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750946146388717, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750946146388801, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750946146389260, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146389366, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750946146389548, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146389608, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146390989, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146392067, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146393096, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146394512, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146395731, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146397129, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146398168, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146399299, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146399699, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146400656, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146400879, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146401491, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146401892, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146402964, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146403040, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146403374, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146403660, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146403977, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146404519, "dur": 51586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146459091, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146459369, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146459959, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146460174, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146460315, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146460755, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146460933, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146461049, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146461174, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146456107, "dur": 5191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750946146461299, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146461467, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750946146461676, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 32, "ts": 1750946146461776, "dur": 1853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146463632, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146463709, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750946146464602, "dur": 51542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750946146522323, "dur": 2965, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 61532, "tid": 356, "ts": 1750946146527206, "dur": 940, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 61532, "tid": 356, "ts": 1750946146528187, "dur": 7694, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 61532, "tid": 356, "ts": 1750946146526538, "dur": 9583, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
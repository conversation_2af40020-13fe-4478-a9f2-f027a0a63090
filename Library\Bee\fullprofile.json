{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 47468, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 47468, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 47468, "tid": 219, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 47468, "tid": 219, "ts": 1750902333788009, "dur": 442, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333790598, "dur": 538, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 47468, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 47468, "tid": 1, "ts": 1750902333620966, "dur": 3310, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750902333624279, "dur": 21166, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750902333645456, "dur": 22164, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333791138, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 47468, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333619792, "dur": 6440, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333626233, "dur": 156561, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333626875, "dur": 1446, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333628324, "dur": 861, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629188, "dur": 210, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629401, "dur": 8, "ph": "X", "name": "ProcessMessages 20504", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629410, "dur": 28, "ph": "X", "name": "ReadAsync 20504", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629440, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629442, "dur": 18, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629462, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629482, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629500, "dur": 15, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629516, "dur": 19, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629537, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629553, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629571, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629588, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629613, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629633, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629653, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629670, "dur": 16, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629688, "dur": 15, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629705, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629729, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629731, "dur": 14, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629746, "dur": 13, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629761, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629781, "dur": 16, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629798, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629816, "dur": 15, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629833, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629854, "dur": 15, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629870, "dur": 15, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629887, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629903, "dur": 14, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629918, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629934, "dur": 17, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629952, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629969, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333629985, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630002, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630023, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630039, "dur": 14, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630054, "dur": 18, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630077, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630101, "dur": 17, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630120, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630138, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630157, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630177, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630195, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630212, "dur": 14, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630229, "dur": 20, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630251, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630270, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630287, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630305, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630322, "dur": 16, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630339, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630356, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630373, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630390, "dur": 13, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630404, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630420, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630436, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630454, "dur": 15, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630471, "dur": 15, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630487, "dur": 15, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630503, "dur": 13, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630518, "dur": 12, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630531, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630547, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630563, "dur": 15, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630580, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630596, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630612, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630627, "dur": 14, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630643, "dur": 23, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630667, "dur": 17, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630686, "dur": 16, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630703, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630720, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630735, "dur": 14, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630751, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630767, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630787, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630807, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630823, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630839, "dur": 19, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630860, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630877, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630894, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630911, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630929, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630952, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630977, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333630996, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631015, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631036, "dur": 20, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631058, "dur": 16, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631076, "dur": 16, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631094, "dur": 62, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631162, "dur": 3, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631166, "dur": 69, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631237, "dur": 1, "ph": "X", "name": "ProcessMessages 1630", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631239, "dur": 27, "ph": "X", "name": "ReadAsync 1630", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631268, "dur": 44, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631314, "dur": 39, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631357, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631360, "dur": 35, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631397, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631398, "dur": 30, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631431, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631433, "dur": 20, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631455, "dur": 25, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631481, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631501, "dur": 24, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631527, "dur": 19, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631547, "dur": 15, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631564, "dur": 19, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631587, "dur": 18, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631606, "dur": 34, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631645, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631648, "dur": 54, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631704, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631706, "dur": 23, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631732, "dur": 141, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631874, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631895, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631914, "dur": 15, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631930, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631947, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631964, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333631985, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632003, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632025, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632047, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632070, "dur": 16, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632088, "dur": 14, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632103, "dur": 15, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632120, "dur": 16, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632137, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632153, "dur": 15, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632170, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632188, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632203, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632219, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632240, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632257, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632274, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632290, "dur": 33, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632324, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632350, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632368, "dur": 59, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632429, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632448, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632449, "dur": 17, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632468, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632486, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632502, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632523, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632541, "dur": 14, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632557, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632573, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632590, "dur": 25, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632617, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632632, "dur": 15, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632648, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632664, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632679, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632695, "dur": 18, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632715, "dur": 15, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632732, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632748, "dur": 14, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632763, "dur": 14, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632779, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632796, "dur": 10, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632808, "dur": 14, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632824, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632844, "dur": 15, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632860, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632875, "dur": 15, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632892, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632909, "dur": 15, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632925, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632943, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632958, "dur": 19, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632978, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632979, "dur": 14, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333632995, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633018, "dur": 29, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633049, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633064, "dur": 14, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633080, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633097, "dur": 18, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633117, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633139, "dur": 15, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633156, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633175, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633191, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633208, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633225, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633246, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633266, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633285, "dur": 41, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633327, "dur": 24, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633353, "dur": 15, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633370, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633389, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633406, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633428, "dur": 17, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633446, "dur": 15, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633462, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633478, "dur": 13, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633492, "dur": 14, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633510, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633532, "dur": 15, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633550, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633571, "dur": 15, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633587, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633605, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633621, "dur": 14, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633637, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633658, "dur": 15, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633675, "dur": 11, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633687, "dur": 14, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633703, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633719, "dur": 15, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633735, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633757, "dur": 27, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633786, "dur": 13, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633801, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633819, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633837, "dur": 15, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633853, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633871, "dur": 27, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633899, "dur": 14, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633916, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633932, "dur": 15, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633948, "dur": 17, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633967, "dur": 18, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333633986, "dur": 14, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634002, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634018, "dur": 14, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634034, "dur": 16, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634053, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634071, "dur": 20, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634107, "dur": 18, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634127, "dur": 18, "ph": "X", "name": "ReadAsync 1306", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634147, "dur": 14, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634163, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634183, "dur": 15, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634200, "dur": 15, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634217, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634233, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634250, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634268, "dur": 16, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634286, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634304, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634320, "dur": 18, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634341, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634359, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634378, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634397, "dur": 74, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634473, "dur": 28, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634502, "dur": 1, "ph": "X", "name": "ProcessMessages 2134", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634504, "dur": 17, "ph": "X", "name": "ReadAsync 2134", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634523, "dur": 14, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634538, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634556, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634573, "dur": 15, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634589, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634649, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634691, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634696, "dur": 65, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634764, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634768, "dur": 58, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634830, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634833, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634911, "dur": 1, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634913, "dur": 30, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333634945, "dur": 422, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635372, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635375, "dur": 118, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635495, "dur": 2, "ph": "X", "name": "ProcessMessages 3737", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635499, "dur": 44, "ph": "X", "name": "ReadAsync 3737", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635546, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635547, "dur": 36, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635585, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635588, "dur": 58, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635649, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635693, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635696, "dur": 96, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635794, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635824, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635825, "dur": 41, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635869, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635922, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635937, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635956, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333635975, "dur": 53, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636029, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636048, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636066, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636083, "dur": 53, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636138, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636160, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636180, "dur": 17, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636199, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636246, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636277, "dur": 33, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636314, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636352, "dur": 42, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636395, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636416, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636443, "dur": 13, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636458, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636526, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636544, "dur": 32, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636578, "dur": 19, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636598, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636668, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636696, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636718, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636721, "dur": 58, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636781, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636799, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636816, "dur": 16, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636834, "dur": 15, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636850, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636902, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636918, "dur": 15, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636935, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333636951, "dur": 53, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637005, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637021, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637039, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637057, "dur": 53, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637111, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637131, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637149, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637166, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637183, "dur": 65, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637251, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637283, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637308, "dur": 39, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637350, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637355, "dur": 47, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637404, "dur": 2, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637407, "dur": 22, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637432, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637475, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637496, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637515, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637532, "dur": 72, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637607, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637631, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637651, "dur": 30, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637682, "dur": 26, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637713, "dur": 47, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637763, "dur": 34, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637807, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637810, "dur": 32, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637845, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637847, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637900, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637960, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333637962, "dur": 39, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638004, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638006, "dur": 105, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638113, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638165, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638167, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638198, "dur": 34, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638234, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638237, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638303, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638367, "dur": 2, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638369, "dur": 29, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638401, "dur": 44, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638448, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638486, "dur": 25, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638514, "dur": 42, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638560, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638584, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638609, "dur": 20, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638633, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638673, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638703, "dur": 19, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638725, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638750, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638799, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638824, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638845, "dur": 18, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638866, "dur": 52, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638920, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638943, "dur": 38, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638983, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333638985, "dur": 49, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639035, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639059, "dur": 20, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639083, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639102, "dur": 40, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639146, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639166, "dur": 5, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639171, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639191, "dur": 57, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639254, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639298, "dur": 18, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639318, "dur": 36, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639356, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639376, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639396, "dur": 62, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639465, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639534, "dur": 1, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639536, "dur": 30, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639568, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639605, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639657, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639659, "dur": 39, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639701, "dur": 40, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639742, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639784, "dur": 50, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639835, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639837, "dur": 25, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639864, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639918, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639951, "dur": 22, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639974, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333639976, "dur": 32, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640012, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640018, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640055, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640057, "dur": 63, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640123, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640152, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640189, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640192, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640233, "dur": 15, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640250, "dur": 24, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640277, "dur": 54, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640333, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640356, "dur": 23, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640382, "dur": 55, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640439, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640467, "dur": 19, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640488, "dur": 54, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640544, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640568, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640586, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640602, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640619, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640639, "dur": 14, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640655, "dur": 14, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640671, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640690, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640748, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640767, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640784, "dur": 26, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640813, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640859, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640878, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640896, "dur": 15, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640913, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640930, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640947, "dur": 32, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640981, "dur": 14, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333640996, "dur": 15, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641013, "dur": 10, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641025, "dur": 60, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641086, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641102, "dur": 34, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641137, "dur": 36, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641175, "dur": 18, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641194, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641213, "dur": 14, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641229, "dur": 13, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641244, "dur": 14, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641259, "dur": 53, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641314, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641332, "dur": 141, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641477, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641519, "dur": 242, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641764, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641818, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641821, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641871, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641875, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641919, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641922, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641969, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333641971, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642008, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642010, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642047, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642049, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642096, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642099, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642144, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642184, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642187, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642239, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642243, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642274, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642276, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642319, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642321, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642361, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642364, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642412, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642414, "dur": 51, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642468, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642470, "dur": 45, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642517, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642521, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642573, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642576, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642625, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642627, "dur": 52, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642682, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642684, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642737, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642740, "dur": 42, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642786, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642789, "dur": 39, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642830, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642832, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642877, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642879, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642922, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642924, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642973, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333642975, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643014, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643017, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643064, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643066, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643119, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643120, "dur": 27, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643148, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643149, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643181, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643230, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643231, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643279, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643281, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643324, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643326, "dur": 37, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643365, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643369, "dur": 43, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643412, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643414, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643452, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643454, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643493, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643496, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643538, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643540, "dur": 111, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643654, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643656, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643710, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643714, "dur": 46, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643763, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643766, "dur": 31, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643800, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643802, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643840, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643865, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643885, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643901, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643920, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643949, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333643966, "dur": 10797, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333654770, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333654773, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333654827, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333654829, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655016, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655059, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655061, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655120, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655151, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655216, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655241, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655683, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655712, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655774, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655802, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655836, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333655853, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656142, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656167, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656203, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656454, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656500, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656502, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656540, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656542, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656575, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656577, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656617, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656619, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656659, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656698, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656700, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656739, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656742, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333656797, "dur": 421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657222, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657253, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657281, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657328, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657330, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657375, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657377, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657430, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657432, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657467, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657507, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657544, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657546, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657587, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657589, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657631, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657633, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657676, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657678, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657716, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657747, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657749, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657779, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657797, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657813, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657904, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657924, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333657942, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658033, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658051, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658100, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658116, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658138, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658195, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658235, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658262, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658289, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658317, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658319, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658348, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658350, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658374, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658394, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658484, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658503, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658590, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658627, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658732, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658753, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658771, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658883, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658904, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333658927, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659079, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659111, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659137, "dur": 419, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659558, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659600, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659632, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659633, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659663, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659665, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659739, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333659757, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660154, "dur": 186, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660341, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660344, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660374, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660418, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660451, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660480, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660659, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660695, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660834, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660861, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660887, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660909, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333660972, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661005, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661062, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661085, "dur": 238, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661325, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661351, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661378, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661412, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661439, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661457, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661459, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661499, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661525, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661544, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661656, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661680, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661763, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661783, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333661800, "dur": 430, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333662233, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333662252, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333662253, "dur": 44870, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333707127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333707130, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333707258, "dur": 1760, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333709021, "dur": 6585, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333715610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333715612, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333715631, "dur": 370, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716005, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716035, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716107, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716153, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716326, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716355, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716514, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716517, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716565, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716567, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716625, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716668, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716670, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716760, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716797, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716849, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716890, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716936, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716969, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333716972, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717018, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717020, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717054, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717105, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717140, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717182, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717231, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717233, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717267, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717311, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717314, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717363, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717366, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717407, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717409, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717441, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717493, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717539, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717542, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717582, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717584, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717634, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717635, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717674, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717676, "dur": 51, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717730, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717732, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717773, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717775, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717819, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717859, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333717984, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333718020, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333718049, "dur": 1100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719153, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719186, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719236, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719468, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719503, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719540, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719573, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719825, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719879, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719881, "dur": 26, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719911, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719945, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333719978, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333720008, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333720027, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333720048, "dur": 48491, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333768543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333768545, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333768579, "dur": 3983, "ph": "X", "name": "ProcessMessages 2414", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333772564, "dur": 1467, "ph": "X", "name": "ReadAsync 2414", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333774033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333774035, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333774058, "dur": 179, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 47468, "tid": 12884901888, "ts": 1750902333774239, "dur": 8506, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333791148, "dur": 682, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 47468, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 47468, "tid": 8589934592, "ts": 1750902333617992, "dur": 49673, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 47468, "tid": 8589934592, "ts": 1750902333667669, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 47468, "tid": 8589934592, "ts": 1750902333667672, "dur": 753, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333791832, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 47468, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 47468, "tid": 4294967296, "ts": 1750902333606494, "dur": 176979, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 47468, "tid": 4294967296, "ts": 1750902333608953, "dur": 5195, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 47468, "tid": 4294967296, "ts": 1750902333783483, "dur": 2775, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 47468, "tid": 4294967296, "ts": 1750902333785167, "dur": 69, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 47468, "tid": 4294967296, "ts": 1750902333786305, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333791837, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750902333625128, "dur": 1201, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333626336, "dur": 569, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333627033, "dur": 601, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333628406, "dur": 293, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BC49A6886E1E88EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750902333629630, "dur": 557, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750902333638484, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750902333627648, "dur": 14460, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333642118, "dur": 130106, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333772225, "dur": 512, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333774727, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333774814, "dur": 1202, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750902333627362, "dur": 14766, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333642152, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333642302, "dur": 606, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1750902333642246, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750902333642978, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333643135, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750902333643329, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750902333643597, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750902333643795, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750902333644319, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333644450, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333645824, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333646969, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333647991, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333649568, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333650603, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333651645, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333652797, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333653799, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333654847, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333655598, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333655885, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333656554, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333657032, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750902333657227, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750902333658291, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333658467, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750902333658596, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750902333658957, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333659066, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333659798, "dur": 52725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333715116, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333716276, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717043, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333712541, "dur": 4686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717228, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333717387, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717451, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717750, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717969, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333718108, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333718310, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750902333717326, "dur": 3021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750902333720347, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750902333720451, "dur": 51764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333627710, "dur": 14639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333642402, "dur": 704, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1750902333642356, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333643153, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333643588, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750902333643679, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750902333643826, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750902333643977, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750902333644064, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750902333644338, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333644436, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750902333644504, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333645697, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333648437, "dur": 925, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\AssetPostProcessors\\ShaderGraphMaterialsUpdater.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750902333646711, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333649425, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333650440, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333651545, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333652558, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333653657, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333654664, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333655523, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333655905, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333656588, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333657042, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333657439, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333657373, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333658047, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333658278, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333658358, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333658974, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333659753, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333660053, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333660161, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333661049, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333661138, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333661225, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333661764, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333661839, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333662218, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333662457, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750902333662544, "dur": 50085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333716248, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333716609, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333712637, "dur": 4685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333717323, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333717584, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333717751, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333718077, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333718468, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750902333717532, "dur": 2976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750902333720512, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750902333720656, "dur": 51572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333627381, "dur": 14761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333642152, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333642300, "dur": 596, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1750902333642248, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750902333642898, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333642980, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333643231, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750902333643439, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750902333643776, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333643892, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750902333643979, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750902333644080, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750902333644162, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750902333644312, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333644454, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333645638, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333646666, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333647787, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333649212, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333650264, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333651375, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333652407, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333653472, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333654520, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333655560, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333655903, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333656583, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333657053, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750902333657296, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333657439, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333657359, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750902333658029, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333658178, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333658247, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333658433, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333658991, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333659775, "dur": 52782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333716350, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333716854, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333717283, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333717355, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333717417, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333712559, "dur": 5056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750902333717616, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333717817, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333717885, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333717992, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333718092, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333718232, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750902333718292, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333718472, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750902333720667, "dur": 51596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333627416, "dur": 14735, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333642169, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750902333642243, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1750902333642158, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750902333642837, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333642944, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333643140, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750902333643257, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750902333643517, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750902333643755, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333643847, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750902333644060, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750902333644148, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750902333644229, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750902333644443, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333645996, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333647031, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333648019, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333649505, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333650521, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333651571, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333652632, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333653730, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333654783, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333655564, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333655892, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333656569, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333657044, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750902333658112, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.27\\Rider\\Editor\\UnitTesting\\CallbackInitializer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750902333657475, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750902333658255, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333658385, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333658975, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333659752, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750902333659863, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750902333660153, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333660248, "dur": 52304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333714314, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750902333716271, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750902333716922, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750902333717284, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750902333712553, "dur": 4866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750902333717419, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333717785, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333717883, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333718083, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333718287, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333718443, "dur": 2168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750902333720664, "dur": 51576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333627456, "dur": 14714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333642236, "dur": 519, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1750902333642179, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750902333642879, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333642877, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_772DB31288B13074.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750902333642981, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333643118, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333643182, "dur": 475, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750902333643698, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750902333643821, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333643873, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750902333643996, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750902333644058, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333644111, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750902333644373, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333644477, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750902333644596, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333645980, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333647013, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333648012, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333649607, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333650620, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333651691, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333652895, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333654007, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333655200, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333655857, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333656560, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333657064, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333658234, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333658355, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333658472, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333659012, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333659793, "dur": 52728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333713300, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333715222, "dur": 555, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333716248, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333716609, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333716705, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333716922, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333717285, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333717451, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333717584, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333717806, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750902333712525, "dur": 5429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750902333717955, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333718073, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333718317, "dur": 1614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750902333719978, "dur": 52238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333627481, "dur": 14704, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333642201, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333642256, "dur": 545, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1750902333642194, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750902333642944, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333643165, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333643225, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750902333643500, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750902333643979, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750902333644329, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333644522, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750902333644612, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333645797, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333646892, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333647955, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333649394, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333650414, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333651490, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333652543, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333653640, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333654921, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333655702, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333655896, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333656596, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333657072, "dur": 1186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333658259, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333658377, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333658962, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750902333659108, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750902333659433, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333659522, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333659805, "dur": 52730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333716247, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333716350, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333712543, "dur": 4124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750902333716668, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333717286, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333717417, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333717525, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333717700, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333717834, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333717931, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333718063, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333718287, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750902333716786, "dur": 3381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750902333720169, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750902333720282, "dur": 51948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333627931, "dur": 14459, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333642441, "dur": 743, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1750902333642396, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333643223, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333643516, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750902333644057, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750902333644155, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750902333644253, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750902333644347, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333644568, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333645902, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333646955, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333647984, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333649478, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333650492, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333651567, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333652636, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333654159, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333654928, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333655696, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333655858, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333656530, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333657059, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333658235, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333658361, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333658960, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333659105, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750902333659802, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333659881, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750902333660180, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333660268, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333660380, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750902333660856, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333660956, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750902333661041, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750902333661432, "dur": 51113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333716248, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750902333716757, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750902333717452, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750902333717835, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750902333718133, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750902333712565, "dur": 5712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750902333718277, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333718468, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750902333718467, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750902333718565, "dur": 2148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750902333720765, "dur": 51501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333627516, "dur": 14693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333642233, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333642297, "dur": 573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1750902333642219, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750902333642964, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333643095, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB998ABA4D09E52A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750902333643325, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750902333643447, "dur": 1096, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750902333644544, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333645867, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333646922, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333648331, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Nodes\\Math\\Range\\ClampNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750902333647937, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333649615, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333650638, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333651710, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333652904, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333653886, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333654933, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333655635, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333655863, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333656592, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333657054, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750902333657394, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333657458, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750902333658420, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333658517, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333659003, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333659786, "dur": 52739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333717044, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333712528, "dur": 4604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750902333717132, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333717285, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333717451, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333717751, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333717932, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333718108, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333718745, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750902333717284, "dur": 3324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750902333720609, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750902333720755, "dur": 51502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333628028, "dur": 14384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333642465, "dur": 770, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1750902333642420, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD6D4C7F30E8FD4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750902333643389, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1750902333643878, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1750902333644133, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750902333644200, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750902333644341, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333644548, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333645902, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333646919, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333648014, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333649575, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333650586, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333651638, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333652971, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333654282, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333654456, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333655419, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333655892, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333656567, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333657071, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333658241, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333658442, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333658994, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333659779, "dur": 52729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333716608, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750902333712518, "dur": 4363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750902333716882, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333717283, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750902333717451, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750902333717671, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750902333717855, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750902333717055, "dur": 3341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750902333720397, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750902333720514, "dur": 51722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333628069, "dur": 14358, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333642456, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333642438, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_44E9AA5F4E926643.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333642957, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333643168, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333643236, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750902333643442, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750902333643584, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644127, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644269, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644427, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644482, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644541, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644613, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644758, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333644813, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645031, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645098, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645206, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645346, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645443, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645647, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645699, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645754, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645805, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333645919, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646214, "dur": 412, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646645, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646700, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646781, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646841, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333646939, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647036, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647157, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647217, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647274, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647334, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647419, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647488, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647566, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647641, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647762, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647836, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333647919, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648007, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648082, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648177, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648269, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648326, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648387, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648460, "dur": 498, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333648993, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333649174, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333649232, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333649384, "dur": 429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333649814, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333649875, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650123, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650219, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650324, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650376, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650752, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650823, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333650930, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651029, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651129, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651376, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651472, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651570, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651653, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651738, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651805, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651866, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333651934, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333652072, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333652130, "dur": 1011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653152, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653268, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653396, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653450, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653518, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653667, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653770, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653923, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333653979, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654081, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654200, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654255, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654386, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654438, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654538, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654681, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654813, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333654972, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333655092, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750902333655338, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333643716, "dur": 11692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333655409, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333655560, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333655861, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333656535, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333657022, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333657244, "dur": 1568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333658813, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333658998, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333659121, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333659966, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333660049, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333660150, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333660993, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333661082, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750902333661191, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333661542, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333661629, "dur": 50964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333714538, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333715717, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333715835, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333716244, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717043, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717242, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717452, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717585, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717854, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333717968, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718064, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718133, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718231, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718309, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333712595, "dur": 5789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718468, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718467, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750902333718524, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333718579, "dur": 2137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750902333720768, "dur": 51501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333628104, "dur": 14337, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333642447, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_A45827FC07386469.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333642537, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333642953, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6272BEC5630D4174.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333643011, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333643008, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333643176, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333643525, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750902333643792, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750902333644042, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750902333644308, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333644509, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333645928, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333646983, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333648611, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Attributes\\SubTargetFilterAttribute.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750902333648043, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333649883, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333650975, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333651956, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333653113, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333654142, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333655140, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333655233, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333655878, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333656546, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333657038, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333657429, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750902333658266, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333658422, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750902333658537, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750902333658932, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333659025, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333659813, "dur": 52730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333716116, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333716248, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333716610, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333716850, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717132, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717241, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717419, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717585, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717700, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717836, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333717932, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750902333712551, "dur": 5624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750902333718176, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333718294, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333718583, "dur": 2146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750902333720730, "dur": 51531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333627582, "dur": 14693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333642298, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333642369, "dur": 683, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1750902333642288, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750902333643077, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750902333643285, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750902333643512, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750902333643794, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750902333644070, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333644207, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750902333644287, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333644440, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750902333644518, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333645777, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333646815, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333647822, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333649238, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333651367, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_0_0.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750902333650316, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333652105, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333653525, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333654574, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333655494, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333655898, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333656579, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333657040, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750902333657284, "dur": 1336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750902333658620, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333658721, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750902333658821, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750902333659208, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333659269, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333659801, "dur": 52727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333715221, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333716244, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333716351, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333716921, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717043, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717241, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717419, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717585, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717861, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333717968, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333718230, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750902333712530, "dur": 5924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750902333718454, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333718525, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333718589, "dur": 2159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750902333720748, "dur": 51536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333627596, "dur": 14695, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333642349, "dur": 753, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1750902333642299, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750902333643104, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333643349, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750902333643499, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750902333643652, "dur": 684, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750902333644337, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750902333644392, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333644532, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750902333644671, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333645942, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333647090, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333648156, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333649891, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333650997, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333652001, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333653403, "dur": 899, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\SetSamplingFrequencyCommand.cs"}}, {"pid": 12345, "tid": 13, "ts": 1750902333653120, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333655047, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333655304, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333655879, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333656565, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333657067, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333658356, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333658974, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333659029, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333659794, "dur": 52744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333714416, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Antiforgery.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333715513, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333716249, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333716703, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333716922, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333717285, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333717419, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333717700, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333717836, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750902333712548, "dur": 5371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750902333717919, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333718202, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333718285, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333718491, "dur": 2221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750902333720759, "dur": 51512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333627625, "dur": 14676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333642347, "dur": 778, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1750902333642307, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750902333643173, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750902333643454, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333643515, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750902333643616, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750902333643762, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750902333644243, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750902333644356, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333644559, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333645908, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333646947, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333648079, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333649743, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333650828, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333651825, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333652927, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333653999, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333655043, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333655249, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333655895, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333656578, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333657077, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333658253, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333658395, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333658981, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333659777, "dur": 52725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333716248, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750902333716851, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750902333712503, "dur": 4845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750902333717349, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333717752, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750902333717603, "dur": 3098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750902333720701, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750902333720818, "dur": 51431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333627646, "dur": 14668, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333642365, "dur": 739, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1750902333642320, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AAB6357F3297AC29.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333643105, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333643172, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AAB6357F3297AC29.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333643583, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333643693, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644048, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644127, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644279, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644426, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644489, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644552, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644617, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333644721, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645043, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645095, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645200, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645358, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645443, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645575, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645675, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645728, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645820, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333645923, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646032, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646196, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646264, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646360, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646418, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646607, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646658, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646734, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646788, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333646938, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647037, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647116, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647172, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647228, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647288, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647341, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647418, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647489, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647565, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647641, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647712, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647765, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647838, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333647920, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648050, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648121, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648172, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648247, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648331, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648391, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333648460, "dur": 512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333649066, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333649169, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333649221, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333649335, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333649393, "dur": 973, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333650389, "dur": 420, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333650857, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333650916, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651015, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651121, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651301, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651383, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651469, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651574, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651653, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651734, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651811, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651871, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333651935, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652087, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652163, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652318, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652374, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652429, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652490, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652643, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652801, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333652901, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653039, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653138, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653201, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653314, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653448, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653503, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653612, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653717, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653769, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653920, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333653976, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654027, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654156, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654277, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654383, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654445, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654549, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654680, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654812, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654870, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333654969, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333655072, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333655203, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333655313, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750902333643874, "dur": 11868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333655925, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333656004, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333656553, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333656622, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333656869, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333657075, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333657437, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333658056, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333657514, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333658300, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333658463, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750902333658584, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333658974, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750902333659527, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333659910, "dur": 47999, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750902333712494, "dur": 3711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333716207, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333716757, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333717241, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333717451, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333717805, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750902333716367, "dur": 3490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750902333719858, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750902333719946, "dur": 52274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333627673, "dur": 14654, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333642372, "dur": 684, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1750902333642333, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_661C6E6134F0042E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750902333643133, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333643332, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750902333643469, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333643629, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333643747, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333644126, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333644182, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750902333644281, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333644494, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333645778, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333647032, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333648170, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333650684, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Framework\\Events\\CustomEvent.cs"}}, {"pid": 12345, "tid": 16, "ts": 1750902333649605, "dur": 2070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333651675, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333652957, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333653942, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333654989, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333655440, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333655894, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333656576, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333657030, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750902333657219, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333657314, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750902333657884, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333658088, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333658281, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333658413, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333658983, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333659767, "dur": 9440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333669208, "dur": 2873, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333672082, "dur": 40485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333713832, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333714972, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333715458, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebClient.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333716249, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333716608, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333716757, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333716923, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717236, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717452, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717584, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717698, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717836, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333717968, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333718085, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750902333712568, "dur": 5633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750902333718204, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333718302, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333718364, "dur": 2063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750902333720475, "dur": 51805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333627694, "dur": 14644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333642386, "dur": 697, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1750902333642344, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8DB4C22B24A5A2EB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750902333643177, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750902333643328, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1750902333643747, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1750902333643833, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1750902333644006, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750902333644110, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750902333644174, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750902333644279, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333644436, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750902333644492, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333645718, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333646852, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333647975, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333649621, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333650677, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333651875, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333652980, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333654047, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333655087, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333655216, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333655856, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333656532, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333657025, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750902333657354, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333657234, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750902333658230, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333658393, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333658978, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333659765, "dur": 2455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333662306, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750902333662546, "dur": 50420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333713245, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333715482, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordbi.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333716248, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333716854, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333717284, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333717387, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333717698, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333717834, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333717931, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333718228, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750902333712968, "dur": 5363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750902333718332, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333718427, "dur": 2085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333720517, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750902333720618, "dur": 51604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333627434, "dur": 14726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333642176, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333642254, "dur": 592, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1750902333642167, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750902333642991, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333642989, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750902333643180, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750902333643251, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750902333643436, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750902333643633, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750902333644109, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750902333644196, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750902333644378, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333644525, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333646049, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333647119, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333649057, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333650105, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333651195, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333652241, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333653430, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333654492, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333655618, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333655882, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333656559, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333657060, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333658236, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333658360, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333659017, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333659790, "dur": 52870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333712872, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333716248, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333716608, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333716758, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333716922, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717043, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717355, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717419, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717585, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717648, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717805, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333717968, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750902333712662, "dur": 5386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750902333718048, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333718260, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333718323, "dur": 1930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333720257, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750902333720320, "dur": 51905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333627721, "dur": 14639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333642403, "dur": 730, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1750902333642366, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_E966188727BA7937.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750902333643169, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_E966188727BA7937.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750902333643330, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1750902333643515, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750902333643725, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750902333643879, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750902333644278, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333644506, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6591615775254759062.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750902333644736, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333645942, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333646987, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333647978, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333649438, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333650466, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333651539, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333652558, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333653631, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333655036, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333655140, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333655261, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333655880, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333656545, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333657065, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750902333657919, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333658056, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333657375, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750902333658377, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333658469, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750902333658598, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750902333659396, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333659807, "dur": 52748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333715230, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.Local.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333715544, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333716154, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333716254, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333716608, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333716704, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333716922, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333717284, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333717451, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333712556, "dur": 5016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750902333717572, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333717699, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333717833, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333718063, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333718230, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333718787, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750902333717663, "dur": 2937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750902333720601, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750902333720775, "dur": 51495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333627818, "dur": 14551, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333642416, "dur": 744, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1750902333642375, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750902333643244, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750902333643632, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750902333643777, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333644195, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750902333644288, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333644586, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333645904, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333647057, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Flow\\Options\\UnitBase.cs"}}, {"pid": 12345, "tid": 20, "ts": 1750902333648598, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IsVariableDefinedUnitOption.cs"}}, {"pid": 12345, "tid": 20, "ts": 1750902333646919, "dur": 2432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333649352, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333650377, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333651620, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333652640, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333654090, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333655096, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333655249, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333655871, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333656536, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333657070, "dur": 1180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333658250, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333658414, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333658998, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333659783, "dur": 52728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333714840, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333716248, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333716350, "dur": 280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333717043, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333712517, "dur": 4744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750902333717262, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333717451, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333717836, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333717968, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333718310, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750902333717402, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750902333720596, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750902333720762, "dur": 51500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333627892, "dur": 14488, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333642392, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333642444, "dur": 767, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1750902333642387, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8E4D74A20FBDC909.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750902333643256, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8E4D74A20FBDC909.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750902333643733, "dur": 411, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750902333644145, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750902333644247, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750902333644331, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333644472, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750902333644661, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333645875, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333647184, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333648443, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Runtime\\Passes\\DepthOnlyPass.cs"}}, {"pid": 12345, "tid": 21, "ts": 1750902333648282, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333649989, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333651154, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333652231, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333653549, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333654588, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333655683, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333655874, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333656540, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333657035, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750902333657314, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333657411, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750902333657898, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333658235, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333658383, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333658988, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333659772, "dur": 52726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333716350, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333716704, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333717284, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333712512, "dur": 4857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750902333717369, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333717452, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333717835, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333717931, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333718287, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333718360, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750902333717424, "dur": 3214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750902333720638, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750902333720730, "dur": 51560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333627501, "dur": 14696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333642260, "dur": 564, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1750902333642204, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750902333642932, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333643327, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750902333643530, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750902333643696, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750902333643846, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750902333644132, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750902333644390, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333644555, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333645853, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333646867, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333648453, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Graphs\\ParentGroupChange.cs"}}, {"pid": 12345, "tid": 22, "ts": 1750902333648016, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333649581, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333650623, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333651720, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333653014, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333654115, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333655276, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333655876, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333656543, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333657091, "dur": 1159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333658251, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333658403, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333659004, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333659786, "dur": 52733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333716255, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333716448, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333716609, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333716920, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333717241, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333717357, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333712529, "dur": 5204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750902333717734, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333717805, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333717804, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750902333717896, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333718090, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333718189, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1750902333718315, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750902333718809, "dur": 53450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333627969, "dur": 14431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333642449, "dur": 769, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1750902333642406, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_767F45CA866D1729.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750902333643245, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_767F45CA866D1729.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750902333643384, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750902333643522, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750902333643601, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750902333643736, "dur": 416, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1750902333644152, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750902333644348, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333644442, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750902333646034, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333644624, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333646821, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333647931, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333649446, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333650686, "dur": 1299, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Interface\\Fonts\\FontWeight.cs"}}, {"pid": 12345, "tid": 23, "ts": 1750902333650475, "dur": 2386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333653362, "dur": 781, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\WebGL\\WebGLGamepad.cs"}}, {"pid": 12345, "tid": 23, "ts": 1750902333652861, "dur": 2163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333655053, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333655254, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333655872, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333656538, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333657074, "dur": 1188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333658262, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333658367, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333658984, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333659768, "dur": 12318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333672086, "dur": 40467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333712916, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333714456, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333716113, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333716253, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333716609, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333716757, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333717387, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333717451, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333717698, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750902333712554, "dur": 5275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750902333717830, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333718029, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333718189, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1750902333718285, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333718368, "dur": 2079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750902333720500, "dur": 51731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333627530, "dur": 14692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333642249, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333642327, "dur": 627, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1750902333642233, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333642955, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333643076, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333643265, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750902333643358, "dur": 439, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750902333643834, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1750902333643960, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750902333644090, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750902333644310, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333644453, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333645738, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333646730, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333648450, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Drawing\\Colors\\PrecisionColors.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750902333647803, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333649901, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333650958, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333651972, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333653074, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333654084, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333655119, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333655308, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333655886, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333656560, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333657028, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333657199, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333657440, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333657290, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750902333657874, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333658107, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333658380, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333658462, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750902333658837, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333659034, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750902333659143, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750902333659455, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333659541, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333659816, "dur": 52724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.Routing.dll"}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Metadata.dll"}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 24, "ts": ****************, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717284, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717356, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717452, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717752, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717874, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333717967, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750902333712541, "dur": 5495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750902333718037, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333718309, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333718396, "dur": 2093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750902333720489, "dur": 51729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333627544, "dur": 14693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333642258, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333642322, "dur": 614, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 25, "ts": 1750902333642244, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0CA797179878B1A6.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750902333642937, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333643128, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333643212, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_13EB1DAF647B9A69.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750902333643459, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333643511, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750902333644003, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750902333644107, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750902333644186, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750902333644285, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750902333644339, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333644462, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333645662, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333646675, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333647705, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333649180, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333650233, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333651296, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333652340, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333653697, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333654809, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333655574, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333655889, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333656565, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333657054, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750902333657360, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333657416, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750902333657900, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333658075, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333658248, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333658422, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333658987, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333659771, "dur": 52734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333716349, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333712506, "dur": 4051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750902333716557, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333717284, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333717387, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333717648, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333717836, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333717931, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333718230, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750902333716774, "dur": 3591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750902333720365, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750902333720517, "dur": 51734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333627560, "dur": 14690, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333642271, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333642330, "dur": 647, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1750902333642261, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_816435D8968F6544.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750902333642978, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333643125, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333643181, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1750902333643434, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750902333643605, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750902333643754, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333643810, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333644126, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750902333644205, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750902333644267, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333644507, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333646208, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333647253, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333648248, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333649724, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333650812, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333651843, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333653196, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333654279, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333654652, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333655579, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333655886, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333656555, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333657056, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750902333657443, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750902333658151, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333658237, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333658436, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333658992, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333659776, "dur": 52719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333713896, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333714129, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333715915, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333716070, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333716247, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717043, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717284, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717419, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717751, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717806, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333717968, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333718134, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333718286, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750902333712498, "dur": 6177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750902333718677, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333718791, "dur": 2006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750902333720820, "dur": 51471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333628147, "dur": 14304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333642540, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750902333642963, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333643144, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750902333643432, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750902333643529, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750902333643647, "dur": 578, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750902333644256, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333644502, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333646243, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\ShaderGraph\\AssetCallbacks\\CreateUnlitShaderGraph.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750902333646243, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333647814, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333649528, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333650541, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333651972, "dur": 1139, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionExitMessageListener.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750902333651604, "dur": 2171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333653775, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333654809, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333655654, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333655857, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333656566, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333657037, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750902333657229, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750902333658235, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333658379, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333658961, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750902333659110, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750902333659447, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333659550, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333659758, "dur": 2010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333661769, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750902333661861, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750902333662129, "dur": 50473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333712604, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333712792, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333716257, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333716757, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333716919, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333717284, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333712603, "dur": 4780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750902333717384, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333717751, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333717836, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333717968, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333718787, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750902333717591, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750902333720556, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750902333720669, "dur": 51574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333628171, "dur": 14299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333642568, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750902333642567, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333642838, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750902333642836, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4796D0C7D217714A.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333642985, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750902333642984, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333643176, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333643330, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750902333643774, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333643831, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1750902333644194, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750902333644368, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333644519, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333645965, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333647018, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333648013, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333649584, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333650619, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333651643, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333652789, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333653897, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333655100, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333655218, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333655900, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333656582, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333657043, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333657471, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333658158, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333658375, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333658974, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333659753, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333659861, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333660435, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333660520, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333661582, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750902333661679, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333661745, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333662217, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750902333662287, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333662577, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750902333663062, "dur": 106278, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750902333628212, "dur": 14269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333642489, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750902333643022, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333643178, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750902333643385, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750902333643517, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750902333643627, "dur": 449, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750902333644115, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750902333644313, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750902333644366, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333644571, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750902333644691, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333645830, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333646893, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333647943, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333649456, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333650453, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333651535, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333652589, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333653692, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333654761, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333655580, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333655881, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333656555, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333657066, "dur": 1173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333658239, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333658461, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333658997, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333659780, "dur": 52733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333716251, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333716350, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333716610, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333716921, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333712522, "dur": 4474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750902333716996, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333717284, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717356, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717452, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717751, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717860, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717931, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333718308, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750902333717074, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750902333720655, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750902333720799, "dur": 51498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333628236, "dur": 14257, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333642540, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333642971, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333643093, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750902333643458, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 30, "ts": 1750902333643686, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750902333644201, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750902333644261, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333644490, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333645899, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333646908, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333647886, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333649364, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333650640, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333651894, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333653031, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333654072, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333655211, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333655915, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333656576, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333657080, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333658237, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333658501, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333659002, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333659796, "dur": 52753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333714931, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Console.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333716261, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333716758, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717044, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717237, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717451, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717648, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717805, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333712551, "dur": 5379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750902333717931, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333718126, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750902333718285, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333718415, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333718473, "dur": 2250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750902333720776, "dur": 51491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333628285, "dur": 14220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333642517, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D19DF51C870C57DD.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750902333642952, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333643106, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333643168, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333643227, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750902333643492, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 31, "ts": 1750902333643955, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750902333644126, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750902333644493, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333645757, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333646739, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333648008, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333649803, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333650866, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333651941, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333653419, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333654495, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333655626, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333655859, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333656531, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333657024, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750902333657194, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333657392, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333657531, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333657920, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333657293, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750902333658489, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333658577, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750902333658691, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750902333659160, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333659799, "dur": 52717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333715287, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333716248, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333712518, "dur": 4183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750902333716702, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333716920, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717043, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717238, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717420, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717585, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717698, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717790, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333717932, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333718309, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750902333716866, "dur": 3769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750902333720636, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750902333720807, "dur": 51465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333628377, "dur": 14148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333642536, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333642526, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_4E866FA7B00374E5.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750902333642843, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333642842, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750902333642926, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333643002, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333643001, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750902333643132, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333643193, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333643431, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 32, "ts": 1750902333643875, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1750902333643971, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750902333644087, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750902333644285, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750902333644340, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333644574, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750902333644700, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333645915, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333646934, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333648463, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Nodes\\Math\\Vector\\TransformNode.cs"}}, {"pid": 12345, "tid": 32, "ts": 1750902333647915, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333649487, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333650516, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333651907, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333653312, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333654356, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333655407, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333655888, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333656563, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333657027, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750902333657367, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750902333657904, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333658070, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333658243, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333658452, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333659011, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333659789, "dur": 52742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333716248, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333716757, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333712533, "dur": 4704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750902333717238, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333717700, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333717751, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333717836, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333718391, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750902333717596, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750902333720423, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750902333720530, "dur": 51703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750902333779821, "dur": 3249, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 47468, "tid": 219, "ts": 1750902333792143, "dur": 1473, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 47468, "tid": 219, "ts": 1750902333793664, "dur": 1656, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 47468, "tid": 219, "ts": 1750902333789756, "dur": 6193, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
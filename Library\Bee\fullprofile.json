{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 10404, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 10404, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 10404, "tid": 109, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 10404, "tid": 109, "ts": 1750897250104609, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250104626, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 10404, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 10404, "tid": 1, "ts": 1750897249888163, "dur": 997, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10404, "tid": 1, "ts": 1750897249889162, "dur": 14409, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10404, "tid": 1, "ts": 1750897249903573, "dur": 18171, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250104630, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 10404, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249888140, "dur": 9604, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249897746, "dur": 206429, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249897753, "dur": 362, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249898120, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249898147, "dur": 6, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249898154, "dur": 2234, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900393, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900428, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900430, "dur": 27, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900459, "dur": 20, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900482, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900502, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900528, "dur": 17, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900547, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900568, "dur": 26, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900597, "dur": 19, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900618, "dur": 32, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900652, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900678, "dur": 31, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900713, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900739, "dur": 22, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900764, "dur": 15, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900781, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900798, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900822, "dur": 15, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900839, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900856, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900876, "dur": 16, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900894, "dur": 20, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900917, "dur": 29, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900948, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249900967, "dur": 31, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901000, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901021, "dur": 15, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901037, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901053, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901070, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901088, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901123, "dur": 25, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901151, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901175, "dur": 19, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901195, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901212, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901230, "dur": 14, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901246, "dur": 16, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901263, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901281, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901300, "dur": 15, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901316, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901335, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901358, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901375, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901392, "dur": 16, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901410, "dur": 16, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901427, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901445, "dur": 13, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901460, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901477, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901494, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901496, "dur": 22, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901520, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901540, "dur": 15, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901557, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901582, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901600, "dur": 15, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901617, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901635, "dur": 15, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901652, "dur": 22, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901675, "dur": 16, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901695, "dur": 36, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901734, "dur": 15, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901752, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901773, "dur": 17, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901791, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901809, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901826, "dur": 13, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901841, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901859, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901876, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901893, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901911, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901932, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901950, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901967, "dur": 23, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249901992, "dur": 15, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902009, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902028, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902043, "dur": 15, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902059, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902075, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902101, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902123, "dur": 15, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902139, "dur": 14, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902155, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902201, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902218, "dur": 17, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902236, "dur": 16, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902254, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902272, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902290, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902292, "dur": 15, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902308, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902332, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902353, "dur": 14, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902369, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902386, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902403, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902420, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902437, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902452, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902467, "dur": 14, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902482, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902484, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902506, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902507, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902525, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902545, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902561, "dur": 14, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902577, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902596, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902615, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902631, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902647, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902663, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902679, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902697, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902717, "dur": 15, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902734, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902754, "dur": 16, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902772, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902789, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902809, "dur": 15, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902825, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902855, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902876, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902878, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902901, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902922, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902942, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902959, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902982, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249902984, "dur": 35, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903022, "dur": 28, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903053, "dur": 17, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903072, "dur": 15, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903089, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903105, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903128, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903153, "dur": 18, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903174, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903194, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903217, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903219, "dur": 16, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903236, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903254, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903274, "dur": 15, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903292, "dur": 17, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903311, "dur": 17, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903329, "dur": 18, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903349, "dur": 15, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903365, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903367, "dur": 14, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903383, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903402, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903403, "dur": 18, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903423, "dur": 15, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903441, "dur": 16, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903459, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903478, "dur": 13, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903493, "dur": 14, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903508, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903525, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903555, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903573, "dur": 32, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903608, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903626, "dur": 17, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903645, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903663, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903682, "dur": 15, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903698, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903716, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903742, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903762, "dur": 15, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903780, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903799, "dur": 15, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903815, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903832, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903853, "dur": 17, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903872, "dur": 14, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903888, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903908, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903924, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903940, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903956, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903972, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249903991, "dur": 17, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904010, "dur": 16, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904027, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904044, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904060, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904078, "dur": 17, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904097, "dur": 14, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904113, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904136, "dur": 15, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904153, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904174, "dur": 15, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904191, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904210, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904231, "dur": 51, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904284, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904307, "dur": 16, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904324, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904340, "dur": 15, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904356, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904373, "dur": 16, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904391, "dur": 15, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904408, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904424, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904440, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904456, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904473, "dur": 17, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904492, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904509, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904525, "dur": 15, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904542, "dur": 105, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904649, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904669, "dur": 15, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904686, "dur": 18, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904705, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904721, "dur": 16, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904739, "dur": 15, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904756, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904774, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904791, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904813, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904834, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904855, "dur": 16, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904873, "dur": 15, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904889, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904906, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904927, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904947, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904967, "dur": 16, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904985, "dur": 13, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249904999, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905018, "dur": 15, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905035, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905052, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905073, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905094, "dur": 13, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905109, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905128, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905149, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905173, "dur": 16, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905190, "dur": 15, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905207, "dur": 15, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905224, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905240, "dur": 16, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905258, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905276, "dur": 15, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905293, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905312, "dur": 15, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905329, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905346, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905362, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905379, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905396, "dur": 15, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905412, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905430, "dur": 13, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905446, "dur": 15, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905463, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905480, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905498, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905514, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905531, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905547, "dur": 15, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905564, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905580, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905597, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905613, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905630, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905647, "dur": 12, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905661, "dur": 17, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905679, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905698, "dur": 15, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905715, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905739, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905755, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905772, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905789, "dur": 15, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905806, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905825, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905844, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905861, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905879, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905906, "dur": 16, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905924, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905941, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905958, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905973, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249905990, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906008, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906025, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906043, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906060, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906077, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906096, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906118, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906135, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906152, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906171, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906186, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906204, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906220, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906238, "dur": 15, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906255, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906273, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906289, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906306, "dur": 19, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906326, "dur": 15, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906343, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906361, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906379, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906396, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906414, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906433, "dur": 19, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906453, "dur": 15, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906470, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906486, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906502, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906526, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906546, "dur": 18, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906566, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906584, "dur": 16, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906602, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906621, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906643, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906661, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906680, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906700, "dur": 15, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906716, "dur": 16, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906733, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906750, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906769, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906787, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906806, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906824, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906843, "dur": 19, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906875, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906897, "dur": 15, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906914, "dur": 15, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906931, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906951, "dur": 16, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906969, "dur": 17, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249906988, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907005, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907022, "dur": 16, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907039, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907058, "dur": 16, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907076, "dur": 15, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907092, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907109, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907126, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907143, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907161, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907181, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907200, "dur": 18, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907220, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907240, "dur": 18, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907259, "dur": 16, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907277, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907295, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907312, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907330, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907346, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907363, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907380, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907396, "dur": 33, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907432, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907453, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907470, "dur": 14, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907486, "dur": 62, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907549, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907565, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907580, "dur": 14, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907595, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907597, "dur": 53, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907652, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907674, "dur": 19, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907694, "dur": 14, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907710, "dur": 60, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907772, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907791, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907811, "dur": 15, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907828, "dur": 52, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907881, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907911, "dur": 15, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907928, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907947, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249907964, "dur": 55, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908020, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908043, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908061, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908079, "dur": 65, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908145, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908164, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908187, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908210, "dur": 14, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908225, "dur": 57, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908284, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908302, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908319, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908337, "dur": 56, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908394, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908412, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908430, "dur": 15, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908446, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908503, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908519, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908536, "dur": 14, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908552, "dur": 53, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908607, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908625, "dur": 26, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908652, "dur": 15, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908670, "dur": 50, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908721, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908739, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908756, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908758, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908775, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908822, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908839, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908841, "dur": 18, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908861, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908878, "dur": 52, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908932, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908949, "dur": 16, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908967, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249908985, "dur": 174, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909167, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909171, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909256, "dur": 2, "ph": "X", "name": "ProcessMessages 2172", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909260, "dur": 26, "ph": "X", "name": "ReadAsync 2172", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909287, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909289, "dur": 40, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909334, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909354, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909373, "dur": 14, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909389, "dur": 49, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909439, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909459, "dur": 23, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909484, "dur": 15, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909500, "dur": 48, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909550, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909569, "dur": 16, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909587, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909605, "dur": 14, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909621, "dur": 49, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909671, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909689, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909707, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909729, "dur": 16, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909747, "dur": 74, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909824, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909847, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909864, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909884, "dur": 18, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909904, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909921, "dur": 30, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909953, "dur": 15, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909970, "dur": 15, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249909986, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910047, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910067, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910086, "dur": 53, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910141, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910159, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910175, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910194, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910213, "dur": 16, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910230, "dur": 15, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910247, "dur": 17, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910265, "dur": 14, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910281, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910327, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910345, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910363, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910379, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910427, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910446, "dur": 15, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910463, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910479, "dur": 50, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910531, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910549, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910565, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910581, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910632, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910651, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910668, "dur": 13, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910684, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910699, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910743, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910762, "dur": 17, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910781, "dur": 14, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910797, "dur": 43, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910841, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910860, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910879, "dur": 14, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910894, "dur": 53, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910949, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910973, "dur": 16, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249910990, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911007, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911079, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911104, "dur": 20, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911125, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911145, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911213, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911234, "dur": 16, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911252, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911269, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911330, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911349, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911351, "dur": 14, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911367, "dur": 14, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911383, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911435, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911455, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911472, "dur": 16, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911490, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911549, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911568, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911606, "dur": 15, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911622, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911673, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911693, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911710, "dur": 14, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911727, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911775, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911796, "dur": 14, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911813, "dur": 13, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911827, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911844, "dur": 56, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911902, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911921, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911938, "dur": 14, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249911954, "dur": 49, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912004, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912022, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912041, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912058, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912085, "dur": 16, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912103, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912121, "dur": 13, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912135, "dur": 15, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912152, "dur": 13, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912166, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912219, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912238, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912256, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912272, "dur": 52, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912326, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912346, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912367, "dur": 15, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912384, "dur": 60, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912446, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912467, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912487, "dur": 14, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912503, "dur": 60, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912565, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912587, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912603, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912619, "dur": 14, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912635, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912652, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912670, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912688, "dur": 15, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912704, "dur": 25, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912731, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912791, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912809, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912827, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912843, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912894, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912911, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912929, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912952, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912974, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249912991, "dur": 16, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913009, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913024, "dur": 16, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913041, "dur": 46, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913089, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913105, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913126, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913150, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913152, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913176, "dur": 16, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913194, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913209, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913224, "dur": 15, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913241, "dur": 15, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913257, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913309, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913327, "dur": 119, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913449, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913483, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913485, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913531, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913534, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913571, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913607, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913656, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913659, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913718, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913722, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913782, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913785, "dur": 62, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913851, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913854, "dur": 50, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913907, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913908, "dur": 30, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913943, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913971, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913991, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249913993, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914029, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914031, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914082, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914084, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914138, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914141, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914192, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914195, "dur": 36, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914234, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914236, "dur": 45, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914284, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914287, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914350, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914354, "dur": 61, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914417, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914421, "dur": 47, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914471, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914473, "dur": 52, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914528, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914530, "dur": 39, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914571, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914573, "dur": 45, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914622, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914624, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914680, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914681, "dur": 28, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914712, "dur": 36, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914751, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914753, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914799, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914802, "dur": 49, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914853, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914857, "dur": 45, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914905, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914908, "dur": 53, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914964, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249914967, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915021, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915024, "dur": 46, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915073, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915076, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915113, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915115, "dur": 21, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915138, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915181, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915183, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915229, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915232, "dur": 37, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915271, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915273, "dur": 39, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915314, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915317, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915358, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915360, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915393, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915396, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915425, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915469, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915471, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915501, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915504, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915528, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915530, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915548, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915581, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915603, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249915627, "dur": 10546, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926179, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926225, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926254, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926452, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926490, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926538, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926565, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926603, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249926622, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927023, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927051, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927116, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927150, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927219, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927240, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927457, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927477, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927496, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927722, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927759, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927761, "dur": 45, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927809, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927811, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927839, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927868, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927870, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927902, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927904, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927941, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249927983, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928019, "dur": 446, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928469, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928513, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928547, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928584, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928586, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928622, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928624, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928685, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928721, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928722, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928771, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928807, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928839, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928841, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928877, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928878, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928906, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928909, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249928942, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929028, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929064, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929065, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929101, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929128, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929161, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929162, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929191, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929218, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929256, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929258, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929289, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929407, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929440, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929441, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929514, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929547, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929548, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929586, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929619, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929621, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929651, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929653, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929679, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929716, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929744, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929838, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929855, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929916, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929935, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249929954, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930062, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930081, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930106, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930243, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930270, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930291, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930382, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930401, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930429, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930431, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930520, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930555, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930557, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930590, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930593, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930622, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930655, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930659, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930690, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930767, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930800, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930831, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930899, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249930925, "dur": 694, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931623, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931664, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931764, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931797, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931799, "dur": 83, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931886, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931926, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249931928, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932000, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932035, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932217, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932253, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932296, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932298, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932432, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932466, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932468, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932501, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932531, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932532, "dur": 80, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932616, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932647, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932676, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249932982, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933000, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933044, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933072, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933073, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933095, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933179, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933202, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933385, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933403, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933421, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933505, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933528, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933573, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933589, "dur": 238, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933829, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933844, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249933846, "dur": 46414, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249980270, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249980274, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249980295, "dur": 23, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249980318, "dur": 8224, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988547, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988549, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988569, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988821, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988848, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249988850, "dur": 215, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989069, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989103, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989156, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989185, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989229, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989267, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989329, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989359, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989447, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989475, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989476, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989543, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989581, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989583, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989623, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989653, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989702, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989703, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989742, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989746, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989776, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989817, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989818, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989865, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989867, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989908, "dur": 29, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989940, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989971, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249989973, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990013, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990015, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990050, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990052, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990086, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990119, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990146, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990148, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990188, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990191, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990228, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990229, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990264, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990311, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990350, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990388, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990389, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990421, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249990440, "dur": 1358, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991802, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991827, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991850, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991883, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991884, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991919, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991921, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249991952, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992018, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992040, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992073, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992101, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992103, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992136, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992166, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992191, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992193, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992224, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992252, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992254, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992278, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992438, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992475, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992476, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992501, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897249992520, "dur": 99729, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250092259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250092263, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250092283, "dur": 1181, "ph": "X", "name": "ProcessMessages 2357", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250093467, "dur": 2872, "ph": "X", "name": "ReadAsync 2357", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250096342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250096344, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10404, "tid": 34359738368, "ts": 1750897250096364, "dur": 7806, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250104637, "dur": 705, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 10404, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 10404, "tid": 30064771072, "ts": 1750897249888105, "dur": 33652, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 10404, "tid": 30064771072, "ts": 1750897249921758, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 10404, "tid": 30064771072, "ts": 1750897249921759, "dur": 36, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250105343, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 10404, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 10404, "tid": 25769803776, "ts": 1750897249885898, "dur": 218306, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 10404, "tid": 25769803776, "ts": 1750897249885999, "dur": 2076, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 10404, "tid": 25769803776, "ts": 1750897250104206, "dur": 53, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 10404, "tid": 25769803776, "ts": 1750897250104216, "dur": 15, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250105347, "dur": 2, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750897249898700, "dur": 1338, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897249900047, "dur": 582, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897249900767, "dur": 576, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897249905234, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750897249910198, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750897249901358, "dur": 12964, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897249914332, "dur": 181799, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897250096140, "dur": 395, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897250097266, "dur": 54, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897250097340, "dur": 1008, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750897249901074, "dur": 13266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249914360, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249914554, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD6D4C7F30E8FD4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249914679, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F302F5FA886F2ABC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249914731, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249914802, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F302F5FA886F2ABC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249914946, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249914945, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249915110, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249915258, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750897249915652, "dur": 588, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750897249916298, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9147286922948009921.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750897249916436, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249917730, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249918823, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249919864, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249921156, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249922174, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249923284, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249924342, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249925394, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249926415, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249927299, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249927568, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249928243, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249928593, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750897249929348, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249928943, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750897249929827, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249930137, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249930313, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249930599, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249931211, "dur": 54436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249985693, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249989390, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\rsp\\10010842633742469623.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750897249985648, "dur": 3799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750897249989448, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249990080, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249990482, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249990634, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249990738, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750897249989819, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750897249992950, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750897249993070, "dur": 103071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249901098, "dur": 13261, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249914365, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750897249914506, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249914637, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249914636, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750897249914748, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750897249915040, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750897249915458, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750897249915592, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750897249915773, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750897249915852, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750897249916321, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750897249916372, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249916437, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249917639, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249918768, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249919822, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249921144, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249922160, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249923198, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249924305, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249925368, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249926965, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249927656, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249928097, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249928767, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249929777, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249929875, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249930112, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249930309, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249930595, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249931244, "dur": 54417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249985710, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249989392, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249989545, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249989731, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249985697, "dur": 4235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750897249989933, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249990081, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990240, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990420, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990519, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990631, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990822, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249991031, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750897249990068, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750897249993020, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750897249993162, "dur": 102976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249901789, "dur": 13021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249914822, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249914811, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4137C56C25BAB6AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750897249914881, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249915059, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249915112, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750897249915271, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750897249915658, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750897249915928, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750897249916101, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750897249916228, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750897249916405, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249917729, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249918789, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249919884, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249921177, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249922197, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249923433, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249924697, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249925726, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249927135, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249927618, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249928109, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249928747, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249929777, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249929870, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249930103, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249930295, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249930585, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249931293, "dur": 54385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249989391, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249989733, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249989978, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990080, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990287, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990420, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990520, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990633, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990696, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249990940, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750897249985687, "dur": 5392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750897249991079, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249991199, "dur": 1922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750897249993197, "dur": 102980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249901122, "dur": 13245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249914373, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750897249914577, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249914785, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249914882, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249915131, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750897249915252, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750897249915379, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750897249915935, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750897249916061, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750897249916174, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750897249916441, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249917644, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249918719, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249919834, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249921394, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249922539, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249923719, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249924804, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249925870, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249927058, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249927631, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249928153, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249928703, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249929765, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249929842, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249930082, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249930308, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249930594, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249931237, "dur": 54427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249988284, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750897249985665, "dur": 4410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750897249990076, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249990192, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750897249990153, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750897249993055, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750897249993170, "dur": 103019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249901454, "dur": 13123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249914595, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249914587, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_262098ACA416DFC1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750897249914676, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249914800, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_262098ACA416DFC1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750897249914904, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249915031, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_32492E6E491A66EE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750897249915160, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249915256, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249915397, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750897249915658, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249915934, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249916159, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249916401, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249916459, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750897249916611, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249918109, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249919202, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249920246, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249921797, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249922886, "dur": 5270, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249928157, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249928694, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249929763, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249929878, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249930108, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249930303, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249930582, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249931285, "dur": 54342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249989391, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249989732, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990035, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990241, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249985628, "dur": 4723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990352, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249990577, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990737, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990822, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249991000, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249991293, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750897249990462, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750897249993095, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750897249993194, "dur": 102972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249901189, "dur": 13195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249914434, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914390, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914581, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249914657, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914656, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1F1578B895CD76F0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914786, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914785, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0CACEFF522BB87D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914879, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249914878, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249915044, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750897249915142, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750897249915432, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249915490, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750897249915813, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750897249916009, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750897249916195, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750897249916293, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750897249916414, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249917588, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249918708, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249919759, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249921157, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249922179, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249923201, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249924321, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249925515, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249926634, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249926710, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249926858, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249927668, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249928085, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249928532, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249928731, "dur": 1045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750897249929777, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249929888, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750897249930070, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750897249930493, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249930589, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249931266, "dur": 54405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249989798, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249989979, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249990081, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249990193, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249990484, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249990579, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750897249985673, "dur": 4983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750897249990657, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249990746, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249990981, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249991167, "dur": 1929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750897249993147, "dur": 103007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249901553, "dur": 13072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249914637, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249914632, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750897249914747, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249914859, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249914959, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249915166, "dur": 882, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750897249916092, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750897249916425, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750897249916543, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249917668, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249918734, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249919805, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249921424, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249922511, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249923555, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249924632, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249925667, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249926743, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249927505, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249928077, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249928535, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750897249928662, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249928784, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750897249929329, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249929580, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249929786, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249929844, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249930085, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249930269, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750897249930397, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750897249930771, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249930838, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249931174, "dur": 54521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249989391, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249989979, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249985696, "dur": 4637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750897249990334, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249990579, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249990904, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249991035, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750897249990560, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750897249993150, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750897249993239, "dur": 102936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249901239, "dur": 13178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249914436, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249914428, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750897249914568, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249914731, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_CB6996444704BF62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750897249914827, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_CB6996444704BF62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750897249915105, "dur": 715, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750897249915977, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750897249916098, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750897249916382, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249917752, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249919245, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Drawing\\Inspector\\PropertyDrawers\\Texture2DArrayPropertyDrawer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750897249919030, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249921280, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249922307, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249923401, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249924511, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249925559, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249926575, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249926770, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249927551, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249928229, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249928608, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750897249928910, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750897249929584, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249929752, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249929835, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249930099, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249930292, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249930591, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249931258, "dur": 54398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249989391, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249989977, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249990036, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249990311, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249990421, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750897249985657, "dur": 4902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750897249990560, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249990696, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249990832, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249990988, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249991406, "dur": 2037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750897249993488, "dur": 102672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249901618, "dur": 13046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249914687, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750897249914676, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_6E89F7CAA1D4322F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750897249914805, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E9D933C7AF1168F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750897249914895, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E9D933C7AF1168F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750897249915107, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249915170, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D3E2F14B3A6F429A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750897249915331, "dur": 579, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750897249915959, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750897249916237, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750897249916357, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249917560, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249918660, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249919826, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249921106, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249922128, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249923316, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249924388, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249925438, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249926470, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249927077, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249927624, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249928180, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249928664, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249929783, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249929835, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249930102, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249930304, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249930588, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249931273, "dur": 54409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249989732, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750897249985683, "dur": 4318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750897249990002, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249990241, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750897249990377, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750897249990941, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750897249990219, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750897249993049, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750897249993150, "dur": 103005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249901278, "dur": 13195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249914495, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914484, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914569, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249914666, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914664, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914721, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249914785, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914783, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C4C4B9839337CEB6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249914896, "dur": 571, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C4C4B9839337CEB6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249915474, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249915980, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249916373, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249916433, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249916500, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249916605, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249916901, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917020, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917125, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917205, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917258, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917364, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917475, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917547, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917598, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249917652, "dur": 434, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918088, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918186, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918286, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918344, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918448, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918555, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918629, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918684, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918749, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918815, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249918901, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249919175, "dur": 708, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249919911, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920036, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920147, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920292, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920607, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920687, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920753, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920810, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920867, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249920992, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249921147, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249921226, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249921376, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249921437, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249921517, "dur": 930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249922486, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249922542, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249922690, "dur": 1777, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924513, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924629, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924686, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924739, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924814, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924883, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249924949, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925014, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925068, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925147, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925205, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925268, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925330, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925381, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925448, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925512, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925573, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925642, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249925883, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926072, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926127, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926185, "dur": 476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926662, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926801, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750897249926921, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750897249915598, "dur": 11799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249927531, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249927605, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249928149, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249928221, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249928533, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249929350, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249928786, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249929679, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249929874, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750897249930084, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249930432, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249930594, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1750897249931123, "dur": 89, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249931608, "dur": 49644, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1750897249987242, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-filesystem-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249989391, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249990079, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249990287, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750897249985641, "dur": 4898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750897249990542, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249990920, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249990980, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249991088, "dur": 1933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750897249993060, "dur": 103082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249901296, "dur": 13192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249914502, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249914495, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249914563, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249914652, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9E74BE77200C0976.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249914834, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0841EB48B73D0C6A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249915096, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750897249915230, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750897249915459, "dur": 466, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750897249915926, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750897249916048, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750897249916294, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750897249916426, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249917733, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249918871, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249920175, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249921480, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249922472, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249923506, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249924575, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249925582, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249926742, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249927540, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249928256, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249928583, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249928754, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249929630, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249929854, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249930091, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249930278, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249930570, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249931150, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249931418, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249932317, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Reflection\\CodebaseSubset.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750897249931524, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249932828, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249932905, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249932999, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249933518, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249933609, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249934003, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249934077, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249934408, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750897249934508, "dur": 51140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249989392, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249989484, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249989797, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249990081, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249990517, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249990822, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750897249985649, "dur": 5287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750897249990940, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249991079, "dur": 1801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750897249992910, "dur": 103224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249901708, "dur": 13021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249914748, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249914739, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5A63E73C35F67AF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249914856, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5A63E73C35F67AF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249914919, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249915034, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750897249915316, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750897249915381, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249915466, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750897249915819, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750897249916008, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750897249916249, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750897249916447, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750897249916509, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249917715, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249918789, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249919968, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249921235, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249922271, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249923309, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249924479, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249925520, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249926661, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249926832, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249927673, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249928078, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249928538, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249928718, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249929646, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249929898, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249930073, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249930444, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249930601, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249930717, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249931085, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249931151, "dur": 2372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249933524, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249933650, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249933961, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249934074, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750897249934180, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249934505, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249934578, "dur": 51067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249989391, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249989486, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249989796, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249989978, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249990035, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249990241, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249990378, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249990481, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249990724, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750897249985665, "dur": 5587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750897249991303, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249991421, "dur": 2030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750897249993486, "dur": 102696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249901339, "dur": 13187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249914540, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914533, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914639, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249914788, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914787, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4796D0C7D217714A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914885, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4796D0C7D217714A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914988, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249914986, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249915217, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_CB41D959725ECE1B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249915387, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1750897249915622, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1750897249915978, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750897249916074, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750897249916340, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249917568, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249918642, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249919785, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249921075, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249922117, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249923169, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249924240, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249925327, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249926370, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249927291, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249927513, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249928075, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249928533, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249928854, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750897249929733, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249929860, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249930079, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249930267, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249930404, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750897249931148, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750897249931246, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750897249931520, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249931627, "dur": 54057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249987218, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249987318, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249989392, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249989977, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249990035, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249985685, "dur": 4510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750897249990196, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750897249990579, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249990633, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249990696, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750897249990339, "dur": 3055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750897249993497, "dur": 102660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249901360, "dur": 13180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249914563, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914552, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914638, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249914707, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914705, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_046FB1380673C4F3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914812, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_046FB1380673C4F3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914970, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249914969, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249915156, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249915261, "dur": 439, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750897249915938, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750897249916103, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750897249916202, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750897249916369, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750897249916449, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249917759, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249918807, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249919828, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249921182, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249922240, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249923315, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249924375, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249925404, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249926456, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249927268, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249927532, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249928274, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249928548, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750897249928864, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750897249929326, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249929494, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249929780, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249929853, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249930088, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249930275, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249930602, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249931203, "dur": 54465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249987292, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-conio-l1-1-0.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249989392, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249985670, "dur": 4361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990032, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249990287, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990576, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990696, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990780, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990902, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750897249990200, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750897249992968, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750897249993076, "dur": 103103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249901089, "dur": 13262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249914360, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249914826, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_1E575EBD1ADFF155.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750897249915239, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750897249915496, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750897249915703, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750897249915941, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750897249916072, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750897249916257, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750897249916383, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249917606, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249918693, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249919868, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249921254, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249922300, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249923362, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249924437, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249925723, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249926774, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249927661, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249928091, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249928531, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750897249928733, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249930266, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750897249930412, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249931311, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249931414, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750897249932319, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Ports\\IUnitPort.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750897249932506, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Ports\\IUnitPortCollection.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750897249931513, "dur": 1081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249932594, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249932783, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750897249932879, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249933185, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249933272, "dur": 52407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249990480, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750897249985680, "dur": 4879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249990560, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249990780, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750897249990941, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750897249990623, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750897249993150, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750897249993238, "dur": 102924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249901401, "dur": 13153, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249914572, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750897249914561, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_44E9AA5F4E926643.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750897249914672, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249914745, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_44E9AA5F4E926643.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750897249914922, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249915056, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750897249915380, "dur": 637, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750897249916103, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750897249916365, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249917550, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249918662, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249919799, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249921175, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249922315, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249923380, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249924465, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249925472, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249926605, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249926867, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249927643, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249928164, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249928685, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249929762, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249929833, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249930071, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249930269, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249930561, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249931156, "dur": 2919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249934076, "dur": 51624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249989392, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750897249990036, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750897249990141, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750897249990287, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750897249985701, "dur": 4780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750897249990481, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249990794, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249991004, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249991347, "dur": 1867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750897249993252, "dur": 102906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249901423, "dur": 13138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249914577, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914569, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_A45827FC07386469.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914676, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914675, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FA846FADE8279C51.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914793, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914792, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914883, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914959, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249914958, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249915219, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750897249915318, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1750897249915743, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1750897249915921, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750897249916150, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750897249916402, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249916511, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249917660, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249918750, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249919771, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249921168, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249922226, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249923258, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249924354, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249925431, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249926605, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249926713, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249927507, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249928076, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249928539, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249928708, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750897249929690, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249929869, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750897249930078, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750897249930847, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249930949, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249931167, "dur": 54462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249987262, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249989391, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249989978, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249990141, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249990285, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249990378, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249990483, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249990579, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750897249985630, "dur": 5608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750897249991239, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249991370, "dur": 1880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750897249993250, "dur": 102889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249901144, "dur": 13231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249914424, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914381, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914517, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249914671, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914670, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0767B7051AB61AA1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914779, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_74AA62AB0BB4C3E0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914876, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_74AA62AB0BB4C3E0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249914937, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249915106, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750897249915257, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750897249915440, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1750897249915647, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1750897249915884, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249915940, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750897249916258, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750897249916406, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249918042, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\DirectorNamedColor.cs"}}, {"pid": 12345, "tid": 18, "ts": 1750897249917700, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249919329, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249920352, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249921689, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249922740, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249923766, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249924857, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249926228, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249927161, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249927611, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249928280, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249928541, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249928804, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750897249929430, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249929578, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249929783, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249929838, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249930080, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249930268, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750897249930415, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750897249930803, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249930955, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249931160, "dur": 54471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249985992, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750897249987262, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750897249989796, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750897249985663, "dur": 4888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750897249990552, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249990677, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249990913, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249991012, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249991364, "dur": 1852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750897249993217, "dur": 102915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249901487, "dur": 13102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249914602, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249914596, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750897249914727, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249914806, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750897249914912, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249915148, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249915147, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_077956647DBD2B29.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750897249915327, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750897249915490, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1750897249915742, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750897249915832, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750897249916078, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750897249916351, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249917606, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249918690, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249919799, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249921133, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249922179, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249923233, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249924317, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249925388, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249926408, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249927262, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249927560, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249928236, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249928600, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750897249928923, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750897249929703, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249929862, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249930094, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249930280, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249930574, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249931324, "dur": 54327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249987469, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249989389, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249989799, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249990081, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249990240, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249990376, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750897249985653, "dur": 4889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750897249990542, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249990828, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249991041, "dur": 1761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249992848, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750897249993490, "dur": 102640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249901510, "dur": 13090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249914616, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249914608, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_DF935DB001343E8A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750897249914715, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249914776, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249914775, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_72EF51AA8584F47D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750897249914909, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249915103, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750897249915238, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750897249915566, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249915686, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249915778, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750897249915872, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750897249915940, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750897249916079, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750897249916402, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249917690, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249918775, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249919824, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249921075, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249922080, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249923269, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249924436, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249925470, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249926971, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249927649, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249928103, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249928757, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249929778, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249929880, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249930115, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249930311, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249930597, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249931219, "dur": 54457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249989979, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249990081, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249990192, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249985677, "dur": 4598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750897249990276, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750897249990778, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249990902, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750897249990455, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750897249993509, "dur": 102683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249901532, "dur": 13082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249914771, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249914823, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_C5E1A198ADF17864.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750897249914980, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249914979, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915155, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915299, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915389, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915560, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915644, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249915922, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249916008, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1750897249916207, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249916432, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750897249916490, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249917713, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249918779, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249919835, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249921081, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249922092, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249923171, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249924280, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249925351, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249926423, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249927301, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249927604, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249928190, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249928655, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249929753, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249929852, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750897249930113, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750897249930521, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249930644, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249931195, "dur": 54459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249989149, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249989391, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249989484, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249989796, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249989978, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249990081, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249990192, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249990312, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249985655, "dur": 4799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750897249990455, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750897249990904, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249991031, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750897249990529, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750897249993461, "dur": 102674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249901215, "dur": 13179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249914411, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914402, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914614, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914613, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E79E12ACD5E69601.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914688, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249914783, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914781, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BC49A6886E1E88EB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750897249914860, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249915053, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BC49A6886E1E88EB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750897249915434, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750897249915552, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249915651, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750897249915832, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750897249916102, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750897249916202, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750897249916274, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750897249916366, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249917677, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249918754, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249919781, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249921073, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249922250, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249923325, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249924377, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249925404, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249926415, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249927328, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249927591, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249928206, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249928651, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750897249928975, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750897249929468, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249929585, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249929784, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249929838, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249930067, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249930270, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249930598, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249931231, "dur": 54427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249985659, "dur": 3689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750897249989349, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249989978, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249990081, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249990193, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249990378, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249990864, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750897249989529, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750897249992770, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750897249992894, "dur": 103289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249901577, "dur": 13060, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249914654, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249914646, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9118DBDC897F9C38.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750897249914711, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249914890, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750897249915033, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249915276, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1750897249915476, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750897249915707, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750897249915925, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750897249916354, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249917539, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249918620, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249919672, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249921107, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249922144, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249923365, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249924456, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249925479, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249926642, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249926729, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249927510, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249928075, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249928166, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249928675, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249929864, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249930100, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249930289, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249930577, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249931312, "dur": 58086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249989400, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249989731, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249990081, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249990579, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249990697, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249990822, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249991202, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750897249989399, "dur": 3328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750897249992727, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750897249992857, "dur": 103287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249901599, "dur": 13048, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249914663, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750897249914655, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FB86898111D557DD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249914790, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750897249914789, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249914885, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249915081, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249915171, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249915433, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249915884, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249916073, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249916170, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750897249916444, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249917648, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249918723, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249919757, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249921080, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249922118, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249923181, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249924662, "dur": 1114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double3x3.gen.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750897249924306, "dur": 2151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249926457, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249927304, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249927576, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249928222, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249928617, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249928866, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249929613, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249929758, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249929834, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249930068, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249930271, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249930562, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249931147, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249931259, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249931739, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249931815, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249932317, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Graphs\\Texture2DShaderProperty.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750897249931900, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249933439, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249933504, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249933985, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249934069, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750897249934177, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249934512, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750897249934580, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750897249934877, "dur": 158379, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750897249901252, "dur": 13192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249914468, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914458, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914608, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914606, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D19DF51C870C57DD.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914733, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914732, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_4C50B7F708B8DB2C.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750897249914887, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249915031, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750897249915204, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750897249915537, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750897249915658, "dur": 365, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750897249916083, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750897249916368, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249917638, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249918719, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249920021, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249921426, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249922466, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249923533, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249924602, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249925639, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249926717, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249927514, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249928111, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249928738, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249929776, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249929858, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249930093, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249930290, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249930580, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249931302, "dur": 54391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249989732, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249985694, "dur": 4454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750897249990149, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249990377, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249990483, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249990575, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750897249990312, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750897249993118, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750897249993269, "dur": 102868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249901641, "dur": 13041, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249914708, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249914694, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1FFDB2139C1829E2.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750897249914779, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249914894, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249915091, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1750897249915296, "dur": 509, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750897249915882, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249915955, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750897249916055, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750897249916362, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249917533, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249918688, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249919880, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249921141, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249922182, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249923208, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249924320, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249925406, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249926979, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249927637, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249928116, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249928714, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249929768, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249929843, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249930095, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249930286, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249930572, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249931146, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750897249931265, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750897249931565, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249931667, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750897249931764, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750897249932273, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249932635, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750897249932757, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750897249933142, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249933209, "dur": 52482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249989390, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249989798, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990035, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990141, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990194, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990381, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990484, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990633, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249990736, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750897249985694, "dur": 5454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750897249991149, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249991249, "dur": 1923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750897249993172, "dur": 102992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249901666, "dur": 13031, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249914715, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249914705, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_62E3103B1EFE0DCE.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750897249914777, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249914857, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249914856, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DB6E6E7F0102E05A.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915060, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DB6E6E7F0102E05A.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915387, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915478, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915601, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915816, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750897249915967, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249916077, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249916182, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750897249916331, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249917573, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249918729, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249919831, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249921467, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249922712, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249923763, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249924830, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249926244, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249927186, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249927584, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249928214, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249928629, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750897249928827, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750897249929572, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249929780, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249929869, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249930099, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249930287, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249930575, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249931319, "dur": 54454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249989520, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249989798, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249990080, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249990376, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750897249985774, "dur": 5000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750897249990774, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249990985, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249991179, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249991245, "dur": 1879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750897249993125, "dur": 103025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249901686, "dur": 13027, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249914737, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249914724, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750897249914874, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249914872, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750897249915058, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750897249915146, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249915145, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_13EB1DAF647B9A69.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750897249915316, "dur": 406, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750897249915781, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1750897249916090, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750897249916401, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249916454, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750897249916586, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249917750, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249918911, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249919971, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249921298, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249922311, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249923424, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249924499, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249925561, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249926333, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249927182, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249927599, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249928196, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249928638, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750897249928940, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750897249929415, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249929596, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249929788, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249929875, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249930110, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249930307, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249930593, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249931250, "dur": 54385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249988297, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249990035, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249990192, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249990483, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750897249985638, "dur": 4941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750897249990579, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249990878, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249990987, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249991233, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249991327, "dur": 1862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750897249993221, "dur": 102925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249901316, "dur": 13196, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249914535, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249914524, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8DB4C22B24A5A2EB.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750897249914648, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249914713, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249914711, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_02D2E9C797962C02.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750897249914781, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249914882, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249915116, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750897249915285, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750897249915396, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750897249915604, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750897249916068, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750897249916160, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750897249916428, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249917759, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249918838, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249919879, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249921163, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249922187, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249923286, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249924388, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249925444, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249926383, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249927265, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249927523, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249928115, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249928726, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249929770, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249929874, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249930109, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249930314, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249930596, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249931226, "dur": 54412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249989389, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990081, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990192, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249985639, "dur": 4619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990258, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750897249990483, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990575, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990696, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990822, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990999, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750897249990445, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750897249993481, "dur": 102668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249901731, "dur": 13011, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249914747, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_771434CFFF9D260C.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249914802, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249914894, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249915025, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 30, "ts": 1750897249915220, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F026DDF4FF0829A6.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249915368, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750897249915475, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249915604, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916367, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916425, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916504, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916615, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916923, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249916993, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249917117, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249917394, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249917472, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249917573, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249917654, "dur": 439, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918094, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918646, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918701, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918761, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918832, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249918914, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919194, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919277, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919382, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919441, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919690, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919753, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919848, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249919908, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920078, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920136, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920332, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920757, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920812, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920865, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249920976, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921038, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921092, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921162, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921307, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921376, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921458, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249921517, "dur": 785, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249922303, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249922358, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249922429, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249922571, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249922717, "dur": 1978, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249924707, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249924774, "dur": 902, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249925747, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249925890, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249926077, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249926153, "dur": 489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249915746, "dur": 11291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750897249927038, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249927213, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249927553, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249928250, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249928576, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249928737, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750897249930066, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249930154, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750897249930282, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750897249930676, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249930735, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249931182, "dur": 54459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249985965, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249987755, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Extensions.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249989798, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990240, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990484, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990579, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990737, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990822, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750897249985643, "dur": 5344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750897249990988, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249991133, "dur": 1914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750897249993047, "dur": 103081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249901751, "dur": 13000, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249914765, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249914758, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_EAFF22773CAE8FE7.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750897249914911, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249915445, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750897249915594, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750897249915774, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750897249916187, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750897249916473, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249917637, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249918711, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249919771, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249921135, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249922152, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249923172, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249924251, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249925311, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249926388, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249927256, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249927546, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249928264, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249928568, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750897249928693, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249929348, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249928755, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750897249929474, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249929593, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249929789, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249929872, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249930104, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249930299, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249930587, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249931279, "dur": 54409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249987295, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249989390, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249989798, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990081, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990141, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990379, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990483, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990633, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990696, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249990779, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249991000, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750897249985689, "dur": 5573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750897249991263, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249991414, "dur": 2033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750897249993506, "dur": 102668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249901776, "dur": 12995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249914784, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249914776, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2DD09EF85C7C380D.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750897249914898, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2DD09EF85C7C380D.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750897249915218, "dur": 714, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1750897249915933, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750897249916073, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1750897249916177, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750897249916340, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249917468, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249918703, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249919823, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249921189, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249922237, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249923249, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249924319, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249925351, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249926458, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249927260, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249927539, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249928269, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249928557, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750897249929001, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249929347, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249928876, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750897249929803, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249929917, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750897249930067, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750897249930521, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249930649, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249931188, "dur": 54437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249987523, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249985626, "dur": 4066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750897249989694, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249990141, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249990865, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249990999, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249991162, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750897249989812, "dur": 3359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750897249993172, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750897249993277, "dur": 102876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750897250101858, "dur": 2925, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 10404, "tid": 109, "ts": 1750897250105387, "dur": 994, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 10404, "tid": 109, "ts": 1750897250106431, "dur": 3771, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 10404, "tid": 109, "ts": 1750897250104619, "dur": 5614, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
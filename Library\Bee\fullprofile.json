{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 47468, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 47468, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 47468, "tid": 112, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 47468, "tid": 112, "ts": 1750901111173513, "dur": 6, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111173527, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 47468, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 47468, "tid": 1, "ts": 1750901111026720, "dur": 940, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750901111027660, "dur": 13870, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750901111041532, "dur": 17896, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111173530, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 47468, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111026702, "dur": 9094, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111035797, "dur": 137359, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111035803, "dur": 27, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111035834, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111036058, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111036079, "dur": 3, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111036083, "dur": 2319, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038405, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038441, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038443, "dur": 25, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038471, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038472, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038497, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038521, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038547, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038548, "dur": 20, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038573, "dur": 23, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038599, "dur": 19, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038620, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038623, "dur": 35, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038660, "dur": 43, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038706, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038764, "dur": 27, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038794, "dur": 23, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038819, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038822, "dur": 41, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038865, "dur": 1, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038866, "dur": 20, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038888, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038909, "dur": 21, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038933, "dur": 25, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038960, "dur": 16, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111038979, "dur": 18, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039000, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039021, "dur": 16, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039039, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039058, "dur": 18, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039078, "dur": 30, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039110, "dur": 20, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039132, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039151, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039169, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039187, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039189, "dur": 17, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039207, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039225, "dur": 15, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039242, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039261, "dur": 15, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039278, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039296, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039315, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039341, "dur": 16, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039359, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039380, "dur": 17, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039399, "dur": 16, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039416, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039436, "dur": 15, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039453, "dur": 15, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039470, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039487, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039510, "dur": 16, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039527, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039546, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039564, "dur": 15, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039581, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039598, "dur": 15, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039614, "dur": 5, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039620, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039640, "dur": 17, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039659, "dur": 27, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039688, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039707, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039727, "dur": 21, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039750, "dur": 18, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039770, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039790, "dur": 15, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039807, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039825, "dur": 16, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039842, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039863, "dur": 17, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039881, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039902, "dur": 16, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039920, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039937, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039955, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111039983, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040004, "dur": 23, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040029, "dur": 16, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040049, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040070, "dur": 17, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040089, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040106, "dur": 19, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040127, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040146, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040162, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040164, "dur": 33, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040201, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040224, "dur": 50, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040277, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040301, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040321, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040324, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040345, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040361, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040381, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040402, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040422, "dur": 19, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040446, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040472, "dur": 16, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040489, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040512, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040530, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040551, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040572, "dur": 16, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040590, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040611, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040613, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040631, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040647, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040665, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040682, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040703, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040724, "dur": 14, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040740, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040759, "dur": 15, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040776, "dur": 17, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040795, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040814, "dur": 17, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040833, "dur": 17, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040852, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040873, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040895, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040914, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040931, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040957, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040977, "dur": 12, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111040991, "dur": 16, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041009, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041028, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041050, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041067, "dur": 15, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041084, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041106, "dur": 17, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041124, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041126, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041144, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041168, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041184, "dur": 16, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041202, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041219, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041240, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041258, "dur": 17, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041278, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041301, "dur": 16, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041318, "dur": 18, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041338, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041357, "dur": 15, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041374, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041395, "dur": 21, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041417, "dur": 15, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041434, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041451, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041468, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041485, "dur": 15, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041501, "dur": 16, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041518, "dur": 15, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041535, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041551, "dur": 18, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041571, "dur": 16, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041589, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041606, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041626, "dur": 14, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041641, "dur": 16, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041659, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041678, "dur": 15, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041695, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041714, "dur": 15, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041731, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041747, "dur": 14, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041763, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041782, "dur": 17, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041800, "dur": 17, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041820, "dur": 17, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041838, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041854, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041872, "dur": 18, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041891, "dur": 15, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041908, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041928, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041945, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041962, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041979, "dur": 17, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111041998, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042018, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042035, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042051, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042068, "dur": 16, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042085, "dur": 17, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042104, "dur": 15, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042121, "dur": 16, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042138, "dur": 18, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042158, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042178, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042180, "dur": 30, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042213, "dur": 47, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042261, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042288, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042290, "dur": 22, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042314, "dur": 22, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042339, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042369, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042371, "dur": 24, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042398, "dur": 24, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042425, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042445, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042465, "dur": 15, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042481, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042499, "dur": 30, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042531, "dur": 19, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042552, "dur": 16, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042569, "dur": 119, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042690, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042711, "dur": 22, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042736, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042754, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042771, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042787, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042812, "dur": 15, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042829, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042850, "dur": 17, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042870, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042893, "dur": 14, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042911, "dur": 14, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042927, "dur": 15, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042943, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042963, "dur": 14, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111042979, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043000, "dur": 12, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043013, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043033, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043058, "dur": 18, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043077, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043096, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043114, "dur": 14, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043129, "dur": 15, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043146, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043165, "dur": 29, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043196, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043215, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043238, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043260, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043279, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043298, "dur": 16, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043316, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043338, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043357, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043377, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043405, "dur": 15, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043422, "dur": 16, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043439, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043456, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043473, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043492, "dur": 16, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043509, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043528, "dur": 16, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043545, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043564, "dur": 18, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043584, "dur": 16, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043602, "dur": 16, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043620, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043637, "dur": 18, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043657, "dur": 13, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043672, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043700, "dur": 17, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043719, "dur": 17, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043738, "dur": 14, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043753, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043774, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043794, "dur": 14, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043810, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043829, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043848, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043864, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043885, "dur": 15, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043902, "dur": 17, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043921, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043939, "dur": 14, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043955, "dur": 16, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043972, "dur": 16, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111043990, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044006, "dur": 18, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044026, "dur": 15, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044043, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044059, "dur": 17, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044078, "dur": 17, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044096, "dur": 15, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044113, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044130, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044146, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044163, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044186, "dur": 24, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044213, "dur": 20, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044235, "dur": 23, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044260, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044261, "dur": 18, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044281, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044300, "dur": 15, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044317, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044343, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044364, "dur": 16, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044383, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044405, "dur": 19, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044425, "dur": 15, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044442, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044458, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044478, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044496, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044516, "dur": 14, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044536, "dur": 14, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044552, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044571, "dur": 17, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044590, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044611, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044628, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044647, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044665, "dur": 15, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044682, "dur": 18, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044701, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044722, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044739, "dur": 15, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044756, "dur": 16, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044774, "dur": 15, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044792, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044816, "dur": 19, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044836, "dur": 12, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044860, "dur": 22, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044885, "dur": 16, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044902, "dur": 15, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044918, "dur": 16, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044935, "dur": 18, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044955, "dur": 15, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044972, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111044990, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045006, "dur": 16, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045023, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045043, "dur": 17, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045061, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045079, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045096, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045112, "dur": 15, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045130, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045150, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045167, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045184, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045201, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045221, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045246, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045269, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045290, "dur": 15, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045307, "dur": 19, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045327, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045344, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045366, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045382, "dur": 16, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045400, "dur": 14, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045417, "dur": 26, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045445, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045466, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045481, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045500, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045515, "dur": 69, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045586, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045608, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045625, "dur": 35, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045661, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045676, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045734, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045755, "dur": 16, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045772, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045789, "dur": 14, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045805, "dur": 53, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045860, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045878, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045897, "dur": 14, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045913, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045964, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045981, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111045999, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046001, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046021, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046035, "dur": 56, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046092, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046111, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046129, "dur": 12, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046142, "dur": 14, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046157, "dur": 52, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046212, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046237, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046238, "dur": 22, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046262, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046279, "dur": 53, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046334, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046353, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046374, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046392, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046394, "dur": 61, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046457, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046476, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046496, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046519, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046533, "dur": 56, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046591, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046609, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046627, "dur": 15, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046643, "dur": 52, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046696, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046715, "dur": 18, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046735, "dur": 57, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046800, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046805, "dur": 79, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046889, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046891, "dur": 61, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046954, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046956, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111046987, "dur": 41, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047032, "dur": 44, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047080, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047083, "dur": 43, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047130, "dur": 45, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047178, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047181, "dur": 33, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047216, "dur": 58, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047279, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047311, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047313, "dur": 19, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047334, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047387, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047412, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047432, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047451, "dur": 42, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047494, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047517, "dur": 30, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047549, "dur": 17, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047567, "dur": 47, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047616, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047634, "dur": 14, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047650, "dur": 19, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047671, "dur": 55, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047728, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047750, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047773, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047791, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047840, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047867, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047888, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047906, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047968, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111047988, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048006, "dur": 15, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048023, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048047, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048065, "dur": 62, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048128, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048147, "dur": 19, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048168, "dur": 24, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048194, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048196, "dur": 46, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048243, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048245, "dur": 17, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048264, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048285, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048339, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048359, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048376, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048395, "dur": 47, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048443, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048464, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048482, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048502, "dur": 21, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048525, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048545, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048563, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048581, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048599, "dur": 15, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048616, "dur": 49, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048666, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048685, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048705, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048721, "dur": 52, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048775, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048793, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048810, "dur": 15, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048827, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048843, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048896, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048916, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048935, "dur": 20, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048957, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111048972, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049021, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049041, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049060, "dur": 14, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049076, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049122, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049142, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049158, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049159, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049179, "dur": 56, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049236, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049257, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049276, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049294, "dur": 55, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049350, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049370, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049388, "dur": 12, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049401, "dur": 53, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049456, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049479, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049500, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049518, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049578, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049597, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049616, "dur": 15, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049632, "dur": 59, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049693, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049710, "dur": 5, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049716, "dur": 15, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049734, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049751, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049769, "dur": 14, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049785, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049832, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049849, "dur": 16, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049866, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049883, "dur": 11, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049896, "dur": 54, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049951, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049968, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111049986, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050003, "dur": 53, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050057, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050079, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050097, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050125, "dur": 61, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050188, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050209, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050229, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050247, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050302, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050323, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050342, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050359, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050414, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050432, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050454, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050474, "dur": 18, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050494, "dur": 17, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050513, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050531, "dur": 23, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050556, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050574, "dur": 54, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050629, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050651, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050670, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050686, "dur": 62, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050750, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050771, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050773, "dur": 18, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050792, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050793, "dur": 53, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050848, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050871, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050893, "dur": 51, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050945, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050966, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111050985, "dur": 15, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051002, "dur": 17, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051021, "dur": 23, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051047, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051065, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051082, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051099, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051162, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051182, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051208, "dur": 54, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051264, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051283, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051302, "dur": 16, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051319, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051338, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051356, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051374, "dur": 14, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051390, "dur": 14, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051405, "dur": 48, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051455, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051474, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051491, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051514, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051533, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051552, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051569, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051591, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051611, "dur": 53, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051665, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051685, "dur": 132, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051820, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111051852, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052040, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052086, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052089, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052138, "dur": 54, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052195, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052197, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052238, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052239, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052276, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052324, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052326, "dur": 42, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052370, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052372, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052404, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052406, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052437, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052439, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052480, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052481, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052522, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052525, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052568, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052570, "dur": 48, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052620, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052622, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052677, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052679, "dur": 50, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052731, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052733, "dur": 40, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052776, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052779, "dur": 47, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052828, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052831, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052871, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052872, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052908, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052911, "dur": 45, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052959, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111052962, "dur": 38, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053002, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053004, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053054, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053055, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053115, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053118, "dur": 53, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053174, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053176, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053230, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053232, "dur": 67, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053302, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053305, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053350, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053353, "dur": 42, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053396, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053397, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053441, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053443, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053491, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053494, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053539, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053540, "dur": 39, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053583, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053585, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053638, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053640, "dur": 48, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053691, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053694, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053744, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053746, "dur": 40, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053789, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053791, "dur": 39, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053832, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053835, "dur": 36, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053875, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053902, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053904, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053933, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053935, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053979, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111053981, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111054018, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111054041, "dur": 10732, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111064778, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111064811, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111064840, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065068, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065105, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065160, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065181, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065251, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065266, "dur": 456, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065724, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065757, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065760, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065809, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065834, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065845, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065878, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111065904, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066140, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066159, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066193, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066407, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066450, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066455, "dur": 71, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066530, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066569, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066572, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066614, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066651, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066654, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066696, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066698, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111066733, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067122, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067171, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067209, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067212, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067257, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067259, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067296, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067339, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067342, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067378, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067404, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067407, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067439, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067487, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067523, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067555, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067583, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067611, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067613, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067649, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067680, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067709, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067736, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067771, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067802, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067824, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067856, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067912, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067934, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067960, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067963, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067990, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111067992, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068097, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068131, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068167, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068168, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068193, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068236, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068266, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068363, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068398, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068399, "dur": 166, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068567, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068593, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068613, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068638, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068669, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068687, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068702, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068718, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068736, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068795, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068811, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068839, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068965, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111068998, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069029, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069108, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069141, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069204, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069226, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069228, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069253, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069272, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069274, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069291, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069312, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069330, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069404, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069423, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069439, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069454, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069541, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069564, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069959, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111069986, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070005, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070063, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070078, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070079, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070099, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070122, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070140, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070194, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070227, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070247, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070291, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070310, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070312, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070469, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070490, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070601, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070620, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070634, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070663, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070681, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070696, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070775, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070792, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070807, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070832, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070892, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070915, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111070935, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071190, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071219, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071243, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071260, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071276, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071329, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071346, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071485, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071501, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071516, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071578, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071599, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071616, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071853, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071873, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111071874, "dur": 33404, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111105284, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111105287, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111105324, "dur": 20, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111105345, "dur": 7928, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113278, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113315, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113893, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113933, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111113963, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114010, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114013, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114057, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114059, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114156, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114184, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114300, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114343, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114345, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114417, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114446, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114502, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114504, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114540, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114542, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114592, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114620, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114622, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114714, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114751, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114791, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114824, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114825, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114908, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114962, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111114964, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115002, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115004, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115061, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115083, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115124, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115206, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115208, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115249, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115251, "dur": 49, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115304, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115306, "dur": 48, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115357, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115359, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115401, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115404, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115447, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115486, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115524, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115602, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115648, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115651, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115687, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115689, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115724, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115726, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115763, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115764, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111115972, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111116013, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111116048, "dur": 906, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111116958, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117007, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117009, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117044, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117118, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117154, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117189, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117191, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117220, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117260, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117296, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117385, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117417, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117419, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117443, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117463, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117477, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117499, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117530, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117557, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117614, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117637, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117665, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117695, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117697, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117735, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117736, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111117762, "dur": 44019, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111161788, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111161791, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111161814, "dur": 972, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111162788, "dur": 2930, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111165722, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 38654705664, "ts": 1750901111165746, "dur": 7407, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111173537, "dur": 717, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 47468, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 47468, "tid": 34359738368, "ts": 1750901111026671, "dur": 32767, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 47468, "tid": 34359738368, "ts": 1750901111059440, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 47468, "tid": 34359738368, "ts": 1750901111059441, "dur": 30, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111174255, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 47468, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 47468, "tid": 30064771072, "ts": 1750901111024501, "dur": 148679, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 47468, "tid": 30064771072, "ts": 1750901111024584, "dur": 1807, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 47468, "tid": 30064771072, "ts": 1750901111173181, "dur": 29, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 47468, "tid": 30064771072, "ts": 1750901111173189, "dur": 12, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111174259, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750901111036665, "dur": 1225, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111037897, "dur": 603, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111038631, "dur": 610, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111047710, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750901111039254, "dur": 13286, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111052550, "dur": 112848, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111165399, "dur": 306, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111165785, "dur": 58, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111166582, "dur": 962, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750901111038916, "dur": 13644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111052583, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111052756, "dur": 649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1750901111052698, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_816435D8968F6544.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750901111053406, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111053532, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750901111053941, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054013, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054152, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054311, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054395, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111054453, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054536, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054601, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054728, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750901111054818, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111056312, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111057422, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111058494, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111059860, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111060946, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111062297, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111063355, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111064434, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111065473, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111066018, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111066712, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111067101, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750901111067493, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750901111068278, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111068425, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111069004, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111069740, "dur": 2394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111072135, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750901111072207, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750901111072446, "dur": 38257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111113985, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111114095, "dur": 608, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Web.HttpUtility.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111114784, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111114996, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115096, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115241, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115328, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115501, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115702, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111115788, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111116033, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111116224, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750901111110704, "dur": 6075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750901111116847, "dur": 1725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750901111118606, "dur": 46874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111039445, "dur": 13417, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111052878, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111052870, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_262098ACA416DFC1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750901111053180, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C4C4B9839337CEB6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750901111053231, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111053314, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111053412, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750901111053523, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750901111053833, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054007, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054224, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054319, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054470, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054580, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750901111054774, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111056150, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111057250, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111058317, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111059702, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111060779, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111061842, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111062945, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111064370, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\Tool\\BringWindowToFront.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750901111064004, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111065433, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111065986, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111066650, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111067081, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750901111068122, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111067538, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750901111068483, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111068581, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750901111069035, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111068688, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750901111069459, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111069562, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111069784, "dur": 40681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111111320, "dur": 626, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111113984, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111114784, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111114845, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111110479, "dur": 4573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750901111115053, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750901111115549, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111115613, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111115700, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111115786, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111116033, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111116157, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750901111115154, "dur": 3289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750901111118514, "dur": 46925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111039171, "dur": 13552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111052744, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111052820, "dur": 636, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1750901111052736, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750901111053504, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750901111053625, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750901111053819, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750901111054038, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750901111054205, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750901111054582, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750901111054749, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111056034, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111057076, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111058206, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111059621, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111060707, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111061855, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111062953, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111064367, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111064538, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111065654, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111066011, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111066756, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111067110, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750901111067426, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750901111068044, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111068159, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111068268, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111068402, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111068856, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111069028, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111069764, "dur": 40684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111112353, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111113338, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111113983, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111110461, "dur": 4411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750901111114873, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750901111115331, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111115650, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111115787, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111116123, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111116201, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750901111115008, "dur": 3415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750901111118501, "dur": 46947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111038977, "dur": 13612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111052604, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111052659, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1750901111052596, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750901111052915, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111053305, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111053447, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111053498, "dur": 496, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750901111054070, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750901111054218, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750901111054447, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750901111054623, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750901111054740, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111056017, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111057094, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111058179, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111059619, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111060682, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111062044, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111063491, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111064685, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111065814, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111066025, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111066685, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111067087, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750901111067392, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750901111067954, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111068039, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750901111068171, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111068442, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750901111068791, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111069007, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111069090, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111069792, "dur": 40677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111114095, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111110484, "dur": 4204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750901111114689, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111114845, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111114995, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111115332, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111115699, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111115787, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111115924, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111116082, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111116223, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750901111114828, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750901111117959, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111118061, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750901111118124, "dur": 47275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111039472, "dur": 13404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111052891, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111052885, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F4C82E9DCF8C85FF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111053148, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111053245, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111053347, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111053345, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111053533, "dur": 846, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054384, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054634, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054769, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054840, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054912, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055015, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055241, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055316, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055372, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055474, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055548, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055630, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055714, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055808, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111055979, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056057, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056123, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056178, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056246, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056359, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056434, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056564, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056681, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056738, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056854, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111056952, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057068, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057205, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057271, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057334, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057434, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057509, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057575, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057642, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057710, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057781, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057873, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057926, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111057989, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058099, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058214, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058412, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058563, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058614, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111058674, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059033, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059284, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059386, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059502, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059599, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059657, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059717, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059775, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059831, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111059884, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111060233, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111060294, "dur": 503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111060805, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111060862, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111060961, "dur": 380, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061453, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061555, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061612, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061694, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061801, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061864, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061919, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111061999, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062161, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062233, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062333, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062434, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062650, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062720, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062783, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062836, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111062937, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063003, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063102, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063157, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063272, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063431, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063565, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111063966, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064028, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064079, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064172, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064274, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064335, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064474, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064574, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064624, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064749, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064813, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064868, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064929, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111064983, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111065305, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RectMask2D.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111065407, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750901111054507, "dur": 11026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750901111065534, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111065671, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111066014, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111066743, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111067114, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111067991, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111067412, "dur": 924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750901111068336, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111068443, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111069006, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111069737, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750901111069828, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750901111070043, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111070131, "dur": 40331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111113985, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111114096, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111114731, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111115233, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111110463, "dur": 5036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750901111115500, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111115683, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111115873, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111116007, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111116006, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750901111116239, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111116514, "dur": 1995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750901111118543, "dur": 46914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111039490, "dur": 13398, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111052901, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111052895, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_DF935DB001343E8A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750901111052990, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111053243, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111053309, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111053406, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750901111053685, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111053782, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750901111054007, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750901111054122, "dur": 437, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750901111054560, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750901111054779, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111056090, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111057192, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111058278, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111059650, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111060711, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111061933, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111063128, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111064144, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111065404, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111065490, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111065991, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111066649, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111067074, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750901111067442, "dur": 1248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750901111068691, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111068855, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750901111068977, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750901111069893, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111069994, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750901111070095, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750901111070895, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111070988, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750901111071082, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750901111071397, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111071498, "dur": 38982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111114819, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111114996, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111115242, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111115787, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111115980, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750901111110507, "dur": 5886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750901111116394, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111116483, "dur": 1996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750901111118523, "dur": 46960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111039043, "dur": 13577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111052678, "dur": 528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1750901111052627, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750901111053292, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111053606, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F026DDF4FF0829A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750901111053874, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750901111053934, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111054071, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750901111054386, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111054444, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750901111054543, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750901111054880, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111055872, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111056992, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111058028, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111059443, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111060489, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111062563, "dur": 1275, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\LeftShiftHandler.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750901111061593, "dur": 2817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111064410, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111065480, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111066094, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111066658, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111067080, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750901111067380, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111068123, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111067437, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750901111068375, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111068514, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750901111068629, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750901111068997, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111069117, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750901111069222, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750901111069600, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111069742, "dur": 40699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111113984, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111115091, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111115614, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111115701, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111115982, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750901111110444, "dur": 5672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750901111116117, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111116274, "dur": 1790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750901111118117, "dur": 47255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111039068, "dur": 13561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111052683, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1750901111052635, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750901111053314, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111053420, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB998ABA4D09E52A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750901111053603, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111053924, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750901111054009, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750901111054286, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750901111054679, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750901111054739, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111054809, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111056070, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111057161, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111058219, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111059587, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111060651, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111061786, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111063048, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111064008, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111065030, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111065832, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111066026, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111066678, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111067084, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750901111067262, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750901111067978, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111068198, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111068428, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111069007, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111069738, "dur": 1948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111071687, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750901111071777, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750901111072009, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111072088, "dur": 38408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111113985, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111114096, "dur": 527, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111114693, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111114918, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111115174, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111115613, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111115699, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111110498, "dur": 5275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750901111115774, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111115882, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750901111115964, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111116078, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111116305, "dur": 2033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750901111118386, "dur": 47065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111039084, "dur": 13570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111052679, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750901111052750, "dur": 555, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1750901111052668, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111053306, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111053451, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750901111053550, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750901111053667, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750901111054096, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750901111054342, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750901111054520, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750901111054620, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750901111054703, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111055921, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111058029, "dur": 1000, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Flow\\Ports\\IUnitPortWidget.cs"}}, {"pid": 12345, "tid": 9, "ts": 1750901111057118, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111059382, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111060433, "dur": 5752, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111066186, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111066665, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111067076, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111067267, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111067827, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111068155, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111068445, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111068957, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111069007, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111069738, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111069824, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111070318, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111070405, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111071468, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750901111071551, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111071644, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111072130, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750901111072197, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111072478, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750901111072755, "dur": 89875, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750901111039103, "dur": 13570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111052699, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111052770, "dur": 657, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1750901111052684, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750901111053428, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111053505, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750901111054114, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750901111054427, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750901111054569, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750901111054740, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111056037, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111057113, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111058189, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111059554, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111060706, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111061798, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111062947, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111063948, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111065410, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111065517, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111066000, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111066801, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111067127, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111068025, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111068084, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111068431, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111069005, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111069094, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111069773, "dur": 40671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111113984, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111114693, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111114784, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111114918, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111115095, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111115329, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111115702, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750901111110445, "dur": 5675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750901111116121, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111116262, "dur": 1557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111117822, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750901111117886, "dur": 47490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111039135, "dur": 13551, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111052735, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1750901111052692, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750901111053322, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111053321, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750901111053438, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111053503, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750901111053647, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750901111053788, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750901111054022, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750901111054649, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750901111054741, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111054804, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111056136, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111057210, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111058277, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111059645, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111060735, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111061828, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111063031, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111064025, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111065143, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111065841, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111065990, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111066648, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111067078, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750901111067407, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111067929, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111067468, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750901111068379, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111068498, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111068912, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111069019, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111069753, "dur": 40681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111114096, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111114692, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111114784, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111114918, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111110444, "dur": 4685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750901111115129, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750901111115697, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111115981, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111116158, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750901111115449, "dur": 2859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750901111118390, "dur": 47052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111039152, "dur": 13546, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111052749, "dur": 671, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1750901111052704, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750901111053522, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750901111053823, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750901111054075, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750901111054442, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750901111054511, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750901111054717, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111055949, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111056967, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111058092, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111059503, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111060568, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111061720, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111062942, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111063999, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111065117, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111065800, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111066022, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111066699, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111067107, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750901111067422, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750901111067911, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111068095, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111068463, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111068934, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111069013, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111069747, "dur": 40674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111113985, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111110423, "dur": 4222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750901111114646, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111114845, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111115242, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111115614, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111115701, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111116224, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750901111114824, "dur": 3086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750901111117910, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750901111118007, "dur": 47368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111038926, "dur": 13646, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111052583, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111052707, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1750901111052669, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0CA797179878B1A6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111053293, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0CA797179878B1A6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111053358, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111053357, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111053510, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111053849, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1750901111054044, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750901111054161, "dur": 462, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750901111054744, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111056109, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111057202, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111058499, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111059884, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111060961, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111062165, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111063210, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111064226, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111065208, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111065285, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111065489, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111065993, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111066695, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111067093, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111067930, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111068084, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\AssetMenu\\AssetMenuOperations.cs"}}, {"pid": 12345, "tid": 13, "ts": 1750901111068185, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\AssetOverlays\\Cache\\SearchLocks.cs"}}, {"pid": 12345, "tid": 13, "ts": 1750901111067264, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750901111068721, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111068903, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750901111069036, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111069035, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750901111069423, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111069489, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111069780, "dur": 40704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111111382, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111114996, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111110489, "dur": 4647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750901111115136, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111115389, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111115650, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111115923, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750901111115270, "dur": 2978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750901111118249, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111118333, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750901111118396, "dur": 47034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111038955, "dur": 13625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111052594, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111052707, "dur": 552, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1750901111052586, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111053325, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111053324, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111053445, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111053507, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111053626, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750901111053922, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750901111054463, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750901111054554, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750901111054674, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750901111054744, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111054802, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111056228, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111057365, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111058421, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111059845, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111060920, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111061941, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111063173, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111064239, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111065415, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111065984, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111066190, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111066652, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111067075, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111067220, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111067990, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111067274, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750901111068069, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111068260, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111068436, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750901111068560, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750901111068895, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111069032, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111069771, "dur": 40666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111113985, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111114782, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111114918, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111115089, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111115417, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111115699, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111115787, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750901111110446, "dur": 5444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750901111115890, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111116032, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111116236, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111116480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111116544, "dur": 1969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750901111118540, "dur": 46920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111038991, "dur": 13607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111052611, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111052671, "dur": 503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1750901111052605, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750901111053259, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_32492E6E491A66EE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750901111053310, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111053509, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750901111053876, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750901111054360, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111054458, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750901111054527, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750901111054638, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750901111054739, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111054807, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111056009, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111057139, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111058566, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111059949, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111061089, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111062376, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111063420, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111064454, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111065536, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111066002, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111066785, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111067120, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111068161, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111068419, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111069028, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111069764, "dur": 40738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111112261, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.Policy.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111113985, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111114096, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111114784, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111114845, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111110505, "dur": 4956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750901111115461, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111115679, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111115756, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111115821, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111115894, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750901111116228, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111116513, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111116583, "dur": 1985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750901111118608, "dur": 46783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111039022, "dur": 13586, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111052660, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1750901111052613, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750901111053314, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111053706, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111053799, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054097, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054280, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054384, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054472, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054563, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054667, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054758, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111054829, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750901111054884, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111056250, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111057339, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111058411, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111059815, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111060893, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111062234, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111063411, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111064464, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111065506, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111065996, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111066683, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111067085, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750901111067391, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750901111068235, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111068569, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750901111068681, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750901111069246, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111069775, "dur": 40685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111113833, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111114095, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111114693, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111114996, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111110461, "dur": 4629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750901111115090, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111115549, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111115613, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111115695, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111115854, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111116268, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750901111115445, "dur": 3057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750901111118503, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750901111118598, "dur": 46893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111039243, "dur": 13520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111052812, "dur": 628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1750901111052769, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8DB4C22B24A5A2EB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750901111053766, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1750901111053889, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750901111054283, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750901111054524, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750901111054729, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111056053, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111057125, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111058171, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111059562, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111060611, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111061762, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111063089, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111064073, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111065147, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111065698, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111066012, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111066737, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111067115, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111067997, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111068094, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111068482, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111068928, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111069016, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111069750, "dur": 40670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111110421, "dur": 4281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750901111114703, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750901111115422, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111115613, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111115700, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111115787, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111115922, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111116007, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111116354, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750901111114797, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750901111118536, "dur": 46929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111039262, "dur": 13510, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111052823, "dur": 629, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1750901111052778, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111053622, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_077956647DBD2B29.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111053765, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750901111053963, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111054132, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1750901111054316, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750901111054426, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111054489, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750901111054726, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111055986, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111057498, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111058552, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111059913, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111060991, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111062229, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111063255, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111064252, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111065308, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111065504, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111065998, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111066790, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111067123, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111068080, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111068382, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111068441, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111069006, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111069738, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111069998, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111070104, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750901111070974, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111071063, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111071159, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750901111071683, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111071753, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750901111072133, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750901111072370, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750901111072450, "dur": 37979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111113983, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111114693, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111114919, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115094, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115242, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115582, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115706, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115787, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111115922, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111116081, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750901111110436, "dur": 5744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750901111116181, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111116271, "dur": 1711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111117986, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750901111118047, "dur": 47326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111039281, "dur": 13500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111052816, "dur": 598, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1750901111052786, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_E966188727BA7937.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750901111053415, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111053565, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750901111053947, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750901111054184, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750901111054374, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750901111054483, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750901111054612, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750901111054713, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111055909, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111056945, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111058079, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111059464, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111060512, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111061634, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111062757, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111063804, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111064851, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111065723, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111066019, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111066719, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111067101, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750901111067451, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111067992, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111067517, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750901111068436, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111068541, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111068859, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111069029, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111069772, "dur": 40686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111113984, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111110458, "dur": 4214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750901111114673, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111114783, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111114846, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111114996, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115388, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115503, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115613, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115701, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115787, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115855, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111115923, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111116158, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750901111114744, "dur": 3758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750901111118503, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750901111118578, "dur": 46889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111039302, "dur": 13491, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111052812, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111052803, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750901111052920, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111052919, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750901111053293, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111053418, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1750901111053740, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750901111054210, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1750901111054324, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750901111054475, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750901111054753, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111056102, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111057191, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111058270, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111059678, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111060841, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111061996, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111063212, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111064226, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111065244, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111065364, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111065525, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111066007, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111066767, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111067118, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111068078, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111068385, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111068855, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750901111069005, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1750901111069372, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111069436, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111069775, "dur": 40739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111111395, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111113985, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111114693, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111114819, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111114996, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111115174, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111115387, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111115549, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111115613, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111116122, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750901111110515, "dur": 5683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750901111116199, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111116358, "dur": 2005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750901111118399, "dur": 47069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111039324, "dur": 13483, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111052819, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111052813, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8E4D74A20FBDC909.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111052898, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111053003, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1F1578B895CD76F0.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111053089, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1F1578B895CD76F0.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111053338, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111053755, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750901111054072, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1750901111054402, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111054641, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750901111054789, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111056098, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111057199, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111058252, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111059672, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111060718, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111061814, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111063087, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111064147, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111065167, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111065527, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111066003, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111066779, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111067119, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111068082, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111068413, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111068853, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111068994, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111069607, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111069777, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111069861, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111070077, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111070177, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111070271, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111070845, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750901111070930, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111071270, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111071337, "dur": 39150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111112112, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111114693, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111115097, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111115329, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111110490, "dur": 5002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111115493, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750901111115701, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111116123, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750901111115568, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750901111118603, "dur": 46874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111039346, "dur": 13471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111053022, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FB86898111D557DD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053123, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053122, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_771434CFFF9D260C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053312, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6272BEC5630D4174.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053364, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053363, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053477, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111053529, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053662, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750901111053884, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111054067, "dur": 529, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750901111054656, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750901111054735, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111054803, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111056218, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111057258, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111058306, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111059623, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111060703, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111061808, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111063250, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111064307, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111064723, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111065718, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111066019, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111066706, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111067092, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111067277, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750901111067890, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111068038, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750901111068094, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111068490, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111068920, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111069017, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111069751, "dur": 40759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111113984, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111110516, "dur": 4205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750901111114722, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750901111115141, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111115329, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111115503, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111115650, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111116081, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750901111114790, "dur": 3667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750901111118532, "dur": 46930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111039364, "dur": 13462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111053020, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9E74BE77200C0976.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750901111053323, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1750901111053281, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750901111053420, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750901111053610, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111053857, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750901111054350, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750901111054632, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750901111054752, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111054819, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111056185, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111057264, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111058316, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111059682, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111060745, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111061881, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111063556, "dur": 882, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\OnScreen\\OnScreenButton.cs"}}, {"pid": 12345, "tid": 23, "ts": 1750901111063050, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111064947, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111065744, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111066015, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111066731, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111067133, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111068005, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111068087, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111068527, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111068868, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111069028, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111069762, "dur": 40690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111113925, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750901111114693, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750901111114994, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750901111115328, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750901111115549, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750901111110480, "dur": 5292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750901111115773, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111115878, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750901111116000, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111116288, "dur": 1961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750901111118291, "dur": 47118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111039385, "dur": 13449, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111052908, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111052907, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_4E866FA7B00374E5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053062, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053117, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053115, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_04DBE999BFBDB12D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053217, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_04DBE999BFBDB12D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053321, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053276, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_0A7BB341A7CF361B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053422, "dur": 556, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_0A7BB341A7CF361B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111053985, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111054634, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111054740, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111054796, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111054852, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055042, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055243, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055352, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055449, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055507, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055565, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055642, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055821, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111055909, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056077, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056160, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056232, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056298, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056354, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056434, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056514, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056568, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056633, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111056700, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057221, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057282, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057335, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057436, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057509, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057574, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057640, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057709, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057781, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057870, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057929, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111057989, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058106, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058174, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058225, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058327, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058381, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058434, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058494, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058588, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111058664, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111059040, "dur": 834, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111059890, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111059954, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060060, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060118, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060170, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060269, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060518, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060578, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060635, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060741, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060832, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060883, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111060936, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061004, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061056, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061114, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061170, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061242, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061346, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061429, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061535, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061629, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061897, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111061952, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062043, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062203, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062256, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062308, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062413, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062638, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062706, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062773, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062829, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062880, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111062948, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063013, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063113, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063173, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063285, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063526, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063577, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063691, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063742, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063843, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063931, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111063988, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064041, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064095, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064155, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064352, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064407, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064464, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064612, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064715, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064868, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064926, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111064982, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111065076, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111065179, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111065238, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111065382, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 24, "ts": 1750901111054095, "dur": 11780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111066042, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111066113, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111066671, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111066740, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111067073, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111067928, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111067497, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111068435, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750901111068562, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111068921, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111068994, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1750901111069580, "dur": 64, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111070091, "dur": 36049, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1750901111114094, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111114820, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111114919, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111115141, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111110473, "dur": 5026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111115499, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111115582, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111115700, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111115787, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111116157, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750901111115566, "dur": 2688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750901111118254, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750901111118367, "dur": 47052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111039408, "dur": 13435, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111052916, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111052915, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053019, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053082, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053165, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053241, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053240, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_219436DA005DE56D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053295, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111053417, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_219436DA005DE56D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053570, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_0CBB1EE9BC1FA429.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053864, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1750901111053981, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750901111054116, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750901111054476, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750901111054607, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750901111054771, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111056068, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111057152, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111058296, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111059666, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111060819, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111061938, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111063246, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111064270, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111065328, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111065485, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111065985, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111066650, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111067077, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111067260, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750901111068054, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111068208, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111068412, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111068854, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750901111069035, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111068976, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750901111069376, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111069453, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111069787, "dur": 40681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111110812, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111110950, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111114751, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111110480, "dur": 4798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115279, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111115389, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115613, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115701, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115784, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115922, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111116122, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750901111115345, "dur": 2919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750901111118265, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750901111118348, "dur": 47052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111039434, "dur": 13416, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111053260, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750901111053319, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111053319, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750901111053460, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750901111053650, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111053973, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111054037, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1750901111054361, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111054488, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750901111054693, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111055945, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111057016, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111058045, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111059482, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111060583, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111061704, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111062870, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111063891, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111064920, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111065795, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111066035, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111066672, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111067121, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111068088, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111068517, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111068875, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111069024, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111069768, "dur": 40688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111114785, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111114918, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111114994, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111115242, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111110457, "dur": 5056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750901111115514, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111115591, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111115665, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111115857, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750901111116045, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111116098, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1750901111116220, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111116344, "dur": 1996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750901111118375, "dur": 47061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111039189, "dur": 13547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111052785, "dur": 595, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1750901111052742, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750901111053409, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750901111053624, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750901111054008, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750901111054392, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750901111054472, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750901111054642, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750901111054861, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111056124, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111057168, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111058521, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111060032, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111061261, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111062575, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111064370, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\Views\\Diff\\GetClientDiffInfos.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750901111063607, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111065217, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111065286, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111065470, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111066016, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111066726, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111067104, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750901111067929, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111067285, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750901111067982, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111068099, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111068472, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111068941, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111069010, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111069741, "dur": 40685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111110428, "dur": 3512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750901111113941, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750901111114694, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111115141, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111115329, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111115386, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111115502, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111115613, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750901111114128, "dur": 3633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750901111117836, "dur": 47542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111039204, "dur": 13542, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111052795, "dur": 597, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 28, "ts": 1750901111052752, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AAB6357F3297AC29.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750901111053438, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111053624, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750901111053928, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750901111054116, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 28, "ts": 1750901111054653, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750901111054751, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111054841, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111056135, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111057200, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111058280, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111059611, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111060767, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111062252, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111063294, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111064275, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111065541, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111066005, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111066773, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111067116, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111067998, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111068076, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111068385, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111068856, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111069032, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111069779, "dur": 40698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111113267, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111113985, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111114691, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111114819, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111115096, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111115550, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111115701, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111115923, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111116033, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111116124, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750901111110488, "dur": 5915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750901111116403, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111116513, "dur": 1980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750901111118526, "dur": 46929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111039227, "dur": 13528, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111052804, "dur": 600, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 29, "ts": 1750901111052760, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_661C6E6134F0042E.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750901111053576, "dur": 582, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750901111054192, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 29, "ts": 1750901111054396, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750901111054585, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750901111054849, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111056215, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111057291, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111058344, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111059826, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111060979, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111062404, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111063486, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111064496, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111065576, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111066008, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111066761, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111067112, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111067998, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111068092, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111068499, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111068906, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111069020, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111069756, "dur": 40751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111113987, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111114693, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111110508, "dur": 4271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750901111114780, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750901111115096, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111115241, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111115547, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111115699, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111115787, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111115922, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111116033, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111116201, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111116414, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750901111114853, "dur": 3333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750901111118269, "dur": 47135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111039513, "dur": 13399, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111052934, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111052924, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750901111053021, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750901111053220, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_72EF51AA8584F47D.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750901111053308, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111053621, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 30, "ts": 1750901111053980, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750901111054202, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750901111054320, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750901111054466, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750901111054628, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750901111054737, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111056021, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111057134, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111058201, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111059810, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111060874, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111061923, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111063082, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111064120, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111065227, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111065345, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111065515, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111065997, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111066795, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111067124, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111068142, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111068454, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111068949, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111069011, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111069746, "dur": 40678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111110534, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111113983, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111114095, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111114693, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111114918, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111114995, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111115096, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111115549, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111115701, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750901111110426, "dur": 5474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750901111115900, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111116117, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111116238, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111116300, "dur": 2031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750901111118365, "dur": 47043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111039531, "dur": 13398, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111052953, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111052939, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9118DBDC897F9C38.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750901111053292, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111053526, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750901111053916, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750901111054384, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 31, "ts": 1750901111054616, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750901111054767, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111056171, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111057239, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111058294, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111059623, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111060714, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111061851, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111063003, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111063992, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111065077, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111065802, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111066024, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111066693, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111067109, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750901111067478, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111067917, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111067543, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750901111068372, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111068506, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111068891, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111069022, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111069759, "dur": 40680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111110697, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111111063, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111112437, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111113985, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.Windows.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111114692, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111115140, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111115502, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111115582, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111115650, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111115922, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750901111110440, "dur": 5945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750901111116385, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111116551, "dur": 2023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750901111118596, "dur": 46901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111039549, "dur": 13393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111053137, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0841EB48B73D0C6A.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750901111053220, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0841EB48B73D0C6A.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750901111053295, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111053438, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111053500, "dur": 713, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750901111054243, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750901111054347, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750901111054470, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750901111054732, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111055955, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111057050, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111058121, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111059527, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111060570, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111061736, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111063016, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111064447, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111065602, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111066009, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111066749, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111067111, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111067999, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111068088, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111068508, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111068883, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111069023, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111069767, "dur": 40714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111111380, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750901111111982, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750901111114994, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750901111115700, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750901111115823, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750901111110486, "dur": 5467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750901111115954, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111116063, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111116239, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111116832, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750901111116894, "dur": 48486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750901111170748, "dur": 2910, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 47468, "tid": 112, "ts": 1750901111174288, "dur": 858, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 47468, "tid": 112, "ts": 1750901111175187, "dur": 3455, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 47468, "tid": 112, "ts": 1750901111173520, "dur": 5144, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
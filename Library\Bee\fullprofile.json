{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 47468, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 47468, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 47468, "tid": 649, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 47468, "tid": 649, "ts": 1750906237275962, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237275982, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 47468, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 47468, "tid": 1, "ts": 1750906237115912, "dur": 967, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750906237116880, "dur": 14965, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 47468, "tid": 1, "ts": 1750906237131847, "dur": 86027, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237275985, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 47468, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237115888, "dur": 9082, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237124971, "dur": 150520, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237124978, "dur": 49, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237125031, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237125218, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237125248, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237125259, "dur": 2289, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127552, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127588, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127589, "dur": 29, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127620, "dur": 25, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127649, "dur": 27, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127677, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127679, "dur": 30, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127712, "dur": 22, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127738, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127766, "dur": 23, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127791, "dur": 35, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127828, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127853, "dur": 40, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127897, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127920, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127944, "dur": 25, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127971, "dur": 21, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237127995, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128018, "dur": 31, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128051, "dur": 16, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128069, "dur": 26, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128096, "dur": 23, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128124, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128148, "dur": 20, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128172, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128193, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128212, "dur": 16, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128229, "dur": 16, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128247, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128268, "dur": 20, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128290, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128311, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128331, "dur": 16, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128348, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128367, "dur": 18, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128386, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128406, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128426, "dur": 28, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128456, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128476, "dur": 20, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128498, "dur": 18, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128517, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128534, "dur": 17, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128552, "dur": 15, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128570, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128589, "dur": 14, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128606, "dur": 17, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128624, "dur": 16, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128642, "dur": 65, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128711, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128734, "dur": 18, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128754, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128773, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128793, "dur": 16, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128811, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128829, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128850, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128851, "dur": 16, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128869, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128889, "dur": 16, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128907, "dur": 15, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128924, "dur": 17, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128943, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237128961, "dur": 74, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129038, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129059, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129080, "dur": 18, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129101, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129119, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129139, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129157, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129175, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129192, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129221, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129244, "dur": 19, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129266, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129291, "dur": 18, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129311, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129328, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129345, "dur": 17, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129365, "dur": 16, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129382, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129400, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129418, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129436, "dur": 14, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129452, "dur": 56, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129510, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129535, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129537, "dur": 16, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129555, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129576, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129592, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129609, "dur": 14, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129624, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129640, "dur": 18, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129661, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129678, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129700, "dur": 14, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129717, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129741, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129772, "dur": 15, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129789, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129807, "dur": 17, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129826, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129845, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129863, "dur": 16, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129881, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129902, "dur": 16, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129920, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129937, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129955, "dur": 31, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237129988, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130010, "dur": 16, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130028, "dur": 15, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130045, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130064, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130082, "dur": 16, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130100, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130119, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130137, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130155, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130175, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130192, "dur": 15, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130209, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130226, "dur": 15, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130243, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130261, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130282, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130299, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130316, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130332, "dur": 15, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130348, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130367, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130390, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130409, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130430, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130450, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130470, "dur": 14, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130486, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130502, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130503, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130521, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130538, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130539, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130565, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130581, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130599, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130617, "dur": 16, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130635, "dur": 14, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130650, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130666, "dur": 16, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130684, "dur": 34, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130719, "dur": 16, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130737, "dur": 15, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130754, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130770, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130788, "dur": 16, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130806, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130824, "dur": 16, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130841, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130858, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130875, "dur": 14, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130892, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130913, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130914, "dur": 17, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130934, "dur": 15, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130950, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130967, "dur": 15, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237130985, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131005, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131023, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131041, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131043, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131062, "dur": 15, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131078, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131097, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131115, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131138, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131158, "dur": 16, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131176, "dur": 15, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131193, "dur": 14, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131208, "dur": 14, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131224, "dur": 15, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131240, "dur": 18, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131261, "dur": 14, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131277, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131297, "dur": 15, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131314, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131330, "dur": 27, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131360, "dur": 18, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131380, "dur": 40, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131421, "dur": 19, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131442, "dur": 15, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131459, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131460, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131484, "dur": 22, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131507, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131509, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131527, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131547, "dur": 18, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131566, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131588, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131609, "dur": 14, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131624, "dur": 18, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131644, "dur": 15, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131660, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131663, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131687, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131707, "dur": 18, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131728, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131753, "dur": 18, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131773, "dur": 113, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131888, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131917, "dur": 1, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131918, "dur": 21, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131943, "dur": 15, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131959, "dur": 32, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237131993, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237132018, "dur": 15, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237132037, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237132056, "dur": 15, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237132072, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135157, "dur": 197, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135355, "dur": 9, "ph": "X", "name": "ProcessMessages 20536", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135366, "dur": 23, "ph": "X", "name": "ReadAsync 20536", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135391, "dur": 17, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135410, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135426, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135448, "dur": 15, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135465, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135482, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135498, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135515, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135532, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135548, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135565, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135583, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135599, "dur": 15, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135615, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135634, "dur": 20, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135656, "dur": 16, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135673, "dur": 18, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135694, "dur": 18, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135713, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135729, "dur": 17, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135748, "dur": 15, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135764, "dur": 15, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135781, "dur": 14, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135796, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135813, "dur": 15, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135829, "dur": 17, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135848, "dur": 17, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135866, "dur": 15, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135883, "dur": 14, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135900, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135918, "dur": 15, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135935, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135958, "dur": 27, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237135987, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136010, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136031, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136052, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136069, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136090, "dur": 15, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136107, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136126, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136143, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136161, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136181, "dur": 50, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136233, "dur": 72, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136318, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136323, "dur": 336, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136662, "dur": 2, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136664, "dur": 110, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136776, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136778, "dur": 23, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136803, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136824, "dur": 11, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136847, "dur": 21, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136870, "dur": 19, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136891, "dur": 25, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136918, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136944, "dur": 26, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237136973, "dur": 32, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137007, "dur": 24, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137033, "dur": 25, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137061, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137085, "dur": 21, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137108, "dur": 19, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137129, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137147, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137168, "dur": 15, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137186, "dur": 18, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137206, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137227, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137246, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137263, "dur": 16, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137280, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137302, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137320, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137342, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137364, "dur": 18, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137384, "dur": 16, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137402, "dur": 15, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137419, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137436, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137453, "dur": 15, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137469, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137486, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137503, "dur": 13, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137518, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137550, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137569, "dur": 17, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137588, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137606, "dur": 69, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137676, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137698, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137717, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137736, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137791, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137811, "dur": 28, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137841, "dur": 16, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137858, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137909, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137931, "dur": 15, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137947, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237137965, "dur": 66, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138033, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138055, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138074, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138089, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138118, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138137, "dur": 53, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138192, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138211, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138238, "dur": 14, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138254, "dur": 52, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138308, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138354, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138374, "dur": 60, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138436, "dur": 64, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138501, "dur": 15, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138550, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138614, "dur": 20, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138674, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138749, "dur": 23, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138773, "dur": 10, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138834, "dur": 20, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138856, "dur": 68, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138926, "dur": 16, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237138958, "dur": 65, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139046, "dur": 20, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139104, "dur": 21, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139127, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139237, "dur": 22, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139284, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139327, "dur": 14, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139342, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139359, "dur": 38, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139399, "dur": 54, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139486, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139488, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139541, "dur": 16, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139559, "dur": 65, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139625, "dur": 15, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139651, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139672, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139796, "dur": 1, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139798, "dur": 23, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139867, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139934, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139936, "dur": 23, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139982, "dur": 1, "ph": "X", "name": "ProcessMessages 1575", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237139983, "dur": 26, "ph": "X", "name": "ReadAsync 1575", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140038, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140086, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140131, "dur": 16, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140148, "dur": 46, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140198, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140251, "dur": 1, "ph": "X", "name": "ProcessMessages 1742", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140252, "dur": 18, "ph": "X", "name": "ReadAsync 1742", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140272, "dur": 94, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140399, "dur": 14, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140415, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140473, "dur": 69, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140545, "dur": 44, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140591, "dur": 15, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140607, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140640, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140676, "dur": 22, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140750, "dur": 58, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140810, "dur": 17, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140829, "dur": 59, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140890, "dur": 19, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140940, "dur": 20, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237140986, "dur": 55, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141042, "dur": 67, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141112, "dur": 89, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141203, "dur": 20, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141251, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141252, "dur": 17, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141292, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141356, "dur": 19, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141376, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141394, "dur": 56, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141459, "dur": 23, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141510, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141561, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141563, "dur": 20, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141604, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141628, "dur": 14, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141643, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141693, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141748, "dur": 19, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141776, "dur": 26, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141804, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141813, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141865, "dur": 33, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237141906, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142049, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142050, "dur": 62, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142114, "dur": 1, "ph": "X", "name": "ProcessMessages 1647", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142115, "dur": 53, "ph": "X", "name": "ReadAsync 1647", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142170, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142229, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142231, "dur": 16, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142285, "dur": 20, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142306, "dur": 14, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142368, "dur": 17, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142423, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142442, "dur": 39, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142482, "dur": 24, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142561, "dur": 34, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142596, "dur": 1, "ph": "X", "name": "ProcessMessages 1856", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142597, "dur": 13, "ph": "X", "name": "ReadAsync 1856", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142643, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142660, "dur": 69, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142732, "dur": 60, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142793, "dur": 50, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142845, "dur": 1, "ph": "X", "name": "ProcessMessages 1767", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142846, "dur": 22, "ph": "X", "name": "ReadAsync 1767", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142907, "dur": 15, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142923, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237142986, "dur": 71, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143098, "dur": 1, "ph": "X", "name": "ProcessMessages 1317", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143100, "dur": 125, "ph": "X", "name": "ReadAsync 1317", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143227, "dur": 2, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143230, "dur": 111, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143373, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143375, "dur": 52, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143476, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143607, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143705, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143707, "dur": 115, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143825, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143827, "dur": 122, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237143952, "dur": 51, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144005, "dur": 116, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144124, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144128, "dur": 123, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144268, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144272, "dur": 129, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144404, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144407, "dur": 147, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144568, "dur": 4, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144622, "dur": 113, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144737, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144741, "dur": 45, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144793, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144796, "dur": 53, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144851, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144854, "dur": 56, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144913, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144915, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144966, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237144968, "dur": 58, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145027, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145029, "dur": 32, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145064, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145123, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145126, "dur": 69, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145219, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145222, "dur": 56, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145280, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145282, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145328, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145329, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145374, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145376, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145420, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145422, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145455, "dur": 29, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145486, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237145507, "dur": 11552, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157077, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157105, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157106, "dur": 34, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157142, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157165, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157196, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157402, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157466, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157468, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157509, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237157539, "dur": 3570, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237161113, "dur": 149, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237161349, "dur": 7, "ph": "X", "name": "ProcessMessages 1684", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237161357, "dur": 492, "ph": "X", "name": "ReadAsync 1684", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237161950, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237161952, "dur": 414, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237162404, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237162405, "dur": 112, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237162677, "dur": 41, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237162719, "dur": 104, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237162868, "dur": 166, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237163100, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237163271, "dur": 46, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237163358, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237163504, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237163505, "dur": 48554, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237212067, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237212070, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237212100, "dur": 27, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237212127, "dur": 4534, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237216666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237216669, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237216714, "dur": 328, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217045, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217067, "dur": 353, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217423, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217480, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217529, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217531, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217556, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217602, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217708, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217710, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217766, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217769, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237217804, "dur": 321, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218128, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218181, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218182, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218231, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218262, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218303, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218343, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218345, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218375, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218397, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218447, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218480, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218545, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218590, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218616, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218649, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218651, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218711, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218765, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218798, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218837, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218868, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218901, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218943, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237218987, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219032, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219034, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219061, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219100, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219102, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219145, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219174, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219176, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219225, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219226, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219264, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219265, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219317, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219354, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219357, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219401, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219403, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219475, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219516, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219549, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219585, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219630, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219682, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219685, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219717, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219719, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219750, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219754, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237219785, "dur": 1185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237220973, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237220986, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221020, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221063, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221106, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221107, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221145, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221278, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221312, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221337, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221363, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221406, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221445, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221447, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221480, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221515, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221552, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221583, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221608, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221633, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221634, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221667, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221690, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221722, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221756, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221757, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221780, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237221802, "dur": 40396, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237262205, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237262209, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237262232, "dur": 99, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237262332, "dur": 4337, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237266672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237266674, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 47468, "tid": 68719476736, "ts": 1750906237266694, "dur": 8792, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237275992, "dur": 552, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 47468, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 47468, "tid": 64424509440, "ts": 1750906237115859, "dur": 102020, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 47468, "tid": 64424509440, "ts": 1750906237217880, "dur": 28, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237276546, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 47468, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 47468, "tid": 60129542144, "ts": 1750906237113508, "dur": 162012, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 47468, "tid": 60129542144, "ts": 1750906237113589, "dur": 1788, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 47468, "tid": 60129542144, "ts": 1750906237275523, "dur": 39, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 47468, "tid": 60129542144, "ts": 1750906237275537, "dur": 12, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 47468, "tid": 60129542144, "ts": 1750906237275564, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237276554, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750906237124569, "dur": 1316, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237125893, "dur": 537, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237126562, "dur": 572, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237128268, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8517F10BC8FA0755.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750906237128588, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750906237132540, "dur": 2444, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750906237135974, "dur": 260, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750906237127148, "dur": 15646, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237142804, "dur": 122238, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237265048, "dur": 208, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237265303, "dur": 56, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237265359, "dur": 111, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237266287, "dur": 1287, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750906237126955, "dur": 15879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237142849, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237142937, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1750906237142841, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143194, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237143276, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143275, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143359, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143430, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143429, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4796D0C7D217714A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143557, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237143666, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_AC5852D97D0872BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237143916, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750906237144078, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750906237144312, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750906237144483, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750906237144860, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237146034, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237147077, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237148195, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237149678, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237150681, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237151720, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237152702, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237153670, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237154544, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237155439, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237155819, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237156496, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237156949, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750906237157968, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237158053, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237157270, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750906237158202, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237158311, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237158632, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237158873, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237159622, "dur": 2597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237162220, "dur": 50889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237213215, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237216015, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237216613, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237216921, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237217041, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237217182, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237217682, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237217914, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237218053, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237218338, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237218650, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237218808, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750906237213112, "dur": 5942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750906237219055, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237219187, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237219247, "dur": 1887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750906237221175, "dur": 43930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237126905, "dur": 15909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237142833, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237142967, "dur": 519, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1750906237142932, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750906237143558, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237143676, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750906237143907, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237144004, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750906237144060, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750906237144417, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237144561, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237144772, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145002, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145070, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145174, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145347, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145505, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145567, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145718, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145820, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145918, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237145969, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146030, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146140, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146401, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146460, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146643, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146700, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146759, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146811, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146909, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237146970, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147070, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147149, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147219, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147280, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147345, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147445, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147543, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147596, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147681, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147744, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237147809, "dur": 693, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237148503, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237148556, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237148653, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237148808, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237149184, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237149299, "dur": 591, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237149899, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237149957, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237150107, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237150370, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237150428, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237150487, "dur": 480, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237150992, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237151098, "dur": 691, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237151809, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237151935, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152050, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152245, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152402, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152507, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152562, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152617, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152718, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152829, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152885, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237152986, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153084, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153181, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153453, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153509, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153653, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153846, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153901, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237153994, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237154051, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237154109, "dur": 686, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237154825, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237155099, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237155192, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237155260, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750906237144210, "dur": 11254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750906237155464, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237155577, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237155800, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237156429, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237157089, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237158020, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237158192, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237158619, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237158951, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237159611, "dur": 2597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237162208, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750906237162309, "dur": 50803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237213259, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237216014, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237216920, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237217183, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237217889, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218139, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218224, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218451, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218611, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218733, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750906237213113, "dur": 5739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750906237218852, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237218966, "dur": 1726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237220744, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750906237221382, "dur": 43739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237127278, "dur": 15732, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237143068, "dur": 585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1750906237143018, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_661C6E6134F0042E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750906237143654, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237143771, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750906237143897, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750906237144087, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237144146, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750906237144301, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750906237144605, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750906237144948, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237146552, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\IRowGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750906237146171, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237148018, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237149459, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237150454, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237151461, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237152627, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237153698, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237154626, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237155585, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237155835, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237156404, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237156843, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750906237157016, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750906237157912, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237158095, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237158181, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237158617, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237158958, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237159612, "dur": 2597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237162209, "dur": 50845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237216015, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237216569, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237217040, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237217340, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237217683, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237217913, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237218280, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750906237213056, "dur": 5585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750906237218641, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237218838, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237219281, "dur": 1932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750906237221350, "dur": 43724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237127029, "dur": 15833, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237142881, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237142934, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1750906237142872, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750906237143417, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237143467, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750906237143566, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237143744, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750906237143910, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750906237144073, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750906237144383, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750906237144495, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750906237144833, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750906237144922, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237144988, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237146251, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237147568, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237148617, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237149954, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237150965, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237152006, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237153014, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237154062, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750906237153959, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237155347, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237155820, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237156462, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237156991, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237158058, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237158163, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237158625, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237158914, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237159632, "dur": 2598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237162230, "dur": 50898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237216614, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237213129, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750906237216884, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237217125, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237217271, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237217682, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237218185, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237219216, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750906237217104, "dur": 4069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750906237221173, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750906237221275, "dur": 43788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237127377, "dur": 15683, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237143104, "dur": 613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1750906237143066, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237143747, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237144063, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237144418, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237144513, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237144565, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145057, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145155, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145388, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145590, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145647, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145746, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145797, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237145940, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237146049, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237146148, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237146301, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237146406, "dur": 734, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147154, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147222, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147331, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147410, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147628, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147680, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147747, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237147809, "dur": 697, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237148512, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237148634, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237148788, "dur": 383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237149212, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237149304, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237149740, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237149928, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237149983, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150084, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150374, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150426, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150487, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150592, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150697, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150802, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237150861, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151074, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151273, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151373, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151478, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151590, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151646, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151703, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151797, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237151944, "dur": 2516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237154713, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237154772, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237154841, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237155013, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237155192, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237155296, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750906237144198, "dur": 11487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237155851, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237155920, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237156429, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237156497, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237156840, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237157018, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237157078, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237157665, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237157785, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750906237158027, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237158478, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237158630, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1750906237159016, "dur": 100, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237159477, "dur": 49391, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1750906237216570, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237216861, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237217126, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750906237213104, "dur": 4454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750906237217558, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237217747, "dur": 3708, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750906237221455, "dur": 43652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237127443, "dur": 15649, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237143360, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_62E3103B1EFE0DCE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237143419, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0CACEFF522BB87D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237143546, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237143647, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DB6E6E7F0102E05A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237143880, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144146, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144288, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144451, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144597, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144683, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144806, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750906237144895, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237146065, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237147178, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237148289, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237149654, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237150813, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237151927, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237152937, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237153834, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237154775, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237154844, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237154926, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237155802, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237156556, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237156858, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237157104, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750906237158139, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237158273, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237158399, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750906237158865, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237159606, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750906237159706, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750906237159980, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237160055, "dur": 2179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237162234, "dur": 50866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237216015, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237216568, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237213101, "dur": 4654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750906237217755, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237217868, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237217943, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218053, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218185, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218280, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218505, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218611, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218691, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237218808, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237219027, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237219175, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750906237217843, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750906237221262, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750906237221352, "dur": 43723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237127060, "dur": 15824, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237142944, "dur": 443, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1750906237142893, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750906237143563, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237143671, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB998ABA4D09E52A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750906237143747, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750906237143910, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750906237144161, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750906237144302, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750906237144692, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750906237144879, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750906237145047, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237146347, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237148488, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Drawing\\Views\\Slots\\Texture3DSlotControlView.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750906237147448, "dur": 2110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237149559, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237150545, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237151564, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237152690, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237153630, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237154542, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237155409, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237155823, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237156451, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237156983, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237158012, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237158161, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237158597, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237158839, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237159609, "dur": 1868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237161478, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750906237161566, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750906237161823, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237162275, "dur": 50840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237213196, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237216015, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237217269, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237217604, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237217682, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237217770, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237217869, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237218143, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237218280, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237218485, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237218563, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750906237213116, "dur": 5521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750906237218638, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237218721, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237218847, "dur": 1750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237220602, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237220695, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750906237221336, "dur": 43783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237127546, "dur": 15596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237143183, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237143533, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237143657, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_219436DA005DE56D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750906237143763, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_0CBB1EE9BC1FA429.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750906237143928, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750906237144578, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750906237144884, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237144936, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750906237144999, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237146269, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237148467, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Generation\\Targets\\PreviewTarget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750906237147343, "dur": 1866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237149210, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237150289, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237151368, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237152371, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237153373, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237154485, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237155422, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237155826, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237156437, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237157048, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237158163, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237158627, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237158898, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237159631, "dur": 2595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237162226, "dur": 50836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237216613, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237216977, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217268, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217339, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217604, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217770, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237213063, "dur": 4798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217861, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237217999, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237217998, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237218052, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237218205, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237218284, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237218283, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750906237218530, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237218647, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237218781, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237218842, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237219249, "dur": 1905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750906237221232, "dur": 43837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237127586, "dur": 15585, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237143187, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237143180, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_DF935DB001343E8A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750906237143555, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237143664, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750906237144067, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750906237144309, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750906237144749, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750906237144923, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237146117, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237147188, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237148319, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237149731, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237151070, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237152093, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237153362, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237154290, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237155190, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237155800, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237156562, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237156851, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750906237157185, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750906237157944, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237158091, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237158147, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750906237158294, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750906237158899, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237158980, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237159621, "dur": 2595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237162216, "dur": 50849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237216015, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237216271, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237216570, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237216922, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237217126, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237217603, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237213066, "dur": 4693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750906237217760, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237217914, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237217994, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218053, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218280, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218404, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218608, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218885, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237218953, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750906237217896, "dur": 3416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750906237221313, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750906237221408, "dur": 43678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237127139, "dur": 15787, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237142983, "dur": 524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1750906237142934, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5AB36D953142D0D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750906237143549, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237143658, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1750906237143729, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237143784, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750906237144136, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750906237144301, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1750906237144620, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750906237144905, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237146047, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237147089, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237148205, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237149679, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237150733, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237151799, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237152846, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237154072, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\AssetsUtils\\LoadAsset.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750906237153783, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237155368, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237155825, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237156445, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237157029, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237158052, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237158158, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237158600, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237158837, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750906237158971, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750906237159260, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237159344, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237159625, "dur": 2614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237162239, "dur": 53781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237216245, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237216569, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237216860, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237217124, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237217340, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237217868, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237217943, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237218184, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237218453, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237218531, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpsPolicy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237218649, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237218733, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Protocols.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237219175, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750906237216025, "dur": 4781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750906237220808, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237220910, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750906237221458, "dur": 43636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237127655, "dur": 15555, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237143215, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237143211, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9118DBDC897F9C38.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750906237143582, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237143772, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_13EB1DAF647B9A69.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750906237143853, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_13EB1DAF647B9A69.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144150, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144263, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144560, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144652, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144752, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750906237144966, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237146269, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237147373, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237148403, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237149869, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237150962, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237152070, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237153077, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237153983, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237154311, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237155266, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237155806, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237156535, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237156890, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750906237157164, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750906237158554, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237158637, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750906237158719, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750906237158947, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237158998, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237159628, "dur": 2589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237162218, "dur": 50849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237213849, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237216570, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237217183, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237217271, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237217339, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218142, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218293, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218405, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218485, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218691, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750906237213068, "dur": 5782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750906237218851, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237218998, "dur": 1894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750906237220947, "dur": 44096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237127200, "dur": 15768, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237142984, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237143044, "dur": 637, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1750906237142977, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750906237143734, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750906237144079, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750906237144237, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750906237144805, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6591615775254759062.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750906237145058, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237146307, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237147412, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237148707, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237150092, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237151128, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237152140, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237153142, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237154182, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237155139, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237155796, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237156402, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237156842, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750906237157041, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237157099, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750906237158006, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237158198, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237158623, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237158929, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237159623, "dur": 2601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237162224, "dur": 53817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237216244, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237216861, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237216922, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237217125, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237217271, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237217682, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237217996, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237218142, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-time-l1-1-0.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237218280, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237218691, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237219176, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750906237216042, "dur": 4406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750906237220448, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237220634, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750906237221326, "dur": 43742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237127212, "dur": 15768, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237143041, "dur": 646, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1750906237142987, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750906237143688, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237143739, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750906237143937, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750906237144151, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750906237144666, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750906237144821, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750906237144951, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750906237145023, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237146340, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237147417, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237148457, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237149967, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237151041, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237152056, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237153481, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237154701, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237155605, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237155828, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237156424, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237157057, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237158102, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237158169, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237158615, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237158965, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237159610, "dur": 2600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237162210, "dur": 50839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237215199, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237216242, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237216570, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237216858, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237216975, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237213051, "dur": 4210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750906237217261, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237217681, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237217868, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237218356, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237218563, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237218691, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237219090, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750906237217409, "dur": 3655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750906237221065, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750906237221162, "dur": 43889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237127232, "dur": 15758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237143039, "dur": 623, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1750906237142996, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750906237143903, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237144154, "dur": 387, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750906237144604, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750906237145118, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237146282, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237147386, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237149575, "dur": 1026, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Graphs\\PreviewMode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1750906237148382, "dur": 2322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237150704, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237151825, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237152816, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237153767, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237154771, "dur": 88, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237154860, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237154937, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237155792, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237156436, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237157013, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237158093, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237158165, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237158606, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237158997, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237159642, "dur": 2589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237162231, "dur": 50841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237215252, "dur": 438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750906237216016, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750906237217125, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750906237217271, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750906237217339, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750906237213073, "dur": 4862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750906237217936, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218062, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218190, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218258, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218585, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218685, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237218845, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237219364, "dur": 1933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750906237221365, "dur": 43732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237127255, "dur": 15745, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237143046, "dur": 600, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1750906237143005, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AAB6357F3297AC29.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750906237143648, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237144043, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144104, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237144155, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144268, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237144460, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144686, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144787, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144928, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750906237144990, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237146364, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237147426, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237148420, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237149799, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237150783, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237151848, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237152861, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237153783, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237154713, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237155355, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237155834, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237156412, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237156838, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750906237156974, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237157049, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237158674, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237158838, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750906237158947, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237159750, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237159837, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750906237159945, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237160852, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237160930, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750906237161023, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237161348, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237161427, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237162259, "dur": 50845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237216014, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237213106, "dur": 3094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237216200, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237216570, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237216861, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237216976, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237217270, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237217681, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237217770, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237217942, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237218054, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237218279, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237218484, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237219175, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Abstractions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750906237216281, "dur": 4797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750906237221079, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750906237221183, "dur": 43871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237126944, "dur": 15881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237142835, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237142957, "dur": 510, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1750906237142922, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0CA797179878B1A6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750906237143557, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237143667, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750906237143881, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750906237144125, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750906237144502, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750906237144727, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750906237144882, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237144945, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237146145, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237147294, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237148343, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237150001, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237151063, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237152110, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237153178, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237154153, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237155316, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237155812, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237156503, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237156932, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750906237157247, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750906237158258, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237158368, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237158603, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237159004, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237159624, "dur": 2587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237162212, "dur": 50836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237216860, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237217042, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237213049, "dur": 4185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750906237217235, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237217868, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237218143, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237218405, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237218563, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237218886, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237218950, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237219216, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237219274, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750906237217357, "dur": 3520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750906237220877, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750906237221015, "dur": 44046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237127305, "dur": 15721, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237143082, "dur": 583, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1750906237143037, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8DB4C22B24A5A2EB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237143713, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750906237143764, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237143889, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750906237144035, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237144144, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1750906237144279, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750906237144595, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750906237144774, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750906237144899, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237144957, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237146238, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237147319, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237148514, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237149873, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237150874, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237151982, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237153009, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237153974, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237154980, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237155814, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237156509, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237156939, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237157813, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750906237157314, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237158250, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237158349, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237158636, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237158850, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237159607, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237159743, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237160297, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750906237160394, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237160493, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237161641, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237161706, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237162216, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750906237162334, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237162612, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750906237162887, "dur": 98914, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750906237127324, "dur": 15717, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237143088, "dur": 589, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1750906237143047, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750906237143728, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237143792, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750906237143879, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750906237144109, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237144242, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750906237144479, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750906237144568, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750906237144791, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750906237144974, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237146439, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237147578, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237149531, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Runtime\\Passes\\DrawSkyboxPass.cs"}}, {"pid": 12345, "tid": 18, "ts": 1750906237148616, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237150551, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237151567, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237152566, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237153534, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237154444, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237155470, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237155818, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237156482, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237156956, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750906237157689, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237157275, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750906237158138, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237158266, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237158630, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237158883, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237159640, "dur": 2592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237162232, "dur": 50857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237216016, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237216244, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237216860, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237216976, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237217042, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237213090, "dur": 4160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750906237217250, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237217339, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237217867, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237217994, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237218280, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237218609, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237218886, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237218954, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750906237217320, "dur": 3636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750906237220956, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750906237221070, "dur": 43975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237127352, "dur": 15697, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237143095, "dur": 615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1750906237143054, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_E966188727BA7937.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750906237143789, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750906237143923, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750906237144139, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1750906237144289, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750906237144606, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1750906237144870, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237146039, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237147074, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237148224, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237149581, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237150570, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237151632, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237152688, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237153635, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237154556, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237155435, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237155829, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237156418, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237157081, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237158031, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237158168, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237158609, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237158981, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237159647, "dur": 2590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237162237, "dur": 50865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237216015, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237216568, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237216978, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237217124, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237217604, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237217682, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237218053, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237218280, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750906237213113, "dur": 5310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750906237218423, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237218495, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237218828, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237219002, "dur": 1969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750906237221017, "dur": 44023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237127007, "dur": 15836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237142855, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237142928, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1750906237142848, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750906237143474, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2DD09EF85C7C380D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750906237143578, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237143577, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750906237143776, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1750906237144129, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1750906237144283, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237144465, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750906237144661, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750906237144768, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750906237144977, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237146150, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237147192, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237148300, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237149705, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237150706, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237151885, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237152894, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237153816, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237154700, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237155509, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237155804, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237156543, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237156879, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750906237157157, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1750906237157772, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237157879, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237158082, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237158160, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237158598, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237158837, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750906237158957, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1750906237159252, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237159314, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237159630, "dur": 2597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237162228, "dur": 50863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237216016, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237216245, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237216300, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237216858, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237213093, "dur": 3850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750906237216944, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237217125, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237218142, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237218334, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237218733, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237218808, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237218954, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237220648, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750906237217124, "dur": 4081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750906237221206, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750906237221315, "dur": 43772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237127397, "dur": 15675, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237143089, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143078, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8E4D74A20FBDC909.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143180, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_4E866FA7B00374E5.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143398, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143397, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_72EF51AA8584F47D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143550, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237143786, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1750906237143963, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1750906237144125, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237144314, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750906237144502, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750906237144694, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750906237144850, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237145903, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237146921, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237148163, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237149556, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237150566, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237151603, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237152644, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237154062, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Editor\\PlasticSCM\\Views\\Diff\\DiffPanel.cs"}}, {"pid": 12345, "tid": 21, "ts": 1750906237153595, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237155131, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237155822, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237156457, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237157006, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237158060, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237158167, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237158608, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237158989, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237159633, "dur": 2595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237162228, "dur": 50878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237213159, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237216274, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237216612, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237216976, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237217072, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237217271, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237217682, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237217890, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237217995, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237218221, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750906237213127, "dur": 5234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750906237218361, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237218666, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237218813, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237218952, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237219086, "dur": 1897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750906237221036, "dur": 44003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237127421, "dur": 15662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237143089, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237143446, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237143573, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237143571, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237143717, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237144132, "dur": 705, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750906237144897, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237146069, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237147204, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237148260, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237149628, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237150618, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237151651, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237152643, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237153662, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237154596, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237155521, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237155826, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237156430, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237157039, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237158069, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237158158, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237158599, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237158835, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237158962, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750906237159625, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237159722, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750906237159989, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237160065, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237160156, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750906237160728, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750906237160821, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1750906237161222, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237162244, "dur": 50882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237213223, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237213306, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237215656, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237216859, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237216976, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237217340, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237217604, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237217683, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237217869, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237218097, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237218185, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237218405, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750906237213127, "dur": 5938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750906237219066, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237219149, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237219317, "dur": 1935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750906237221252, "dur": 43828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237127019, "dur": 15832, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237142864, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237142938, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1750906237142858, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237143487, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237143567, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237143566, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237143734, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144072, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144191, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144282, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144606, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144739, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750906237144884, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237144949, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237146177, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237147249, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237148323, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237149695, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237150694, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237151764, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237152855, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237153799, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237154707, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237155521, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237155803, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237156550, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237156870, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237157038, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237157107, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750906237157660, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237157786, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750906237157949, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750906237158463, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237158634, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237158865, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237159624, "dur": 2590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237162214, "dur": 50845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237214244, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237214346, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237216570, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237216861, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237217072, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237213060, "dur": 4667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750906237217728, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237217914, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237218052, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237218143, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237218611, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237218953, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237219027, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750906237217879, "dur": 3382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750906237221262, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750906237221360, "dur": 43719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237127465, "dur": 15639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237143117, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD6D4C7F30E8FD4.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143202, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143299, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143298, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_4E13DA5D4C1EBAA5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143389, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143387, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0841EB48B73D0C6A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143478, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0841EB48B73D0C6A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237143548, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237143661, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144067, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144318, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144555, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144609, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144704, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144763, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750906237144919, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237145012, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237146268, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237147343, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237148365, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237149790, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237150787, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237151904, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237152882, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237153833, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237154775, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237154859, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237154935, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237155789, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237156405, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237156839, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237157933, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237157022, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750906237158005, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237158161, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237158309, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750906237158671, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237158773, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237158841, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237159607, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237159840, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750906237159939, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750906237160709, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237160839, "dur": 1397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237162237, "dur": 50856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237215265, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237216015, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237213106, "dur": 3417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750906237216524, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237216972, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237217126, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237217339, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237217681, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237217812, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237217914, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237218097, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237218225, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237218484, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237219175, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750906237216658, "dur": 3953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750906237220612, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237220716, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750906237221406, "dur": 43683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237127492, "dur": 15628, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237143138, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143129, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_44E9AA5F4E926643.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143399, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237143470, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_4C50B7F708B8DB2C.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143542, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143541, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143707, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750906237143828, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750906237144005, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750906237144094, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237144145, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750906237144306, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750906237144702, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750906237144924, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750906237145032, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237146464, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237147540, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237148575, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237149946, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237151130, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237152143, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237153152, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237154094, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237154652, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237155470, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237155815, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237156489, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237156967, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750906237157320, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237157373, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750906237158011, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237158212, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237158628, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237158892, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237159629, "dur": 2604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237162233, "dur": 50849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237213101, "dur": 2866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750906237215969, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237216570, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237216860, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237216921, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237217042, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237217340, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237217814, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237217942, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237218184, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237218611, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Antiforgery.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237218691, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Server.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237218953, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750906237216267, "dur": 4616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750906237220884, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237220970, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750906237221032, "dur": 44004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237127513, "dur": 15620, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237143181, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237143284, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0767B7051AB61AA1.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750906237143531, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237143529, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750906237143684, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237143742, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750906237143911, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1750906237144153, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1750906237144302, "dur": 498, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1750906237144802, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1750906237144898, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237144962, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237146252, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237147301, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237148379, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237149726, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237150737, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237151769, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237152834, "dur": 1955, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\XR\\XRLayoutBuilder.cs"}}, {"pid": 12345, "tid": 26, "ts": 1750906237152789, "dur": 2759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237155548, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237155808, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237156528, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237156896, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750906237157232, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750906237158006, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237158202, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237158626, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237158907, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237159616, "dur": 2604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237162221, "dur": 50849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237216015, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237216273, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237216860, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237216976, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237217041, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237217339, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237217914, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237218337, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237218564, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237218954, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237219026, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750906237213071, "dur": 6095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750906237219167, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237219278, "dur": 1920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750906237221198, "dur": 43858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237127093, "dur": 15809, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237142915, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237142990, "dur": 533, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1750906237142909, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750906237143569, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237143567, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750906237143802, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D3E2F14B3A6F429A.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750906237143906, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750906237144073, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750906237144252, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750906237144606, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750906237144872, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750906237145109, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237145988, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237147061, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237148146, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237149621, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237150582, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237151713, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237152811, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237153746, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237154685, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237155558, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237155817, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237156476, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237156969, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237157852, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237158042, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237158177, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237158614, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237158973, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237159613, "dur": 2593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237162207, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750906237162299, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750906237162517, "dur": 50539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237216570, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237216715, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237213059, "dur": 4032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750906237217092, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237217340, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237217603, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237217868, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237218053, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237218222, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237218563, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237218731, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237219026, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237220606, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750906237217189, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750906237221129, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750906237221241, "dur": 43851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237127574, "dur": 15577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237143193, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237143562, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237143561, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750906237143690, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237143795, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750906237144236, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 28, "ts": 1750906237144788, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750906237144871, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237146030, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237147166, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237148250, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237149567, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237150551, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237151735, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237152752, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237153805, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237154766, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237154826, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237154925, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237155790, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237156402, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237156841, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750906237156996, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237157072, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750906237157640, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237157870, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237158058, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237158199, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237158625, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237158921, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237159625, "dur": 2596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237162222, "dur": 50852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237213481, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237216014, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237213075, "dur": 3744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750906237216820, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237217073, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237217270, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237217889, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237218097, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237218280, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237218406, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237218611, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237218953, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237219175, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-filesystem-l1-1-0.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750906237217030, "dur": 4068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750906237221099, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750906237221227, "dur": 43831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237127118, "dur": 15796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237142933, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237143004, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 29, "ts": 1750906237142922, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750906237143545, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237143822, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750906237144069, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750906237144268, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237144572, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1750906237144892, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237146115, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237147305, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237148340, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237149699, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237150682, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237151772, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237152807, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237153763, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237154716, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237155031, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237155817, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237156468, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237156976, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237158014, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237158164, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237158600, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237158838, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237159610, "dur": 2601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237162211, "dur": 50840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237216245, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237216570, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237216858, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237216920, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237213052, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750906237217082, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237217184, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237217342, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237217681, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237218052, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237218452, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237218611, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237218954, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750906237217159, "dur": 4074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750906237221234, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750906237221337, "dur": 43779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237127605, "dur": 15586, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237143199, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750906237143502, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750906237143566, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237143565, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750906237143800, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750906237144140, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750906237144277, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237144383, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750906237144611, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 30, "ts": 1750906237144783, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750906237144909, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237146160, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237147255, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237148332, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237149672, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237150668, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237151764, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237152775, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237153706, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237154636, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237155487, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237155808, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237156523, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237156904, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750906237157159, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750906237157920, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237158077, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237158194, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237158622, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237158937, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237159608, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237160934, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750906237161026, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750906237161534, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750906237161903, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750906237162120, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237162246, "dur": 50851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237216014, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237216570, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237216976, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237217771, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237218053, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237218144, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750906237213098, "dur": 5119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750906237218218, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237218735, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 30, "ts": 1750906237218735, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 30, "ts": 1750906237218835, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237219194, "dur": 1835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237221033, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750906237221086, "dur": 43986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237127627, "dur": 15576, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237143213, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237143207, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_6AB44E5D558EFFCA.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750906237143399, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237143398, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_74AA62AB0BB4C3E0.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750906237143531, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237143711, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750906237144039, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750906237144216, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750906237144268, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237144458, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750906237144556, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750906237144919, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237146386, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237147516, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237148568, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237149866, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237150963, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.9\\Editor\\PostProcessing\\LensFlareComponentSRPEditor.cs"}}, {"pid": 12345, "tid": 31, "ts": 1750906237150880, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237152711, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237154282, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237155272, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237155811, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237156515, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237156915, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750906237157149, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237157261, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750906237158067, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237158184, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237158618, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237158944, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237159619, "dur": 2619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237162239, "dur": 50847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237216920, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237213086, "dur": 3923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750906237217010, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237217271, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237217340, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237217682, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237218279, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237218485, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237218649, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237218808, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237218954, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750906237217107, "dur": 4126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750906237221234, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750906237221371, "dur": 43749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237127168, "dur": 15779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237142962, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237143013, "dur": 555, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 32, "ts": 1750906237142956, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_816435D8968F6544.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237143569, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237143688, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237143687, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237143790, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237144038, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750906237144137, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750906237144392, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750906237144854, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237146060, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237147099, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237148290, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237149624, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237150649, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237151862, "dur": 2490, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnEndDragMessageListener.cs"}}, {"pid": 12345, "tid": 32, "ts": 1750906237151698, "dur": 3461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237155159, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237155798, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237156401, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237156844, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237157068, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750906237157965, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237158181, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237158346, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750906237158737, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237158893, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750906237158987, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 32, "ts": 1750906237159277, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237159364, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237159617, "dur": 2626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237162243, "dur": 52118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237216570, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237216921, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237216976, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237217042, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237217182, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237217943, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237218096, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237218612, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237218733, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237218808, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237218953, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750906237214363, "dur": 4793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750906237219159, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237219348, "dur": 1940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750906237221289, "dur": 43777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750906237271702, "dur": 2961, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 47468, "tid": 649, "ts": 1750906237276596, "dur": 1207, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 47468, "tid": 649, "ts": 1750906237277873, "dur": 5632, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 47468, "tid": 649, "ts": 1750906237275971, "dur": 7563, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 53624, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 53624, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 53624, "tid": 6, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 53624, "tid": 6, "ts": 1750895430600589, "dur": 611, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430604019, "dur": 703, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 53624, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 53624, "tid": 1, "ts": 1750895430459810, "dur": 3315, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 53624, "tid": 1, "ts": 1750895430463128, "dur": 32863, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 53624, "tid": 1, "ts": 1750895430495998, "dur": 34282, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430604726, "dur": 251, "ph": "X", "name": "", "args": {}}, {"pid": 53624, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430456487, "dur": 128, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430456616, "dur": 136346, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430457131, "dur": 1983, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430459119, "dur": 904, "ph": "X", "name": "ProcessMessages 8722", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460026, "dur": 245, "ph": "X", "name": "ReadAsync 8722", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460272, "dur": 7, "ph": "X", "name": "ProcessMessages 20505", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460279, "dur": 21, "ph": "X", "name": "ReadAsync 20505", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460303, "dur": 16, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460320, "dur": 16, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460338, "dur": 39, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460379, "dur": 58, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460441, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460444, "dur": 52, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460498, "dur": 2, "ph": "X", "name": "ProcessMessages 1375", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460500, "dur": 24, "ph": "X", "name": "ReadAsync 1375", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460528, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460554, "dur": 16, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460572, "dur": 15, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460589, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460605, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460620, "dur": 15, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460637, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460653, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460672, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460688, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460707, "dur": 49, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460759, "dur": 38, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460803, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460805, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460858, "dur": 29, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460889, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460891, "dur": 38, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460932, "dur": 26, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460961, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430460963, "dur": 36, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461001, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461003, "dur": 57, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461062, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461064, "dur": 21, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461087, "dur": 20, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461109, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461128, "dur": 27, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461157, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461180, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461197, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461219, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461246, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461248, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461273, "dur": 23, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461298, "dur": 20, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461320, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461343, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461381, "dur": 20, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461404, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461435, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461440, "dur": 31, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461472, "dur": 33, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461507, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461526, "dur": 19, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461548, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461574, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461575, "dur": 24, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461602, "dur": 17, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461621, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461639, "dur": 16, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461658, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461682, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461697, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461714, "dur": 28, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461744, "dur": 24, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461771, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461793, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461812, "dur": 15, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461828, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461846, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461865, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461882, "dur": 12, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461895, "dur": 14, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461913, "dur": 19, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461933, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461950, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461965, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430461982, "dur": 30, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462014, "dur": 17, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462033, "dur": 13, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462048, "dur": 13, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462062, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462082, "dur": 16, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462100, "dur": 17, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462118, "dur": 17, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462137, "dur": 14, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462154, "dur": 13, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462168, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462184, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462200, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462218, "dur": 25, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462246, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462271, "dur": 25, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462299, "dur": 20, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462322, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462339, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462356, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462372, "dur": 40, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462414, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462437, "dur": 15, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462454, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462471, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462490, "dur": 14, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462505, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462524, "dur": 16, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462542, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462560, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462576, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462594, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462616, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462634, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462653, "dur": 16, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462671, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462688, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462705, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462723, "dur": 16, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462740, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462741, "dur": 16, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430462759, "dur": 298, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463059, "dur": 49, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463108, "dur": 3, "ph": "X", "name": "ProcessMessages 6902", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463112, "dur": 20, "ph": "X", "name": "ReadAsync 6902", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463135, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463151, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463168, "dur": 15, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463184, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463204, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463221, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463241, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463261, "dur": 16, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463279, "dur": 18, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463298, "dur": 17, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463317, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463318, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463337, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463359, "dur": 19, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463379, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463405, "dur": 111, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463519, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463568, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463570, "dur": 25, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463598, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463619, "dur": 15, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463635, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463658, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463676, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463693, "dur": 16, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463711, "dur": 35, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463749, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463791, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463793, "dur": 23, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463818, "dur": 35, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463856, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463858, "dur": 42, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463902, "dur": 28, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463933, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463956, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430463978, "dur": 34, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464015, "dur": 32, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464048, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464073, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464096, "dur": 22, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464120, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464141, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464160, "dur": 21, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464183, "dur": 25, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464210, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464212, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464235, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464260, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464282, "dur": 18, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464302, "dur": 15, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464319, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464342, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464359, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464381, "dur": 12, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464395, "dur": 15, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464412, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464444, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464467, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464489, "dur": 17, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464507, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464525, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464543, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464560, "dur": 15, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464577, "dur": 20, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464600, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464621, "dur": 15, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464639, "dur": 13, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464654, "dur": 16, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464671, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464689, "dur": 14, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464706, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464723, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464740, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464758, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464774, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464792, "dur": 14, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464807, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464825, "dur": 15, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464842, "dur": 14, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464858, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464876, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464894, "dur": 17, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464913, "dur": 15, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464929, "dur": 18, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464949, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464972, "dur": 18, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430464991, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465009, "dur": 16, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465027, "dur": 14, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465042, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465060, "dur": 15, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465077, "dur": 28, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465106, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465123, "dur": 14, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465139, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465155, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465171, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465187, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465205, "dur": 16, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465223, "dur": 13, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465237, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465254, "dur": 18, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465273, "dur": 16, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465291, "dur": 24, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465317, "dur": 24, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465342, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465366, "dur": 15, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465382, "dur": 109, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465493, "dur": 1, "ph": "X", "name": "ProcessMessages 1544", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465494, "dur": 27, "ph": "X", "name": "ReadAsync 1544", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465525, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465550, "dur": 14, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465566, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465584, "dur": 14, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465600, "dur": 14, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465616, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465634, "dur": 14, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465650, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465665, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465690, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465713, "dur": 16, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465731, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465749, "dur": 14, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465764, "dur": 12, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465777, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465793, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465809, "dur": 14, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465825, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465842, "dur": 14, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465858, "dur": 14, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465902, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465924, "dur": 1, "ph": "X", "name": "ProcessMessages 1306", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465925, "dur": 17, "ph": "X", "name": "ReadAsync 1306", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465945, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430465966, "dur": 1920, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430467891, "dur": 25, "ph": "X", "name": "ProcessMessages 20537", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430467918, "dur": 61, "ph": "X", "name": "ReadAsync 20537", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430467984, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430467987, "dur": 117, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468107, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468149, "dur": 37, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468188, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468190, "dur": 29, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468222, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468224, "dur": 59, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468285, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468322, "dur": 31, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468354, "dur": 26, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468383, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468385, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468436, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468477, "dur": 29, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468508, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468511, "dur": 49, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468563, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468605, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468607, "dur": 176, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468786, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468788, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468829, "dur": 1, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468832, "dur": 31, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468865, "dur": 26, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468893, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468895, "dur": 44, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468941, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468944, "dur": 26, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430468973, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469041, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469084, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469088, "dur": 24, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469115, "dur": 65, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469184, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469208, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469242, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469244, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469272, "dur": 46, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469324, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469354, "dur": 43, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469399, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469401, "dur": 57, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469462, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469464, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469494, "dur": 80, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469580, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469613, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469615, "dur": 43, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469660, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469662, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469692, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469723, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469741, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469760, "dur": 56, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469820, "dur": 23, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469847, "dur": 49, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469898, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469900, "dur": 23, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469926, "dur": 44, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469974, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430469996, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470016, "dur": 26, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470044, "dur": 26, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470073, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470074, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470105, "dur": 24, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470131, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470171, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470195, "dur": 16, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470213, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470230, "dur": 43, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470276, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470318, "dur": 25, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470344, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470346, "dur": 17, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470364, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470421, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470461, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470463, "dur": 17, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470483, "dur": 41, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470527, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470551, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470569, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470586, "dur": 46, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470636, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470657, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470678, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470679, "dur": 18, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470699, "dur": 44, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470745, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470767, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470786, "dur": 18, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470806, "dur": 14, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470822, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470887, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470910, "dur": 27, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470940, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470941, "dur": 14, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430470957, "dur": 51, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471010, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471031, "dur": 14, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471047, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471065, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471085, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471135, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471157, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471178, "dur": 16, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471196, "dur": 53, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471251, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471268, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471284, "dur": 16, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471302, "dur": 13, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471317, "dur": 46, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471365, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471382, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471401, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471420, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471421, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471465, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471482, "dur": 16, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471501, "dur": 14, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471516, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471563, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471587, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471606, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471623, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471683, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471726, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471727, "dur": 40, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471770, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471772, "dur": 35, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471809, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471832, "dur": 46, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471879, "dur": 15, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471898, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471944, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471970, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430471988, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472007, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472026, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472046, "dur": 16, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472063, "dur": 26, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472090, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472110, "dur": 50, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472161, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472180, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472197, "dur": 13, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472211, "dur": 52, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472265, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472286, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472309, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472327, "dur": 55, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472385, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472442, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472443, "dur": 38, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472483, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472485, "dur": 45, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472533, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472572, "dur": 34, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472608, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472637, "dur": 39, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472679, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472681, "dur": 26, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472709, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472729, "dur": 14, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472745, "dur": 17, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472764, "dur": 49, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472814, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472835, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472854, "dur": 60, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472917, "dur": 15, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472934, "dur": 50, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430472987, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473008, "dur": 19, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473028, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473029, "dur": 22, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473053, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473068, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473089, "dur": 7, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473098, "dur": 12, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473111, "dur": 60, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473174, "dur": 15, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473191, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473206, "dur": 14, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473221, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473270, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473286, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473305, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473320, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473336, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473351, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473371, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473388, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473412, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473426, "dur": 63, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473490, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473504, "dur": 150, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473659, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473692, "dur": 259, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430473953, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474033, "dur": 3, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474037, "dur": 40, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474078, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474080, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474125, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474127, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474168, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474169, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474201, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474203, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474230, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474259, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474262, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474298, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474301, "dur": 54, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474359, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474361, "dur": 49, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474412, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474413, "dur": 36, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474452, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474454, "dur": 46, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474503, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474504, "dur": 32, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474539, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474570, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474572, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474613, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474615, "dur": 40, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474657, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474659, "dur": 41, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474703, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474705, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474746, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474748, "dur": 45, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474795, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474797, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474843, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474846, "dur": 47, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474895, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474898, "dur": 54, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474953, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474955, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474992, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430474995, "dur": 48, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475045, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475047, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475100, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475103, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475146, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475148, "dur": 49, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475200, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475201, "dur": 49, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475252, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475254, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475299, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475333, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475371, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475373, "dur": 38, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475413, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475417, "dur": 45, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475465, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475509, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475511, "dur": 41, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475554, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475556, "dur": 49, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475608, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475610, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475650, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475683, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475685, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475719, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475721, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475827, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475829, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430475858, "dur": 10842, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486707, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486735, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486797, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486822, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486826, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486910, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486938, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430486996, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487014, "dur": 406, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487422, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487442, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487527, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487550, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487590, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487610, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487873, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487889, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487923, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430487926, "dur": 923, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430488854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430488857, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430488922, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430488926, "dur": 58, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430488990, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489032, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489035, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489079, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489082, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489137, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489140, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489185, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489188, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489260, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489308, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489310, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489355, "dur": 42, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489399, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489401, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489442, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489444, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489490, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489493, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489518, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489538, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489564, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489593, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489634, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489661, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489691, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489783, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489784, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489814, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489816, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489855, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489915, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489947, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489949, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489983, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430489984, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490015, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490018, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490062, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490090, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490112, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490128, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490131, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490227, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490251, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490365, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490384, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490397, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490444, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490465, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490485, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490509, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490525, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490546, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490575, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490670, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490674, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490705, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490852, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490918, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430490948, "dur": 929, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430491879, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430491925, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430491928, "dur": 69, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492001, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492027, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492029, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492056, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492082, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492111, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492154, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492181, "dur": 275, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492460, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492491, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492492, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492594, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492630, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492632, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492685, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492720, "dur": 80, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492803, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492839, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492840, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430492877, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493203, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493207, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493246, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493248, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493266, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493287, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493317, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493348, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493372, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493410, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493435, "dur": 255, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493694, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493712, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493787, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430493809, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430494370, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430494373, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430494394, "dur": 77038, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430571440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430571443, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430571462, "dur": 1593, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430573058, "dur": 6452, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579517, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579521, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579569, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579572, "dur": 364, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430579942, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580103, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580168, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580206, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580208, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580268, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580271, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580307, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580309, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580342, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580371, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580373, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580541, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580544, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580668, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580670, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580707, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580745, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580747, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580788, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580861, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580900, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580935, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430580966, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581012, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581014, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581074, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581076, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581121, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581163, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581165, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581203, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581205, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581242, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581244, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581288, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581291, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581334, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581336, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581378, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581380, "dur": 39, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581421, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581423, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581473, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581475, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581507, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581509, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581544, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581645, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581669, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581694, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581714, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581718, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581742, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581765, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581793, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581846, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430581876, "dur": 640, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430582520, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430582549, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430582551, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430582581, "dur": 500, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583084, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583111, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583134, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583157, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583237, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583269, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583271, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583301, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583334, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583358, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583359, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583390, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583475, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583505, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583507, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583541, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583573, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583574, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583601, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583603, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583633, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583659, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583680, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583722, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583738, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583755, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583772, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583836, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583857, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583881, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430583911, "dur": 740, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430584654, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430584674, "dur": 182, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 53624, "tid": 12884901888, "ts": 1750895430584857, "dur": 7919, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430604978, "dur": 762, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 53624, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 53624, "tid": 8589934592, "ts": 1750895430454514, "dur": 75810, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 53624, "tid": 8589934592, "ts": 1750895430530327, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 53624, "tid": 8589934592, "ts": 1750895430530334, "dur": 1076, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430605742, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 53624, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 53624, "tid": 4294967296, "ts": 1750895430429762, "dur": 163918, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 53624, "tid": 4294967296, "ts": 1750895430435216, "dur": 14063, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 53624, "tid": 4294967296, "ts": 1750895430593691, "dur": 5120, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 53624, "tid": 4294967296, "ts": 1750895430595676, "dur": 2226, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 53624, "tid": 4294967296, "ts": 1750895430598857, "dur": 6, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430605748, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750895430455222, "dur": 1534, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430456766, "dur": 579, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430457491, "dur": 666, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430458556, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750895430459343, "dur": 1272, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750895430465789, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750895430467762, "dur": 467, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750895430473212, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750895430458172, "dur": 15677, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430473859, "dur": 110437, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430584297, "dur": 447, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430584996, "dur": 1091, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750895430457835, "dur": 16035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430473891, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430473988, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_6DF80889E17A310D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474113, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430474183, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474182, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_74665D5FA2186AE5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474380, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0CACEFF522BB87D4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474465, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474464, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474669, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_DACAA631817C5F43.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474755, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750895430474957, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750895430475037, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750895430475327, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750895430475415, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750895430475653, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750895430475918, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430477214, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430478242, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430479199, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430480425, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430481309, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430482742, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430483759, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430485330, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430486219, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430486968, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430487254, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430487862, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430488374, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430489380, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430489768, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430489838, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430490260, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430490386, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430491126, "dur": 84998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430578294, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Identity.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430578559, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430578733, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430579345, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430580438, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430580521, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430580602, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430580716, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581039, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581094, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581241, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581317, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581483, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581593, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750895430576125, "dur": 5852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750895430581978, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430582077, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430582138, "dur": 1940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750895430584104, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430458109, "dur": 15921, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430474036, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AAB6357F3297AC29.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474137, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474136, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_4E866FA7B00374E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474230, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430474306, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474304, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474398, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1F601FBAF535BF46.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474495, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474495, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474665, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F48F7C76736D07A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430474948, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750895430475272, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750895430475586, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750895430475805, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750895430475976, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430477334, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430478347, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430479978, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\ReplaceColorNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750895430480838, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Legacy\\UnlitMasterNode1.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750895430479402, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430481434, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430483151, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\UnaryOperatorHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750895430482543, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430484477, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430485568, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430486585, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430487225, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430487838, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430488331, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750895430488646, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750895430489152, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430489417, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430489842, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430490267, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430490399, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430491136, "dur": 84949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430579346, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430579812, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430580130, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430580232, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430580324, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430580439, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581178, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581317, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581376, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581443, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581593, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750895430576086, "dur": 5814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750895430581900, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430582017, "dur": 1842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750895430583859, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430457852, "dur": 16030, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430473891, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430474111, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430474189, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430474187, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_6E89F7CAA1D4322F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750895430474254, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430474404, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430474554, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430474626, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430474625, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F026DDF4FF0829A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750895430474729, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F026DDF4FF0829A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750895430474972, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750895430475177, "dur": 447, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750895430475687, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750895430475867, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430477128, "dur": 1062, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430475925, "dur": 2733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430478658, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430479642, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430480837, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430482216, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430484096, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Playables\\PrefabControlPlayable.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750895430483523, "dur": 2059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430485582, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430486570, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430487218, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430487861, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430488351, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430489442, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430489817, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430490226, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750895430490360, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750895430490719, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430490813, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430491149, "dur": 84960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430576181, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430578739, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430580132, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430580565, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430580680, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430580850, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430581140, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430581241, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750895430576113, "dur": 5344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750895430581458, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430581693, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430582136, "dur": 1851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750895430584016, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430458079, "dur": 15925, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430474020, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430474013, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C8E242A8B361CAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750895430474244, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430474243, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_C5E1A198ADF17864.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750895430474448, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430474596, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430474677, "dur": 788, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750895430475507, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750895430475786, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750895430475868, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430477193, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430478185, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430479265, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430480535, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430481460, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430482544, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430483837, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430485160, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430486195, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430486950, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430487248, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430487842, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430488372, "dur": 1013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430489385, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430489769, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430489836, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430490257, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430490378, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430491123, "dur": 84947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430579344, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430576071, "dur": 4048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750895430580119, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750895430580520, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430580602, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430580767, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430580948, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430581073, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430581178, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430581376, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750895430580256, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750895430584091, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430458355, "dur": 15835, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430474211, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430474200, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0767B7051AB61AA1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750895430474284, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430474345, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0767B7051AB61AA1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750895430474585, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430474659, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750895430475052, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750895430475229, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750895430475366, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750895430475638, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750895430475853, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430477015, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430477994, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430479050, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430480406, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430481421, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430482573, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430483644, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430484651, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430485589, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430486731, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430487235, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430487846, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430488352, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430489427, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430489821, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430490237, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430490403, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430491138, "dur": 84936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430577705, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Server.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430580131, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430580232, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430576094, "dur": 4266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750895430580361, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430580766, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430581039, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430581178, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430581481, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750895430580589, "dur": 3177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750895430583767, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750895430583859, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430457944, "dur": 15974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430473982, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1750895430473926, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_49ACE09E3C0E6144.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750895430474322, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430474321, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4137C56C25BAB6AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750895430474434, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430474657, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750895430475052, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750895430475282, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750895430475471, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750895430475692, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750895430475864, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430477140, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430478251, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430479265, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430480535, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430481714, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430483092, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430484339, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430485263, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430486214, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430486998, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430487268, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430487870, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430488368, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430489389, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430489841, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430490261, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430490388, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430491129, "dur": 84929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430578372, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Collections.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580131, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580233, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580437, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580565, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580645, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430580849, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430581039, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430581108, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430581241, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750895430576060, "dur": 5286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750895430581347, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430581430, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430581508, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430581592, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430581696, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430582091, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430582177, "dur": 1904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750895430584113, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430457968, "dur": 15963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430473996, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1750895430473940, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D76F3551127B4440.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474170, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474169, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FB86898111D557DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474324, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430474401, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474399, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB998ABA4D09E52A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474587, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474676, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430474783, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430475089, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750895430475322, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430475493, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750895430475715, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430475880, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430475938, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750895430476107, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430477401, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430478424, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430479432, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430480673, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430481855, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430483162, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430484540, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430485447, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430486341, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430486475, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430486525, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430487249, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430487849, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430488335, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750895430488660, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750895430489509, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430489694, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430489848, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430490265, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430490394, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430491130, "dur": 84910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430576228, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430576680, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430579346, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430580320, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430580437, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430580680, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430580849, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430580989, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750895430576042, "dur": 5136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750895430581179, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430581480, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430581610, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430581748, "dur": 1830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750895430583623, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430458197, "dur": 15892, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430474100, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474094, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_44E9AA5F4E926643.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474193, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430474250, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_4C50B7F708B8DB2C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474301, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430474374, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_4C50B7F708B8DB2C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474627, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_077956647DBD2B29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474716, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_077956647DBD2B29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430474951, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750895430475222, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750895430475326, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750895430475512, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750895430475850, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430477098, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430478286, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.9\\Editor\\Converter\\InitializeConverterContext.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750895430478129, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430479765, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430480986, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430482001, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430483345, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430484378, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430485320, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430486256, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430486841, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430487243, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430487835, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430488322, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430488717, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750895430489302, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430489673, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750895430489815, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750895430490265, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430490431, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430491143, "dur": 84929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430579811, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430580436, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430580901, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430581040, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430581317, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750895430576074, "dur": 5343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750895430581418, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430581568, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430581683, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430581751, "dur": 1912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430583667, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750895430583893, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430458215, "dur": 15886, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430474202, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430474201, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1FFDB2139C1829E2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750895430474304, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430474400, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430474398, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_AC5852D97D0872BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750895430474555, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430474624, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_13EB1DAF647B9A69.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750895430474830, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750895430475172, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750895430475467, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750895430475653, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750895430475916, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430477300, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430478310, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430479273, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430480555, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430481624, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430482834, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430483919, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430484848, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430485782, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430486738, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430487240, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430487832, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430488353, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430489370, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430489837, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430490265, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430490393, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430491148, "dur": 84919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430576069, "dur": 4005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750895430580075, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430580267, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430580566, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430580681, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581040, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581108, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581178, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581355, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581481, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581593, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430581711, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750895430580263, "dur": 3376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750895430583640, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750895430583726, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430458022, "dur": 15942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430473982, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430473972, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0CA797179878B1A6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474096, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474095, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_A45827FC07386469.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474187, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430474324, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5A63E73C35F67AF2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474407, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474406, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FCE1C8134C576E3F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430474703, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750895430475027, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750895430475251, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430475355, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750895430475856, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430477223, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430478198, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430479342, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430480569, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430481777, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430483114, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430484649, "dur": 1707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\UI\\PointerModel.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750895430484163, "dur": 2564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430486727, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430487234, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430487829, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430488306, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430488618, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750895430489540, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430489664, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430489812, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750895430490187, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430490284, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750895430490439, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750895430490775, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430490858, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430491151, "dur": 84956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430576197, "dur": 2094, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430579346, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430579812, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430580436, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Hosting.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430580678, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430580851, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430581073, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.HttpListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430581179, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Numerics.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430581318, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.Unsafe.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430581376, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430581460, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750895430576108, "dur": 6678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750895430582790, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430582874, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750895430584229, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430458250, "dur": 15876, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430474143, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430474134, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D19DF51C870C57DD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750895430474297, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430474296, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750895430474404, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_13A6CA2C2371A4E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750895430474510, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430474696, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475028, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430475085, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475333, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475397, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475560, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475753, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750895430475878, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430475934, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750895430476076, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430477512, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430478578, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430479677, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430480959, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430481981, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430483260, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430484300, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430485403, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430486576, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430487227, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430487837, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430488357, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750895430488729, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750895430489248, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430489425, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430489822, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430490259, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430490385, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430491125, "dur": 84938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430576973, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430579812, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430579923, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580130, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580232, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580488, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580565, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580682, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430580948, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750895430576072, "dur": 5187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750895430581260, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430581541, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1750895430581692, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430582037, "dur": 1834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750895430583912, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430458058, "dur": 15934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430474009, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474001, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_02A5411DCBCEA692.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474130, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474128, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E79E12ACD5E69601.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474222, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430474289, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474286, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C4C4B9839337CEB6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474386, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430474622, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474621, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_0CBB1EE9BC1FA429.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750895430474852, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430474907, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750895430475275, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750895430475511, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750895430475646, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750895430475878, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430475929, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750895430476039, "dur": 2437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430478476, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430479865, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430481081, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430482256, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430483467, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430484430, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430485379, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430486592, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430487226, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430487858, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430488349, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430489364, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430489750, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430489817, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430490227, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750895430490407, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430490474, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750895430490823, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430490896, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430491111, "dur": 40635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430531748, "dur": 2328, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430534077, "dur": 41984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430576199, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580271, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580442, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580520, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580646, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580766, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750895430576062, "dur": 4927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750895430580989, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430581109, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430581275, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430581404, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430581689, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430581846, "dur": 1988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750895430583951, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430457880, "dur": 16011, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430473902, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430473988, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1750895430473896, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_FF3EE29BA550255D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430474173, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430474279, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430474333, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_02D2E9C797962C02.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430474490, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430474489, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430474674, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_AFC4A1775743CCD8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430475078, "dur": 654, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750895430475851, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430477097, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430478288, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430479392, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430480632, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430481553, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430482729, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430483793, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430485130, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430486302, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430486524, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430487208, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430487832, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430488300, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430488680, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750895430490095, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430490265, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430490418, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750895430491419, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430491506, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750895430492286, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430492368, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750895430492446, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750895430492730, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430492818, "dur": 83258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430579346, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430579812, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430580130, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430580323, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430580566, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430580851, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430576077, "dur": 4961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750895430581038, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430581194, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430581355, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430581354, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750895430581440, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430581565, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430581703, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1750895430581762, "dur": 1905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750895430583723, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430458098, "dur": 15919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430474036, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474026, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_4E134BA707D59294.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474110, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430474196, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474194, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FA846FADE8279C51.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474291, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430474346, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FA846FADE8279C51.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474517, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430474746, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750895430474874, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750895430475203, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750895430475574, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750895430475848, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430477432, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1750895430477219, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430478992, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430480245, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430481295, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430482365, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430483493, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430484709, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430485832, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430486869, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430487244, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430487834, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430488309, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430488723, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430489177, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430489375, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430489763, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430489821, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430490225, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430490369, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430490956, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430491146, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430491259, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430491564, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430491657, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430491773, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430492312, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430492418, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750895430492519, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430492946, "dur": 86410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430579811, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430580180, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430580450, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430580645, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430580988, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430581317, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Web.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430581424, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Abstractions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750895430579358, "dur": 3983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750895430583344, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750895430583456, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430457898, "dur": 16001, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430473915, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430473906, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_61D587A6357C338F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430474107, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430474106, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_793BB1A80CF480CF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430474187, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430474294, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430474427, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430474602, "dur": 469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750895430475116, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750895430475244, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750895430475411, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750895430475624, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750895430475809, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430477063, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430478142, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430479658, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.9\\Editor\\Data\\Interfaces\\Graph\\GraphDrawingData.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750895430479500, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430481323, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430482372, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430483505, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430484478, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430485431, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430486251, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430486963, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430487253, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430487848, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430488339, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430488605, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430489501, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430489760, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430489826, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430490241, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430490371, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430491105, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430491188, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430491252, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430491686, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430491761, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430491848, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430492870, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430492952, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430493025, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430493540, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430493655, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750895430493749, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430494074, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430494131, "dur": 81995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430576174, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430576239, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430580130, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430576127, "dur": 4307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430581374, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430581443, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750895430580492, "dur": 3343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750895430583839, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750895430583940, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430457916, "dur": 15993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430473963, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1750895430473915, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_04A1FDA49363C53B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474221, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474220, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_4E13DA5D4C1EBAA5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474295, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430474376, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474375, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6272BEC5630D4174.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474435, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430474603, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750895430474809, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430474875, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750895430475176, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750895430475648, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750895430475909, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430477422, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430478452, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430479469, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430481322, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430482407, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430483921, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430484970, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430486092, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430486913, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430487246, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430487840, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430488322, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750895430488676, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750895430489893, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430490018, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750895430490121, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750895430490517, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430490573, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430491152, "dur": 84941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430577103, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580232, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580391, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580446, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580602, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430576102, "dur": 4713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580816, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750895430581347, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430581462, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750895430580952, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750895430583682, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430458144, "dur": 15906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430474056, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_71B2BC6506C23CCC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474112, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430474210, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474209, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_62E3103B1EFE0DCE.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474285, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430474375, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_62E3103B1EFE0DCE.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474463, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474461, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2AAE32905E8C08CA.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474713, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750895430474943, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1750895430475227, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1750895430475811, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430476996, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430478058, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430479057, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430480292, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430481286, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430482388, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430483484, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430484476, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430485386, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430486350, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430486469, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430486567, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430487217, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430487863, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430488356, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430489373, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430489820, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1750895430489977, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1750895430490348, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430490461, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430491145, "dur": 84955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430579342, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430580131, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430580231, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430580401, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430580565, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430580851, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430581040, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430581095, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 17, "ts": 1750895430576102, "dur": 5068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1750895430581171, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430581348, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430581715, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1750895430581781, "dur": 2048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1750895430583911, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430458162, "dur": 15901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430474072, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46653E30803A0FEB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750895430474197, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430474196, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F302F5FA886F2ABC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750895430474293, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430474381, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F302F5FA886F2ABC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750895430474506, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430474635, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750895430474905, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1750895430475015, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750895430475162, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750895430475664, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1750895430475903, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430477177, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430478186, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430479207, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430480552, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430481465, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430482627, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430483714, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430484793, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430485722, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430486648, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430487231, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430487853, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430488340, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1750895430488674, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1750895430489577, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430489652, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430489777, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430489854, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430490269, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430490400, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430491133, "dur": 84914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430579812, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580131, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580233, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580488, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580644, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580851, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430576069, "dur": 4870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750895430580940, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1750895430581140, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581317, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581374, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581482, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581610, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581667, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1750895430581073, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1750895430584001, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430458423, "dur": 15861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430474295, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430474284, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BC49A6886E1E88EB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750895430474369, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430474449, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430474814, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430474879, "dur": 424, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750895430475366, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750895430475513, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750895430475673, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1750895430476800, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@2.9.7\\Editor\\Editors\\CinemachineSmoothPathEditor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1750895430475828, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430477682, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430478710, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430479757, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430480976, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430481997, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430483173, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430484176, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430485128, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430486081, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430486876, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430487250, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430487846, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430488331, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750895430488557, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750895430489412, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430489857, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430490276, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430490367, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430491106, "dur": 1943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430493050, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1750895430493132, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430493200, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1750895430493527, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430493633, "dur": 82484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430579346, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430576120, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750895430580286, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430580681, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430581043, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430581179, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430581462, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 19, "ts": 1750895430580630, "dur": 3123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1750895430583753, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1750895430583900, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430457986, "dur": 15957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430473956, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430473951, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_10E6568D27D507F0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750895430474106, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430474105, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F4C82E9DCF8C85FF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750895430474179, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430474237, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750895430474288, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430474354, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BBCEFDB554DB8E5E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750895430474521, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430474795, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750895430475017, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430475080, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750895430475177, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1750895430475436, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750895430475641, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430475750, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1750895430475950, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430477442, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430478427, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430479393, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430480611, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430481701, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430482909, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430484023, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430485257, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430486582, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430487225, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430487857, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430488377, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430489368, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430489751, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430489866, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430490233, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430490360, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430491108, "dur": 2552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430493661, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1750895430493780, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1750895430494100, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430494166, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1750895430494426, "dur": 81671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430576364, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430579813, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430580232, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430580483, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430580767, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": 1750895430576106, "dur": 5133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1750895430581240, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430581437, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430581531, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430581734, "dur": 1700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1750895430583479, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430458004, "dur": 15950, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430473970, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430473962, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A03EB829CBA328B3.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474172, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474171, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9E74BE77200C0976.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474294, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474292, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_4796D0C7D217714A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474381, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430474468, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430474626, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430474735, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474795, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750895430474954, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750895430475116, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750895430475403, "dur": 517, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1750895430475921, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430477456, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430478673, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430479725, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430480956, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430481978, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430483210, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430484281, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430485422, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430486493, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430487209, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430487829, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430488303, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1750895430488672, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1750895430489361, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430489480, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430489823, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430490238, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430490410, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430491141, "dur": 84942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430576084, "dur": 3209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750895430579294, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1750895430580130, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430580233, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430580436, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430580851, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430581140, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430581241, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430581424, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430581481, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430581548, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 21, "ts": 1750895430579831, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1750895430583689, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430458233, "dur": 15881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430474128, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430474121, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_DF935DB001343E8A.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1750895430474244, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430474431, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430474631, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750895430474742, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1750895430475090, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750895430475311, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1750895430475691, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750895430475783, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750895430475887, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17122354673617034552.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1750895430476019, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430477247, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430478276, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430479317, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430481326, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\Framework\\Math\\MoveTowards.cs"}}, {"pid": 12345, "tid": 22, "ts": 1750895430480609, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430482117, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430483332, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430484764, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430485701, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430486666, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430487257, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430487864, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430488361, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430489387, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430489775, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430489832, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430490249, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430490374, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430491117, "dur": 84927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430579739, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580130, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580230, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580323, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580438, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580518, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580645, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580852, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430580948, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430581096, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430576046, "dur": 5221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1750895430581268, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430581471, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430581550, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1750895430581638, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430581700, "dur": 1161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1750895430582903, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430458039, "dur": 15940, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430474192, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430474256, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474255, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_1E575EBD1ADFF155.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474382, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_1E575EBD1ADFF155.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474471, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474469, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474671, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_06567403B026473B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430474812, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430474867, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1750895430475203, "dur": 420, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1750895430475624, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750895430475721, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1750895430475865, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430477158, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430478160, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430479242, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430480860, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430482317, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430483521, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430484559, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430485690, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430486642, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430487228, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430487851, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430488343, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430488559, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750895430489324, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430489648, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430489827, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430490245, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430490372, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430491104, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1750895430491192, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1750895430491489, "dur": 84565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430576056, "dur": 4121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750895430580178, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1750895430580323, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430580436, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430580602, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430580680, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430581039, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430581274, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 23, "ts": 1750895430580309, "dur": 3726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1750895430584097, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430458269, "dur": 15870, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430474146, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_C4861F75001A3909.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430474198, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430474299, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430474445, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_219436DA005DE56D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430474527, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430474717, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750895430474830, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1750895430475228, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1750895430475387, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750895430475833, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1750895430475967, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430477262, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430478277, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430479259, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430480500, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430481414, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430482563, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430483662, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430484649, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430485712, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430486658, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430487232, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430487853, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430488346, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430489367, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430489752, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430489814, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430490228, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430490362, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430491105, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430491423, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430491515, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750895430492319, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430492404, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430492497, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750895430492965, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430493045, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430493142, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750895430493471, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430493574, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430493690, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1750895430493979, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430494055, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1750895430494155, "dur": 82008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430579662, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580130, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580231, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580323, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580443, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580564, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580643, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430580852, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581039, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581242, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581316, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581374, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581481, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581591, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.2.0\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581667, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 24, "ts": 1750895430576164, "dur": 5573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1750895430581737, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430581849, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1750895430583898, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430458286, "dur": 15865, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430474163, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430474158, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_9118DBDC897F9C38.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750895430474274, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430474274, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_2DD09EF85C7C380D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750895430474433, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430474575, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430474712, "dur": 538, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1750895430475389, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750895430475511, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750895430475643, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750895430475743, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1750895430475878, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430477535, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430478603, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430479574, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430481194, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430482265, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430483408, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430484532, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430485431, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430486388, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430486487, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430487208, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430487834, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430488317, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750895430488605, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430488664, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750895430489524, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430489749, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1750895430489912, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1750895430490656, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430490738, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430491150, "dur": 84927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430579345, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580130, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580231, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580323, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580438, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580565, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580645, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580767, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430580851, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430581178, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430581481, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430581608, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430581715, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 25, "ts": 1750895430576078, "dur": 5895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1750895430581976, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430582106, "dur": 1817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1750895430583924, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430458307, "dur": 15857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430474179, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474171, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_99235D0DADAB3855.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474245, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430474318, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474316, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E9D933C7AF1168F7.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474417, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E9D933C7AF1168F7.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474481, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474479, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430474687, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_295D7F5050B34F24.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475277, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475602, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475810, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475867, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475932, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430476016, "dur": 475, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430476492, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430476758, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430476823, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430476915, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477141, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477364, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477602, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477663, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477758, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430477905, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430478096, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430478189, "dur": 678, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430478891, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430479015, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430479075, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430479134, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430479187, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430479399, "dur": 902, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430480314, "dur": 531, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430480847, "dur": 630, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430481526, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430481639, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430481790, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430481898, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430481956, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482011, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482076, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482167, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482221, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482424, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482476, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482703, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430482804, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430483036, "dur": 1677, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430484740, "dur": 1632, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486373, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486524, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486598, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486655, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486715, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430475403, "dur": 11559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750895430486963, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430487063, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430487212, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430487831, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430488305, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1750895430488496, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1750895430489506, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430489684, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430489831, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430490252, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430490380, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430491120, "dur": 84930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430579812, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430580129, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430580437, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430580520, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430580851, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430576107, "dur": 4921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750895430581029, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1750895430581316, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430581461, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430581591, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1750895430581086, "dur": 3063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1750895430584225, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430458330, "dur": 15846, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430474193, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474184, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1F1578B895CD76F0.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474307, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474306, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474397, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A72520642B52B843.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474476, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474475, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_C07BA8A41180AD1C.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474727, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750895430474831, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1750895430475138, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750895430475242, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750895430475580, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1750895430475708, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750895430475885, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1750895430476006, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430478278, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.State\\Transitions\\StateTransitionAnalyser.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750895430479351, "dur": 903, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.State\\Transitions\\IStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750895430477452, "dur": 2990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430480800, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Runtime\\VisualScripting.Flow\\UnitCategory.cs"}}, {"pid": 12345, "tid": 27, "ts": 1750895430480442, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430481878, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430483186, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430484379, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430485357, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430486349, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430486584, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430487219, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430487842, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430488332, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1750895430488658, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1750895430489570, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430489832, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430490250, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430490376, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430491119, "dur": 84919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430579345, "dur": 493, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430580129, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430580444, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430576039, "dur": 4688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750895430580728, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430581178, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430581241, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430581374, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430581460, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430581593, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 27, "ts": 1750895430580875, "dur": 2896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1750895430583774, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1750895430583889, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430458120, "dur": 15920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430474143, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750895430474141, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8710F84A064A72C8.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750895430474272, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430474447, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430474737, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750895430475158, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750895430475406, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750895430475525, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430475655, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1750895430475911, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430477758, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430478820, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430479804, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430481456, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430482512, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430483612, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430484629, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430485585, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430486612, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430487237, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430487830, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430488302, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750895430488447, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430488744, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750895430489676, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430489816, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1750895430489978, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1750895430490302, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430490406, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430491139, "dur": 84964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430578442, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Core.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750895430579811, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750895430580436, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750895430580989, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 28, "ts": 1750895430576125, "dur": 4982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1750895430581107, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430581408, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430581533, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1750895430581591, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430581712, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1750895430581776, "dur": 1929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1750895430583705, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430458371, "dur": 15875, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430474254, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_CB6996444704BF62.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430474308, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430474417, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430474415, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430474560, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430474640, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_77C9A58BBAED7663.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430474821, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430475593, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430475810, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430475868, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430475929, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430476022, "dur": 461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430476484, "dur": 330, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430476815, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430476922, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430477223, "dur": 1187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430478503, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430478610, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430478801, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430478945, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430478998, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479065, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479125, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479186, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479403, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479803, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479902, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430479955, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430480274, "dur": 569, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430480844, "dur": 1007, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430481893, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430481952, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482004, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482077, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482195, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482556, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482705, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430482800, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430483040, "dur": 1053, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484094, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484238, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484294, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484344, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484443, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484675, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430484727, "dur": 1689, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430486465, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430486525, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430486595, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430486646, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430486704, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430474988, "dur": 12103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430487275, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430487346, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430487940, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430488299, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430488444, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430488539, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430489338, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430488516, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430489627, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430489819, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1750895430489930, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430490233, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430490394, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 29, "ts": 1750895430490945, "dur": 60, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430491297, "dur": 80471, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 29, "ts": 1750895430579345, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430579812, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580131, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580233, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580323, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580437, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580565, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430576036, "dur": 4887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430580923, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430581274, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430581347, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430581591, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1750895430581101, "dur": 3012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 29, "ts": 1750895430584113, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1750895430584217, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430458388, "dur": 15869, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430474277, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430474267, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_771434CFFF9D260C.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750895430474341, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430474441, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430474828, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750895430475174, "dur": 654, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1750895430475829, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430477205, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430478157, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430479136, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430480457, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430481389, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430482394, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430483526, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430484586, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430485511, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430486504, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430487245, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430487839, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430488320, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1750895430488724, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 30, "ts": 1750895430489214, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430489638, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430489757, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430489853, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430490268, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430490398, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430491132, "dur": 84989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430579813, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430580233, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430580437, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.1\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430576122, "dur": 4402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750895430580525, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430580850, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430581172, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430581462, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430581667, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 30, "ts": 1750895430580653, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 30, "ts": 1750895430583784, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430583857, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1750895430583956, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430458404, "dur": 15870, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430474292, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430474281, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_72EF51AA8584F47D.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750895430474402, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430474401, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_0A7BB341A7CF361B.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750895430474829, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475159, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430475271, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475501, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475604, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475725, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475882, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1750895430475991, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430477504, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430478579, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430479665, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430481040, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430482124, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430483395, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430484426, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430485580, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430486802, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430487238, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430487837, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430488314, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1750895430488492, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1750895430489360, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430489659, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430489835, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430490254, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430490380, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430491122, "dur": 84915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430577151, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430577271, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430578914, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430580130, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430580232, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430580487, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.11\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430576054, "dur": 4489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750895430580543, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430580946, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430581140, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430581355, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430581482, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430581592, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 31, "ts": 1750895430580690, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 31, "ts": 1750895430583496, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1750895430583596, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430458180, "dur": 15896, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430474096, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474086, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_BE5CE1BA3FC5C7DB.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474196, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430474268, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474267, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_EAFF22773CAE8FE7.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474502, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430474634, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474727, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430474815, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430474904, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475010, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475126, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475317, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475482, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475627, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475685, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430475879, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430475932, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14355918427286719942.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1750895430476049, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430477351, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430478406, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430479633, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430480872, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430481855, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430483096, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430484261, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430485174, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430486144, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430486921, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430487258, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430487860, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430488358, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430489378, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430489766, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430489828, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430490247, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430490373, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430491115, "dur": 42969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430534085, "dur": 42004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430577298, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430578473, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430579341, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430579812, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430580324, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.4\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430576099, "dur": 4377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750895430580477, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430581073, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430581274, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430581374, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430581460, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430581591, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.17f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 32, "ts": 1750895430580607, "dur": 3184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 32, "ts": 1750895430583792, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1750895430583928, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750895430589528, "dur": 2797, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 53624, "tid": 6, "ts": 1750895430607652, "dur": 1578, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 53624, "tid": 6, "ts": 1750895430609305, "dur": 1950, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 53624, "tid": 6, "ts": 1750895430602700, "dur": 9220, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
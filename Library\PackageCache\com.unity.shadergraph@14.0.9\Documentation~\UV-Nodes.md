# UV Nodes

| [Flipbook](Flipbook-Node.md) | [Polar Coordinates](Polar-Coordinates-Node.md) |
| :--------------- | :-------- |
| ![Image](images/FlipbookNodeThumb.png) | ![Image](images/PolarCoordinatesNodeThumb.png) |
| Creates a flipbook, or texture sheet animation, of the UVs supplied to input In. | Converts the value of input UV to polar coordinates. |
| [**Radial Shear**](Radial-Shear-Node.md) | [**Rotate**](Rotate-Node.md) |
| ![Image](images/RadialShearNodeThumb.png) | ![Image](images/RotateNodeThumb.png) |
| Applies a radial shear warping effect similar to a wave to the value of input UV. | Rotates the value of input UV around a reference point defined by input Center by the amount of input Rotation. |
| [**Spherize**](Spherize-Node.md) | [**Tiling and Offset**](Tiling-And-Offset-Node.md) |
| ![Image](images/SpherizeNodeThumb.png) | ![Image](images/TilingAndOffsetNodeThumb.png) |
| Applies a spherical warping effect similar to a fisheye camera lens to the value of input UV. | Tiles and offsets the value of input UV by the inputs Tiling and Offset respectively. |
| [**Triplanar**](Triplanar-Node.md) | [**Twirl**](Twirl-Node.md) |
| ![Image](images/TriplanarNodeThumb.png) | ![Image](images/TwirlNodeThumb.png) |
| A method of generating UVs and sampling a texture by projecting in world space. | Applies a twirl warping effect similar to a black hole to the value of input UV. |
